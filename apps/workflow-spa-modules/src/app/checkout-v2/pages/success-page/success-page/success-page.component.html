<apo-checkout-header></apo-checkout-header>

@if (orderInfo()) {
  <div [class.cross-sale]="orderInfo()?.crossSaleEnabled"
       class="success-page-content"
       data-test="success-page-main-wrapper">

    @if (isBankWirePayment) {
      <apo-bank-wire-header
        class="deferred-info"
        [userInfo]="orderInfo()?.userInfo"
        [userInfoUpdated]="userInfoUpdated()"
        (confirmUserInfo)="confirmUserInfo()"
        (updateUserInfo)="updateUserInfo($event)"
        data-test="success-page-bank-wire-header-component">
      </apo-bank-wire-header>

      @if (!orderInfo()?.crossSaleEnabled) {
        <apo-bank-wire-info
          class="bank-wire-bar"
          [orderPid]="orderInfo()?.orderPid"
          [totalPrice]="orderInfo()?.orderInfo.totalPrice"
          [currencyCode]="orderInfo()?.orderInfo.currencyCode"
          [bankDetails]="orderInfo()?.bankDetails"
          data-test="success-page-bank-wire-info-component">
        </apo-bank-wire-info>
      }
    }

    @if (isMultibancoPayment) {
      <apo-bank-wire-header
        class="deferred-info"
        [userInfo]="orderInfo()?.userInfo"
        [userInfoUpdated]="userInfoUpdated()"
        (confirmUserInfo)="confirmUserInfo()"
        (updateUserInfo)="updateUserInfo($event)"
        data-test="success-page-bank-wire-header-component">
      </apo-bank-wire-header>

      <apo-multibanco-info
        class="bank-wire-bar"
        [totalPrice]="orderInfo()?.orderInfo.totalPrice"
        [currencyCode]="orderInfo()?.orderInfo.currencyCode"
        [bankDetails]="orderInfo()?.paymentMethod"
        data-test="success-page-multibanco-info-component">
      </apo-multibanco-info>
    }

    @if (!isBankWirePayment && !isMultibancoPayment) {
      <apo-placed-order-info
        class="placed-order-info"
        [userInfo]="orderInfo()?.userInfo"
        [userInfoUpdated]="userInfoUpdated()"
        (confirmUserInfo)="confirmUserInfo()"
        (updateUserInfo)="updateUserInfo($event)"
        data-test="success-page-placed-order-info-component">
      </apo-placed-order-info>
    }

    <apo-common-order-info
      class="info-bar"
      [crossSaleEnabled]="orderInfo()?.crossSaleEnabled"
      [orderPid]="orderInfo()?.orderPid"
      [orderInfo]="orderInfo()?.orderInfo"
      [coupon]="orderInfo()?.coupon"
      [paymentMethod]="orderInfo()?.paymentMethod"
      [isAboMedsChooseModification]="orderInfo()?.isAboMedsChooseModification"
      [discounts]="orderInfo()?.discounts"
      data-test="success-page-common-order-info-component">
    </apo-common-order-info>

    @if (!isBankWirePayment && !isMultibancoPayment) {
      <apo-user-info
        class="user-bar"
        [isUserChangePasswordShown]="orderInfo()?.isNewCustomer"
        [shippingInfo]="orderInfo()?.shippingInfo"
        [userInfo]="orderInfo()?.userInfo"
        data-test="success-page-user-info-component">
      </apo-user-info>
    }

    <div class="buttons" data-test="success-page-buttons-wrapper">
      <button kit-button-primary kit-button-xxl
              (click)="onContinueShoppingClick(langCode)"
              data-analytics-click="continue_shopping"
              data-test="success-page-continue-shopping-button">
        {{ 'successPageV2.main.continueShopping' | translate }}
      </button>

      <button kit-button-secondary kit-button-xxl
              (click)="goToOrderHistory()"
              data-analytics-click="to_order_history"
              data-test="success-page-order-history-button">
        {{ 'successPageV2.main.orderHistory' | translate }}
      </button>
    </div>
  </div>
} @else {
  <apo-loading-spinner data-test="success-page-loading-spinner"></apo-loading-spinner>
}

@if (canSeeTSModal) {
  <div class="trusted-shops-container"
       (click)="onTSModalClick($event)"
       data-test="success-page-trusted-shops-container-wrapper"
       data-analytics-click="ts_modal">
    <div id="trustbadgeCustomCheckoutContainer">
      <i class="icon icon-xmark"></i>
    </div>
  </div>
}

<apo-checkout-footer></apo-checkout-footer>
