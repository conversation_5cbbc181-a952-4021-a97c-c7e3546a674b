@use 'scss/colors';
@use 'scss/mixins';

$max-width: 466px;
$min-width: 466px;
$max-width-mobile: 343px;
$min-width-mobile: 343px;

@mixin container {
  display: flex;
  max-width: $max-width;
  min-width: $min-width;

  @include mixins.small-tablet {
    max-width: min(#{$max-width-mobile}, calc(100vw - 32px));
    min-width: min(#{$min-width-mobile}, calc(100vw - 32px));
  }
}

@mixin bar {
  display: flex;
  flex-direction: column;
  color: colors.$dark-blue;
  border-radius: 8px;

  @include mixins.mobile {
  }
}

:host {
  min-height: 100vh;
  display: flex;
  flex-direction: column;

  .trusted-shops-container {
    position: absolute;
    height: 100%;
    width: 100%;
    top: 0;

    #trustbadgeCustomCheckoutContainer {
      z-index: 999;
      display: block;
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);

      i {
        position: absolute;
        z-index: 1;
        right: 15px;
        top: 15px;
        cursor: pointer;
      }
    }
  }
}

apo-checkout-footer {
  margin-top: auto;
}

.deferred-info,
.placed-order-info {
  @include container;
}

.bank-wire-bar,
.info-bar,
.user-bar {
  @include container;
  @include bar;
}

.buttons {
  margin: 32px 0 40px 0;

  @include mixins.small-tablet {
    margin: 24px 0 32px 0;
  }

  @include container;
  flex-direction: column;
  width: 100%;

  button {
    &:not(:first-child) {
      margin-top: 8px;
    }
  }
}

.success-page-content {
  display: flex;
  flex-direction: column;
  background: colors.$whisper;
  align-items: center;
}
