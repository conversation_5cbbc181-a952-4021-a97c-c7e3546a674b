import { Component, On<PERSON><PERSON>roy, OnInit, signal } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { finalize, filter, first, switchMap, takeUntil, tap } from 'rxjs/operators';
import { AbstractComponent } from '@workflow-spa/shared/helpers';
import { PaymentSystemName } from '@workflow-spa/shared/methods';
import { UserInfo } from '@workflow-spa/shared/models';
import { IEnvironment } from '@workflow-spa/shared/interfaces';
import { TrackerEventsEnum } from '../../../../../../../../shared/models/tracker-events.enum';
import { LanguageService, PasswordService } from '@workflow-spa/shared/services';
import { CommandError, CommandErrorCode } from '@workflow-spa/shared/commands';
import { SuccessOrderInfoModel } from '@workflow-spa/shared/models';
import { EnvironmentLoadService, RedditTrackingService } from '@workflow-spa/shared/services';
import { RequestPhoneModalComponent } from '../modals/request-phone-modal/request-phone-modal.component';
import { PackstationModalComponent } from '../modals/packstation-modal/packstation-modal.component';
import { RequestPhonePackstationModalComponent } from '../modals/request-phone-packstation-modal/request-phone-packstation-modal.component';
import { of } from 'rxjs';
import { VerificationModalComponent } from '../verification-modal/components/verification-modal/verification-modal.component';
import { VerificationModalService } from '../verification-modal/services/verification-modal.service';
import { VerificationModel } from '../verification-modal/models/verification.model';
import { QueryParamsHelpers } from '../../../../../../../../shared/helpers/query-params.helpers';
import { sdkLogHelper } from '../../../../../../../../shared/helpers/sdk-log.helper';
import { FaceBookEventEnum, facebookTrackEvent, prefixMap } from '../../../../../../../../shared/helpers/facebook.helper';
import { internalAnalyticsHelper } from '../../../../../../../../shared/helpers/internal-analytics.helper';
import { VerificationStatusEnum } from '../../../shared/enums/verification.status.enum';
import { TrustedShopsModel } from '../../../models/trusted-shops.model';
import { CheckoutRenderSuccessPageCommand } from '../../../commands/checkout-render-success-page.command';
import { BankWireHeaderComponent } from '../components/bank-wire-header/bank-wire-header.component';
import { BankWireInfoComponent } from '../components/bank-wire-info/bank-wire-info.component';
import { MultibancoInfoComponent } from '../components/multibanco-info/multibanco-info.component';
import { PlacedOrderInfoComponent } from '../components/placed-order-info/placed-order-info.component';
import { CommonOrderInfoComponent } from '../components/common-order-info/common-order-info.component';
import { UserInfoComponent } from '../components/user-info/user-info.component';
import { TranslatePipe } from '@ngx-translate/core';
import { SpinnerModule } from '@workflow-spa/shared/modules';
import { ButtonComponent, DialogRef, DialogService, ModalDefaultModule } from '@apo/ui-kit';
import { TrustedShopsService } from '../../../services/trusted-shops.service';
import { ExitIntentManagementService } from '../../../components/exit-intent-modal/services/exit-intent-management.service';
import { CheckoutFooterComponent } from '../../../layout/checkout-footer/checkout-footer.component';
import { CheckoutHeaderComponent } from '../../../layout/checkout-header/checkout-header.component';
import { CheckoutV2Selectors } from '../../../store';
import { Store } from '@ngxs/store';
import { CheckoutWorkflowHttpV2Service } from '../../../services/checkout-workflow-http-v2.service';

@Component({
  selector: 'apo-success-page',
  templateUrl: './success-page.component.html',
  styleUrls: ['./success-page.component.scss'],
  imports: [
    BankWireHeaderComponent,
    BankWireInfoComponent,
    MultibancoInfoComponent,
    PlacedOrderInfoComponent,
    CommonOrderInfoComponent,
    UserInfoComponent,
    TranslatePipe,
    SpinnerModule,
    ModalDefaultModule,
    ButtonComponent,
    CheckoutHeaderComponent,
    CheckoutFooterComponent,
  ],
  providers: [
    VerificationModalService,
  ],
})
export class SuccessPageComponent extends AbstractComponent implements OnInit, OnDestroy {
  PaymentSystemName = PaymentSystemName;

  readonly orderInfo = signal<SuccessOrderInfoModel | null>(null);
  readonly userInfoUpdated = signal<boolean>(false);
  readonly isLoading = signal<boolean>(true);
  readonly showTSModal = signal<boolean>(false);

  private verificationModal: DialogRef<VerificationModalComponent>;
  private dialogRef: DialogRef<PackstationModalComponent | RequestPhonePackstationModalComponent>;
  private readonly mobileViewWidth = 768;

  get isBankWirePayment(): boolean {
    return this.orderInfo()?.paymentMethod?.system_name === PaymentSystemName.offline;
  }
  get isMultibancoPayment(): boolean {
    return this.orderInfo()?.paymentMethod?.system_name === PaymentSystemName.paypalMultibanco;
  }
  get canSeeTSModal(): boolean {
    return (window.innerWidth <= this.mobileViewWidth) && this.showTSModal();
  }
  get baseUrl() { return location.origin; }
  get langCode() { return this.languageService.currentLanguage; }
  get currentParams() { return { ...QueryParamsHelpers.queryParams }; }
  get currentCommand(): CheckoutRenderSuccessPageCommand | null {
    return this.store.selectSnapshot(CheckoutV2Selectors.currentCommand) as CheckoutRenderSuccessPageCommand || null;
  }

  constructor(
    private route: ActivatedRoute,
    private promoService: ExitIntentManagementService,
    private languageService: LanguageService,
    private trustedShopsService: TrustedShopsService,
    private verificationService: VerificationModalService,
    private environmentLoadService: EnvironmentLoadService<IEnvironment>,
    private redditTrackingService: RedditTrackingService,
    private passwordService: PasswordService,
    private dialogService: DialogService,
    private store: Store,
    private checkoutWorkflowHttpV2Service: CheckoutWorkflowHttpV2Service,
  ) {
    super();
  }

  ngOnInit() {
    this.setLogs();
    this.initData$();
    this.initGoogleAnalytics();
    this.trackRedditPurchaseEvent();
  }

  private initData$() {
    this.route.queryParams.pipe(
      switchMap(params => this.checkoutWorkflowHttpV2Service.getOrderInfo(params['order_pid'])),
      first(),
      tap(info => {
        this.showInitialModal(info);
        this.handleTrustBadgeData(info);
      }),
      finalize(() => this.isLoading.set(false)),
    ).subscribe(
      info => {
        this.orderInfo.set(info);
        this.logFaceBook();
      },
      (error: CommandError) => {
        if (error.code === CommandErrorCode.OrderNotFound) {
          window.location.href = `${window.location.origin}/${this.languageService.currentLanguage}`;
        }
      },
    );

    this.promoService.destroyExitIntentLogicSubscription();
  }

  private trackRedditPurchaseEvent() {
    if (this.orderInfo()?.isNewCustomer) {
      this.redditTrackingService.trackEvent('Purchase');
    }
  }

  private initGoogleAnalytics() {
    // this.analyticsService.dispatchPurchase(this.commandData);
  }

  private setLogs() {
    sdkLogHelper(TrackerEventsEnum.thankYouPage, {
      category_id: this.currentCommand?.categoryId,
      query_params: window.location.search,
    });
  }

  goToOrderHistory() {
    sdkLogHelper(TrackerEventsEnum.thankYouPageOrderHistoryClicked, {
      category_id: this.currentCommand?.categoryId,
      query_params: window.location.search,
    });
    window.location.href = `${this.baseUrl}/${this.langCode}/account/order-history`;
  }

  private handleTrustBadgeData(info: SuccessOrderInfoModel) {
    const dialogClosed$ = this.dialogRef?.afterClosed() || of(null);

    dialogClosed$
      .pipe(
        switchMap(() => this.trustedShopsService.getTrustedShopsInfo()),
        tap((res: TrustedShopsModel) => {
          this.trustedShopsService.setOrderForTrustedShops({
            orderPid: info.orderPid,
            email: info.userInfo?.email,
            price: String(info.orderInfo.totalPrice),
            currencyCode: info.orderInfo.currencyCode,
            paymentMethod: info.paymentMethod.system_name,
          }, res.tsId);

          this.showTSModal.set(true);
        }),
        takeUntil(this.ngUnsubscribe$),
      )
      .subscribe();
  }

  ngOnDestroy() {
    this.trustedShopsService.removeCheckoutElement();
    this.dialogRef?.close();
    this.verificationModal?.close();
    super.ngOnDestroy();
  }

  private showInitialModal(info: SuccessOrderInfoModel) {
    if (this.currentParams['validation']) return this.checkVerification();

    if (info?.showPhoneNumberPopup && info?.showPackstationPopup) return this.showRequestPhonePackstationModal(info);
    if (info?.showPhoneNumberPopup) return this.showPhoneModal(info);
    if (info?.showPackstationPopup) return this.showPackstationModal();

    if (!info?.showPhoneNumberPopup && !info?.showPackstationPopup) return this.checkVerification();
  }

  private showPhoneModal(info: SuccessOrderInfoModel) {
    internalAnalyticsHelper('popUp', { element_name: 'request_phone_modal', value: 'shown' });

    this.dialogRef = this.dialogService.open(RequestPhoneModalComponent, { data: info?.orderPid });

    this.dialogRef.afterClosed()
      .pipe(
        tap(() => {
          internalAnalyticsHelper('popUp', {
            element_name: 'request_phone_modal',
            value: 'closed',
          });
        }),
        takeUntil(this.ngUnsubscribe$),
      )
      .subscribe(() => this.checkVerification());
  }

  onClose() {
    this.dialogRef.close();
  }

  private showPackstationModal() {
    this.dialogRef = this.dialogService.open(PackstationModalComponent);

    internalAnalyticsHelper('popUp', {
      element_name: 'packstation_modal',
      value: 'shown',
    });

    this.dialogRef.afterClosed()
      .pipe(
        tap(() => {
          internalAnalyticsHelper('popUp', {
            element_name: 'packstation_modal',
            value: 'closed',
          });
        }),
        takeUntil(this.ngUnsubscribe$),
      )
      .subscribe(() => this.checkVerification());
  }

  private showRequestPhonePackstationModal(info: SuccessOrderInfoModel) {
    this.dialogRef = this.dialogService.open(RequestPhonePackstationModalComponent, { data: info?.orderPid });

    internalAnalyticsHelper('popUp', {
      element_name: 'request_phone_packstation_modal',
      value: 'shown',
    });

    this.dialogRef.afterClosed()
      .pipe(
        tap(() => {
          internalAnalyticsHelper('popUp', {
            element_name: 'request_phone_packstation_modal',
            value: 'closed',
          });
        }),
        takeUntil(this.ngUnsubscribe$),
      )
      .subscribe(() => this.checkVerification());
  }

  private showVerificationModal(data: VerificationModel) {
    this.verificationModal = this.dialogService.open(VerificationModalComponent, { data });

    internalAnalyticsHelper('popUp', {
      element_name: 'verification_modal',
      value: 'shown',
    });

    this.verificationModal
      .afterClosed()
      .pipe(
        tap(() => {
          internalAnalyticsHelper('popUp', {
            element_name: 'verification_modal',
            value: 'closed',
          });
        }),
        takeUntil(this.ngUnsubscribe$),
      )
      .subscribe();
  }

  confirmUserInfo() {
    this.passwordService.confirmUserInfo()
      .pipe(takeUntil(this.ngUnsubscribe$))
      .subscribe(() => {
        this.userInfoUpdated.set(true);
        this.checkVerification();
      });
  }

  updateUserInfo(userInfo: UserInfo) {
    this.passwordService.updateUserInfo(userInfo)
      .pipe(takeUntil(this.ngUnsubscribe$))
      .subscribe(() => {
        this.userInfoUpdated.set(true);
        this.checkVerification();
      });
  }

  private checkVerification() {
    this.verificationService.checkVerification(this.currentParams).pipe(
      filter((res: VerificationModel) => res.validationRequired || !!this.currentParams['validation']),
      takeUntil(this.ngUnsubscribe$),
    ).subscribe(res => this.showVerificationModal(res));
  }

  onTSModalClick(event: Event) {
    this.showTSModal.set(
      !(event.target as Element).classList.contains('trusted-shops-container') &&
      !(event.target as Element).classList.contains('icon-xmark'),
    );
  }

  onContinueShoppingClick(langCode: string) {
    sdkLogHelper(TrackerEventsEnum.thankYouPageContinueShoppingClicked, {
      query_params: window.location.search,
    });

    window.location.href = `${window.location.origin}/${this.languageService.currentLanguage}`;
  }

  private logFaceBook() {
    const orderInfo = this.orderInfo();
    facebookTrackEvent(FaceBookEventEnum.purchase, `${prefixMap[FaceBookEventEnum.purchase]}_${orderInfo?.orderPid}`, {
      currency: this.currentCommand?.analyticData?.currency,
      value: this.currentCommand?.analyticData?.total_price,
    });

    facebookTrackEvent(FaceBookEventEnum.fbp, `${prefixMap[FaceBookEventEnum.fbp]}_${orderInfo?.orderPid}`, {
      currency: this.currentCommand?.analyticData?.currency,
      value: this.currentCommand?.analyticData?.total_price,
    });

    if (orderInfo?.subscriptionPlan) {
      facebookTrackEvent(FaceBookEventEnum.subscription, `${prefixMap[FaceBookEventEnum.subscription]}_${orderInfo?.orderPid}`, {
        currency: orderInfo?.currency,
        value: orderInfo?.totalPrice,
      });

      facebookTrackEvent(FaceBookEventEnum.fbs, `${prefixMap[FaceBookEventEnum.fbs]}_${orderInfo?.orderPid}`, {
        currency: orderInfo?.currency,
        value: orderInfo?.totalPrice,
      });
    }
  }
}
