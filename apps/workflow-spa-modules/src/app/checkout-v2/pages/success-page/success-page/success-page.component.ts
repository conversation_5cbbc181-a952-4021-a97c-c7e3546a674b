import {
  Component,
  OnDestroy,
  OnInit,
  signal,
  computed,
  Signal,
} from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { finalize, filter, first, switchMap, takeUntil, tap } from 'rxjs/operators';
import { AbstractComponent } from '@workflow-spa/shared/helpers';
import { PaymentSystemName } from '@workflow-spa/shared/methods';
import { UserInfo } from '@workflow-spa/shared/models';
import { TrackerEventsEnum } from '../../../../../../../../shared/models/tracker-events.enum';
import { LanguageService, PasswordService } from '@workflow-spa/shared/services';
import { CommandError, CommandErrorCode } from '@workflow-spa/shared/commands';
import { SuccessOrderInfoModel } from '@workflow-spa/shared/models';
import { RedditTrackingService } from '@workflow-spa/shared/services';
import { RequestPhoneModalComponent } from '../modals/request-phone-modal/request-phone-modal.component';
import { PackstationModalComponent } from '../modals/packstation-modal/packstation-modal.component';
import { RequestPhonePackstationModalComponent } from '../modals/request-phone-packstation-modal/request-phone-packstation-modal.component';
import { of } from 'rxjs';
import { VerificationModalComponent } from '../verification-modal/components/verification-modal/verification-modal.component';
import { VerificationModalService } from '../verification-modal/services/verification-modal.service';
import { VerificationModel } from '../verification-modal/models/verification.model';
import { QueryParamsHelpers } from '../../../../../../../../shared/helpers/query-params.helpers';
import { sdkLogHelper } from '../../../../../../../../shared/helpers/sdk-log.helper';
import { FaceBookEventEnum, facebookTrackEvent, prefixMap } from '../../../../../../../../shared/helpers/facebook.helper';
import { internalAnalyticsHelper } from '../../../../../../../../shared/helpers/internal-analytics.helper';
import { TrustedShopsModel } from '../../../models/trusted-shops.model';
import { CheckoutRenderSuccessPageCommand } from '../../../commands/checkout-render-success-page.command';
import { BankWireHeaderComponent } from '../components/bank-wire-header/bank-wire-header.component';
import { BankWireInfoComponent } from '../components/bank-wire-info/bank-wire-info.component';
import { MultibancoInfoComponent } from '../components/multibanco-info/multibanco-info.component';
import { PlacedOrderInfoComponent } from '../components/placed-order-info/placed-order-info.component';
import { CommonOrderInfoComponent } from '../components/common-order-info/common-order-info.component';
import { UserInfoComponent } from '../components/user-info/user-info.component';
import { TranslatePipe } from '@ngx-translate/core';
import { SpinnerModule } from '@workflow-spa/shared/modules';
import { ButtonComponent, DialogRef, DialogService, ModalDefaultModule } from '@apo/ui-kit';
import { TrustedShopsService } from '../../../services/trusted-shops.service';
import { ExitIntentManagementService } from '../../../components/exit-intent-modal/services/exit-intent-management.service';
import { CheckoutFooterComponent } from '../../../layout/checkout-footer/checkout-footer.component';
import { CheckoutHeaderComponent } from '../../../layout/checkout-header/checkout-header.component';
import { CheckoutV2Selectors } from '../../../store';
import { select } from '@ngxs/store';
import { CheckoutWorkflowHttpV2Service } from '../../../services/checkout-workflow-http-v2.service';

@Component({
  selector: 'apo-success-page',
  templateUrl: './success-page.component.html',
  styleUrls: ['./success-page.component.scss'],
  imports: [
    BankWireHeaderComponent,
    BankWireInfoComponent,
    MultibancoInfoComponent,
    PlacedOrderInfoComponent,
    CommonOrderInfoComponent,
    UserInfoComponent,
    TranslatePipe,
    SpinnerModule,
    ModalDefaultModule,
    ButtonComponent,
    CheckoutHeaderComponent,
    CheckoutFooterComponent,
  ],
  providers: [
    VerificationModalService,
  ],
})
export class SuccessPageComponent extends AbstractComponent implements OnInit, OnDestroy {
  PaymentSystemName = PaymentSystemName;

  readonly orderInfo = signal<SuccessOrderInfoModel | null>(null);
  readonly userInfoUpdated = signal<boolean>(false);
  readonly isLoading = signal<boolean>(true);
  readonly showTSModal = signal<boolean>(false);

  readonly isBankWirePayment = computed(() =>
    this.orderInfo()?.paymentMethod?.system_name === PaymentSystemName.offline,
  );

  readonly isMultibancoPayment = computed(() =>
    this.orderInfo()?.paymentMethod?.system_name === PaymentSystemName.paypalMultibanco,
  );

  readonly canSeeTSModal = computed(() =>
    (window.innerWidth <= this.mobileViewWidth) && this.showTSModal(),
  );

  readonly currentCommand = select(CheckoutV2Selectors.currentCommand) as Signal<CheckoutRenderSuccessPageCommand | null>;

  private verificationModal: DialogRef<VerificationModalComponent>;
  private dialogRef: DialogRef<PackstationModalComponent | RequestPhonePackstationModalComponent>;
  private readonly mobileViewWidth = 768;

  get baseUrl() { return location.origin; }
  get langCode() { return this.languageService.currentLanguage; }
  get currentParams() { return { ...QueryParamsHelpers.queryParams }; }

  constructor(
    private route: ActivatedRoute,
    private promoService: ExitIntentManagementService,
    private languageService: LanguageService,
    private trustedShopsService: TrustedShopsService,
    private verificationService: VerificationModalService,
    private redditTrackingService: RedditTrackingService,
    private passwordService: PasswordService,
    private dialogService: DialogService,
    private checkoutWorkflowHttpV2Service: CheckoutWorkflowHttpV2Service,
  ) {
    super();
  }

  ngOnInit() {
    this.trackEvent(TrackerEventsEnum.thankYouPage);
    this.initData$();
    this.initGoogleAnalytics();
    this.trackRedditPurchaseEvent();
  }

  private initData$() {
    this.route.queryParams.pipe(
      switchMap(params => this.checkoutWorkflowHttpV2Service.getOrderInfo(params['order_pid'])),
      first(),
      tap(info => {
        this.showInitialModal(info);
        this.handleTrustBadgeData(info);
      }),
      finalize(() => this.isLoading.set(false)),
    ).subscribe(
      info => {
        this.orderInfo.set(info);
        this.logFaceBook();
      },
      (error: CommandError) => {
        if (error.code === CommandErrorCode.OrderNotFound) {
          window.location.href = `${window.location.origin}/${this.languageService.currentLanguage}`;
        }
      },
    );

    this.promoService.destroyExitIntentLogicSubscription();
  }

  private trackRedditPurchaseEvent() {
    if (this.orderInfo()?.isNewCustomer) {
      this.redditTrackingService.trackEvent('Purchase');
    }
  }

  private initGoogleAnalytics() {
    // this.analyticsService.dispatchPurchase(this.commandData);
  }

  goToOrderHistory() {
    this.trackEvent(TrackerEventsEnum.thankYouPageOrderHistoryClicked);
    window.location.href = `${this.baseUrl}/${this.langCode}/account/order-history`;
  }

  private trackEvent(event: TrackerEventsEnum, additionalContext?: any) {
    const currentCmd = this.currentCommand();
    sdkLogHelper(event, {
      category_id: currentCmd?.categoryId,
      query_params: window.location.search,
      ...additionalContext,
    });
  }


  private openModalWithVerification<T>(
    component: any,
    modalName: string,
    config?: any,
  ): DialogRef<T> {
    internalAnalyticsHelper('popUp', { element_name: modalName, value: 'shown' });

    const dialogRef = this.dialogService.open(component, config);

    // Handle modal closed with analytics and verification check
    dialogRef.afterClosed()
      .pipe(
        tap(() => {
          internalAnalyticsHelper('popUp', {
            element_name: modalName,
            value: 'closed',
          });
        }),
        takeUntil(this.ngUnsubscribe$),
      )
      .subscribe(() => this.checkVerification());

    return dialogRef;
  }

  private handleTrustBadgeData(info: SuccessOrderInfoModel) {
    const dialogClosed$ = this.dialogRef?.afterClosed() || of(null);

    dialogClosed$
      .pipe(
        switchMap(() => this.trustedShopsService.getTrustedShopsInfo()),
        tap((res: TrustedShopsModel) => {
          this.trustedShopsService.setOrderForTrustedShops({
            orderPid: info.orderPid,
            email: info.userInfo?.email,
            price: String(info.orderInfo.totalPrice),
            currencyCode: info.orderInfo.currencyCode,
            paymentMethod: info.paymentMethod.system_name,
          }, res.tsId);

          this.showTSModal.set(true);
        }),
        takeUntil(this.ngUnsubscribe$),
      )
      .subscribe();
  }

  ngOnDestroy() {
    this.trustedShopsService.removeCheckoutElement();
    this.dialogRef?.close();
    this.verificationModal?.close();
    super.ngOnDestroy();
  }

  private showInitialModal(info: SuccessOrderInfoModel) {
    if (this.currentParams['validation']) return this.checkVerification();

    if (info?.showPhoneNumberPopup && info?.showPackstationPopup) return this.showRequestPhonePackstationModal(info);
    if (info?.showPhoneNumberPopup) return this.showPhoneModal(info);
    if (info?.showPackstationPopup) return this.showPackstationModal();

    if (!info?.showPhoneNumberPopup && !info?.showPackstationPopup) return this.checkVerification();
  }

  private showPhoneModal(info: SuccessOrderInfoModel) {
    this.dialogRef = this.openModalWithVerification(
      RequestPhoneModalComponent,
      'request_phone_modal',
      { data: info?.orderPid },
    );
  }

  onClose() {
    this.dialogRef.close();
  }

  private showPackstationModal() {
    this.dialogRef = this.openModalWithVerification(
      PackstationModalComponent,
      'packstation_modal',
    );
  }

  private showRequestPhonePackstationModal(info: SuccessOrderInfoModel) {
    this.dialogRef = this.openModalWithVerification(
      RequestPhonePackstationModalComponent,
      'request_phone_packstation_modal',
      { data: info?.orderPid },
    );
  }

  private showVerificationModal(data: VerificationModel) {
    this.verificationModal = this.dialogService.open(VerificationModalComponent, { data });

    internalAnalyticsHelper('popUp', {
      element_name: 'verification_modal',
      value: 'shown',
    });

    this.verificationModal
      .afterClosed()
      .pipe(
        tap(() => {
          internalAnalyticsHelper('popUp', {
            element_name: 'verification_modal',
            value: 'closed',
          });
        }),
        takeUntil(this.ngUnsubscribe$),
      )
      .subscribe();
  }

  confirmUserInfo() {
    this.passwordService.confirmUserInfo()
      .pipe(takeUntil(this.ngUnsubscribe$))
      .subscribe(() => {
        this.userInfoUpdated.set(true);
        this.checkVerification();
      });
  }

  updateUserInfo(userInfo: UserInfo) {
    this.passwordService.updateUserInfo(userInfo)
      .pipe(takeUntil(this.ngUnsubscribe$))
      .subscribe(() => {
        this.userInfoUpdated.set(true);
        this.checkVerification();
      });
  }

  private checkVerification() {
    this.verificationService.checkVerification(this.currentParams).pipe(
      filter((res: VerificationModel) => res.validationRequired || !!this.currentParams['validation']),
      takeUntil(this.ngUnsubscribe$),
    ).subscribe(res => this.showVerificationModal(res));
  }

  onTSModalClick(event: Event) {
    this.showTSModal.set(
      !(event.target as Element).classList.contains('trusted-shops-container') &&
      !(event.target as Element).classList.contains('icon-xmark'),
    );
  }

  onContinueShoppingClick(langCode: string) {
    this.trackEvent(TrackerEventsEnum.thankYouPageContinueShoppingClicked);
    window.location.href = `${window.location.origin}/${this.languageService.currentLanguage}`;
  }

  private logFaceBook() {
    const orderInfo = this.orderInfo();
    const currentCmd = this.currentCommand();

    if (!orderInfo) return;

    // Track purchase events
    this.trackFacebookEvent(FaceBookEventEnum.purchase, orderInfo.orderPid, {
      currency: currentCmd?.analyticData?.currency,
      value: currentCmd?.analyticData?.total_price,
    });

    this.trackFacebookEvent(FaceBookEventEnum.fbp, orderInfo.orderPid, {
      currency: currentCmd?.analyticData?.currency,
      value: currentCmd?.analyticData?.total_price,
    });

    // Track subscription events if applicable
    if (orderInfo.subscriptionPlan) {
      this.trackFacebookEvent(FaceBookEventEnum.subscription, orderInfo.orderPid, {
        currency: orderInfo.currency,
        value: orderInfo.totalPrice,
      });

      this.trackFacebookEvent(FaceBookEventEnum.fbs, orderInfo.orderPid, {
        currency: orderInfo.currency,
        value: orderInfo.totalPrice,
      });
    }
  }

  /**
   * Centralized Facebook event tracking
   */
  private trackFacebookEvent(event: FaceBookEventEnum, orderPid: string, data: any) {
    facebookTrackEvent(event, `${prefixMap[event]}_${orderPid}`, data);
  }
}
