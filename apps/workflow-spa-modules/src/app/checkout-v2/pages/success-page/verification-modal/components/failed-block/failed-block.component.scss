@use 'scss/colors';

:host {
  display: block;
  max-width: 460px;
  width: 100%;
}

.failed-block {
  &__top {
    display: flex;
    align-items: center;
    padding: 16px 48px 16px 24px;
    background: colors.$light-gray;
    width: 100%;

    img {
      width: 24px;
      margin-right: 5px;
    }

    h2 {
      margin: 0;
      font-size: 18px;
      line-height: 21px;
      font-weight: 500;
    }
  }

  &__content {
    padding: 32px 0 40px;
    font-size: 16px;
    line-height: 24px;
    font-weight: 400;

    ::ng-deep b {
      display: inline-block;
      font-weight: 500;
      margin-bottom: 8px;
    }
  }
}
