@use 'scss/colors';
@use 'scss/mixins';

:host {
  min-width: 460px;

  @include mixins.small-tablet() {
    min-width: 343px;
  }
}

kit-modal-default-header {
  span {
    color: colors.$dark-blue;
    font-family: var(--kit-font-family);
    font-size: var(--kit-typography-headings-font-size-md);
    font-weight: 500;
    line-height: var(--kit-typography-headings-line-height-md);
  }
}

.modal {
  display: flex;
  border-radius: 10px;
  border: 1px solid colors.$live-chat-button-bg-color;
  position: relative;
  overflow: hidden;
  color: colors.$dark-blue;

  @include mixins.small-tablet {
    flex-direction: column;
    height: auto;
    overflow: visible;
  }

  .cross {
    width: 24px;
    cursor: pointer;
    position: absolute;
    right: 24px;
    top: 16px;
    z-index: 20;
  }
}
