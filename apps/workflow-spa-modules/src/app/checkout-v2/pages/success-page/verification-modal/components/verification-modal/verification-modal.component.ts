import { Component, HostListener, Inject, OnInit, signal } from '@angular/core';
import { VerificationStatusEnum } from '../../../../../shared/enums/verification.status.enum';
import { AbstractComponent } from '@workflow-spa/shared/helpers';
import { VerificationModel } from '../../models/verification.model';
import { VerificationModalService } from '../../services/verification-modal.service';
import { finalize, switchMap, takeUntil, tap } from 'rxjs/operators';
import { timer } from 'rxjs';
import { QueryParamsHelpers } from '../../../../../../../../../../shared/helpers/query-params.helpers';
import { sdkLogHelper } from '../../../../../../../../../../shared/helpers/sdk-log.helper';
import { TrackerEventsEnum } from '../../../../../../../../../../shared/models/tracker-events.enum';
import { InProgressBlockComponent } from '../in-progress-block/in-progress-block.component';
import { SuccessBlockComponent } from '../success-block/success-block.component';
import { FailedBlockComponent } from '../failed-block/failed-block.component';
import { VerificationBlockComponent } from '../verification-block/verification-block.component';
import { LazyLoadingModule } from '@workflow-spa/shared/modules';
import { DIALOG_DATA, DialogRef, ModalDefaultModule } from '@apo/ui-kit';
import { TranslatePipe } from '@ngx-translate/core';
import { internalAnalyticsHelper } from '../../../../../../../../../../shared/helpers/internal-analytics.helper';

@Component({
  selector: 'apo-verification-modal',
  templateUrl: 'verification-modal.component.html',
  styleUrls: ['verification-modal.component.scss'],
  imports: [
    InProgressBlockComponent,
    SuccessBlockComponent,
    FailedBlockComponent,
    VerificationBlockComponent,
    LazyLoadingModule,
    TranslatePipe,
    ModalDefaultModule,
  ],
  providers: [
    VerificationModalService,
  ],
})
export class VerificationModalComponent extends AbstractComponent implements OnInit {
  private readonly redirectToIdMerit: string = 'redirectToIdMerit';

  verificationStatusEnum = VerificationStatusEnum;
  readonly status = signal<VerificationStatusEnum>(this.data?.status);

  @HostListener('window:beforeunload', ['$event']) beforeUnloadHandler(e: Event) {
    if (this.isRedirectToIdMerit) {
      return;
    }

    this.verificationService.sendVerificationEmail()
      .pipe(takeUntil(this.ngUnsubscribe$)).subscribe();
  }

  get currentParams() {
    return { ...QueryParamsHelpers.queryParams };
  }

  get qrCodeUrl(): string {
    return `data:image/png;base64,${this.data.qrCode}`;
  }

  get redirectLink(): string {
    return this.data.verificationLink;
  }

  get isInProgress(): boolean {
    return this.status() === this.verificationStatusEnum.inProgress
      || this.status() === this.verificationStatusEnum.notStarted;
  }

  get isVerified(): boolean {
    return this.status() === this.verificationStatusEnum.verified;
  }

  get isFailed(): boolean {
    return this.status() === this.verificationStatusEnum.failed;
  }

  get isStarted(): boolean {
    return this.status() === this.verificationStatusEnum.started;
  }

  get isRedirectToIdMerit() {
    return window.sessionStorage.getItem(this.redirectToIdMerit);
  }

  constructor(
    private dialogRef: DialogRef<VerificationModalComponent>,
    @Inject(DIALOG_DATA) public data: VerificationModel,
    private verificationService: VerificationModalService,
  ) {
    super();
  }

  ngOnInit() {
    this.startVerificationPolling();
  }

  private startVerificationPolling() {
    if (this.isVerified || this.isFailed) {
      return;
    }

    timer(1000, 5000)
      .pipe(
        switchMap(() => this.verificationService.checkVerificationStatus(this.currentParams)),
        tap((res: VerificationModel) => this.status.set(res.status)),
        takeUntil(this.ngUnsubscribe$),
      ).subscribe();
  }

  startVerification() {
    window.sessionStorage.setItem(this.redirectToIdMerit, 'true');
    sdkLogHelper(TrackerEventsEnum.verificationModalVerificationStarted, {
      query_params: window.location.search,
    });
    window.location.href = this.redirectLink;
  }

  setImage(status: VerificationStatusEnum): string {
    switch (status) {
      case VerificationStatusEnum.verified:
        return 'assets/img/verify_success.svg';
      case VerificationStatusEnum.failed:
        return 'assets/img/verify_fail.svg';
      default:
        return 'assets/img/verify.svg';
    }
  }

  onClose() {
    sdkLogHelper(TrackerEventsEnum.verificationModalClosed, {
      query_params: window.location.search,
    });

    internalAnalyticsHelper('syntheticClick', {element: 'close_verification_modal'})

    if (this.isInProgress || this.isStarted) {
      this.verificationService.sendVerificationEmail()
        .pipe(
          takeUntil(this.ngUnsubscribe$),
          finalize(() => this.close()),
        ).subscribe();
    } else {
      this.close();
    }
  }

  private close() {
    this.dialogRef.close();
  }
}
