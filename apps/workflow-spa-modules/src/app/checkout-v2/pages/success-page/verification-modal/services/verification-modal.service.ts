import { Injectable } from '@angular/core';
import { map } from 'rxjs/operators';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { VerificationModel } from '../models/verification.model';
import { VerificationDto } from '../models/verification.dto';

@Injectable()
export class VerificationModalService {
  baseUrl = '/workflow/public';

  constructor(
    private http: HttpClient,
  ) {}

  checkVerification(params): Observable<VerificationModel> {
    return this.http.get<VerificationDto>(`${this.baseUrl}/customer-validate`, {params})
      .pipe(map(dto => new VerificationModel(dto)));
  }

  checkVerificationStatus(params): Observable<VerificationModel> {
    return this.http.get<VerificationDto>(`${this.baseUrl}/customer-validate/status?smart_token=${params['smart_token']}`)
      .pipe(map(dto => new VerificationModel(dto)));
  }

  sendVerificationEmail(): Observable<string> {
    return this.http.post<string>(`${this.baseUrl}/customer-validate/email`, {})
  }
}
