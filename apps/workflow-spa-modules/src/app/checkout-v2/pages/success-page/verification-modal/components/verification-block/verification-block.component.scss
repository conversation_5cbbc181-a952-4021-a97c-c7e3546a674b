@use 'scss/colors';
@use 'scss/mixins';

::ng-deep {
  .modern-panel--large {
    .mat-mdc-dialog-container {
      @include mixins.small-mobile-height() {
        max-height: calc(90vh - 60px);
      }
    }
  }
}

:host {
  display: block;
  max-width: 720px;
  width: 100%;
  min-width: 460px;

  @include mixins.small-tablet() {
    min-width: 343px;
  }
}

h2 {
  margin: 0;
  padding: 16px 48px 16px 24px;
  font-size: 18px;
  line-height: 21px;
  font-weight: 500;
  background: colors.$light-gray;
}

.content {
  position: relative;
  padding: 32px 0 40px;

  @include mixins.mobile() {
    padding: 16px 24px;
  }
}

.warning_text {
  max-width: 504px;
  width: 100%;
  margin: 0 0 24px;
  font-size: 14px;
  line-height: 20px;
  font-weight: 500;

  @include mixins.mobile() {
    margin: 0 0 16px;
  }
}

.row {
  display: flex;
  justify-content: space-between;

  .column {
    display: flex;
    flex-direction: column;
    max-width: 324px;
    width: 100%;

    &:first-child {
      margin-right: 24px;
    }

    &_title {
      margin: 0 0 8px;
      font-size: 14px;
      line-height: 20px;
      font-weight: 500;
    }

    .items {
      .item {
        display: flex;
        color: colors.$dim-gray;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 24px;

          @include mixins.mobile() {
            margin-bottom: 16px;
          }
        }

        span {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 16px;
          height: 16px;
          flex: 0 0 auto;
          margin-right: 8px;
          background: colors.$tropical-blue;
          color: colors.$dark-blue;
          border-radius: 50%;
          font-size: 9px;
          line-height: 0;

          &.check {
            background: url('/assets/icons/verification-check.svg');
          }
        }

        p {
          margin: 0;
          font-size: 14px;
          line-height: 20px;

          ::ng-deep b {
            font-weight: 400;
            color: colors.$dark-blue;
          }
        }
      }
    }
  }
}

.icons {
  display: flex;
  justify-content: space-between;
  margin-top: auto;

  img {
    display: block;
    max-width: 98px;
    max-height: 51px;

    @include mixins.mobile() {
      max-width: 74px;
      max-height: 40px;
    }
  }
}

.verify {
  @include mixins.mobile() {
    display: none;
  }

  .scan-code {
    margin-bottom: 8px;

    p {
      margin: 0;
      font-size: 14px;
      line-height: 20px;
      color: colors.$dim-gray;
    }
  }

  .qr-code {
    width: 125px;
    height: 125px;
    min-height: 125px;
    min-width: 125px;
    overflow: hidden;

    img {
      width: calc(100% + 40px);
      margin: -20px;
    }
  }

  &-mob {
    display: none;

    @include mixins.mobile() {
      position: sticky;
      bottom: 0;
      left: 0;
      display: block;
      padding: 16px 0;
    }

    button {
      &.verification {
        width: 100%;
        height: 48px;
        font-weight: 500;
        font-size: 14px;
        padding: 0 20px;
        text-transform: initial;
        letter-spacing: 0;
        background: colors.$default-orange;
        border-radius: 8px;

        &:after {
          content: '';
          width: 16px;
          height: 16px;
          right: 20px;
          position: absolute;
          background: url(../../../../../../../assets/img/vector-btn.svg) no-repeat;
        }
      }
    }
  }
}
