<kit-modal-default-header>
  @if (isInProgress || (isRedirectToIdMerit && isStarted)) {
    <span data-test="verification-modal-header-title">{{ 'successPageV2.main.verificationModal.titleText' | translate }}</span>
  }
</kit-modal-default-header>

<kit-modal-default-content>
  @if (isInProgress || (isRedirectToIdMerit && isStarted)) {
    <apo-verification-block
      [qrCodeUrl]="qrCodeUrl"
      (startVerification)="startVerification()"
      data-test="verification-modal-verification-block-component"
    ></apo-verification-block>
  }

  @if (isStarted && !isRedirectToIdMerit) {
    <apo-in-progress-block
      [qrCodeUrl]="qrCodeUrl"
      data-test="verification-modal-in-progress-block-component"
    ></apo-in-progress-block>
  }

  @if (isVerified) {
    <apo-success-block data-test="verification-modal-success-block-component"></apo-success-block>
  }

  @if (isFailed) {
    <apo-failed-block data-test="verification-modal-failed-block-component"></apo-failed-block>
  }
</kit-modal-default-content>
