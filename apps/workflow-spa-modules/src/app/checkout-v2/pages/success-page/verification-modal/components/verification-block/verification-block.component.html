<div class="content" data-test="verification-block-main-wrapper">
  <div class="warning_text" [innerHTML]="'successPageV2.main.verificationModal.warningText' | translate"
       data-test="verification-block-warning-text"></div>

  <div class="row" data-test="verification-block-row-wrapper">
    <div class="column">
      <div class="column_title" [innerHTML]="'successPageV2.main.verificationModal.rulesTitle' | translate"
           data-test="verification-block-rules-title"></div>

      <div class="items" data-test="verification-block-rules-items-wrapper">
        @for (rule of ('successPageV2.main.verificationModal.rules.items' | translate); track $index) {
          <div class="item" [attr.data-test]="'verification-block-rule-item-' + $index">
            <span [attr.data-test]="'verification-block-rule-index-' + $index">{{ $index + 1 }}</span>
            <p [innerHTML]="rule.item" [attr.data-test]="'verification-block-rule-text-' + $index"></p>
          </div>
        }
      </div>

      <div class="verify" data-test="verification-block-verify-wrapper">
        <div class="scan-code" data-test="verification-block-scan-code-wrapper">
          <p data-test="verification-block-scan-code-text">
            {{ 'successPageV2.main.verificationModal.scanCodeText' | translate }}
          </p>
        </div>

        <div class="qr-code" data-test="verification-block-qr-code-wrapper">
          <img [src]="qrCodeUrl()" data-test="verification-block-qr-code-image" />
        </div>
      </div>
    </div>

    <div class="column">
      <div class="column_title" [innerHTML]="'successPageV2.main.verificationModal.reasonsTitle' | translate"
           data-test="verification-block-reasons-title"></div>

      <div class="items" data-test="verification-block-reasons-items-wrapper">
        @for (reason of ('successPageV2.main.verificationModal.reasons.items' | translate); track $index) {
          <div class="item" [attr.data-test]="'verification-block-reason-item-' + $index">
            <span class="check" [attr.data-test]="'verification-block-reason-check-' + $index"></span>
            <p [innerHTML]="reason.item" [attr.data-test]="'verification-block-reason-text-' + $index"></p>
          </div>
        }
      </div>

      <div class="icons" data-test="verification-block-certificates-icons-wrapper">
        @for (icon of ('successPageV2.main.verificationModal.certificatesIcons.items' | translate); track $index) {
          <img [apoLazyLoad]="icon.item.src"
               [attr.data-test]="'verification-block-certificate-icon-' + $index" />
        }
      </div>
    </div>
  </div>

  <div class="verify-mob" data-test="verification-block-verify-mob-wrapper">
    <button
      class="verification button"
      (click)="startVerification.emit()"
      data-analytics-click="start_verification"
      data-test="verification-block-start-verification-button">
      {{ 'successPageV2.main.verificationModal.startVerification' | translate }}
    </button>
  </div>
</div>
