import { Component, EventEmitter, Output, input } from '@angular/core';
import { TranslatePipe } from '@ngx-translate/core';
import { LazyLoadingModule } from '@workflow-spa/shared/modules';

@Component({
  selector: 'apo-verification-block',
  templateUrl: './verification-block.component.html',
  styleUrls: ['./verification-block.component.scss'],
  imports: [
    TranslatePipe,
    LazyLoadingModule,
  ],
})
export class VerificationBlockComponent {
  readonly qrCodeUrl = input<string>('');
  @Output() startVerification = new EventEmitter<void>();
}
