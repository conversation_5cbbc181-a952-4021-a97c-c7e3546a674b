import { Component, input } from '@angular/core';
import { TranslatePipe } from '@ngx-translate/core';
import { SpinnerModule } from '@workflow-spa/shared/modules';

@Component({
  selector: 'apo-in-progress-block',
  templateUrl: './in-progress-block.component.html',
  styleUrls: ['./in-progress-block.component.scss'],
  imports: [
    TranslatePipe,
    SpinnerModule,
  ],
})
export class InProgressBlockComponent {
  readonly qrCodeUrl = input<string>('');
}
