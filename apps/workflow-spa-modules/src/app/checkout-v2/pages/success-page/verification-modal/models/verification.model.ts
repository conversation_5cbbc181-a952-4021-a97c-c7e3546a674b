import { VerificationStatusEnum } from '../../../../shared/enums/verification.status.enum';
import { VerificationDto } from './verification.dto';

export class VerificationModel {
  qrCode: string;
  status: VerificationStatusEnum;
  verificationLink: string;
  errorMessage: string;
  validationRequired: boolean;

  constructor(dto: VerificationDto) {
    this.qrCode = dto.qr_code;
    this.status = dto.status;
    this.verificationLink = dto.verification_link;
    this.errorMessage = dto.error_message;
    this.validationRequired = dto.validation_required;
  }
}
