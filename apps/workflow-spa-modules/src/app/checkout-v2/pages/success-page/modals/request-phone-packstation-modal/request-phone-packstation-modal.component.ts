import { Component, Inject, signal } from '@angular/core';
import { RequestPhoneModalHttpService } from '../services/request-phone-modal.http.service';
import { OrderPid } from '../../../../../shared/models/order-pid';
import { catchError, delay, takeUntil, tap } from 'rxjs/operators';
import { HttpErrorResponse } from '@angular/common/http';
import { throwError } from 'rxjs';
import { TrackerEventsEnum } from '../../../../../../../../../shared/models/tracker-events.enum';
import { AbstractComponent } from '@workflow-spa/shared/helpers';
import { sdkLogHelper } from '../../../../../../../../../shared/helpers/sdk-log.helper';
import { remapArrayErrors } from '@workflow-spa/shared/helpers/remap-errors.helper';
import { requestPhoneV2ModalErrorsMap } from '@workflow-spa/shared/consts/errors-map-v2.const';
import { internalAnalyticsHelper } from '../../../../../../../../../shared/helpers/internal-analytics.helper';
import { TranslatePipe } from '@ngx-translate/core';
import { AddPhoneNumberComponent } from '../../components/add-phone-number/add-phone-number.component';
import { DIALOG_DATA, DialogRef, ModalDefaultModule } from '@apo/ui-kit';

@Component({
  selector: 'apo-packstation-modal',
  templateUrl: './request-phone-packstation-modal.component.html',
  styleUrls: ['./request-phone-packstation-modal.component.scss'],
  imports: [
    TranslatePipe,
    AddPhoneNumberComponent,
    ModalDefaultModule,
  ],
})
export class RequestPhonePackstationModalComponent extends AbstractComponent {
  readonly errorMessage = signal<string>('');
  readonly isThankYouShown = signal<boolean>(false);
  readonly isStepTwoShown = signal<boolean>(false);

  constructor(
    private dialogRef: DialogRef<RequestPhonePackstationModalComponent>,
    private requestPhoneModalHttpService: RequestPhoneModalHttpService,
    @Inject(DIALOG_DATA) public orderPid: OrderPid,
  ) {
    super();
  }

  addPhone(phoneNumber: string) {
    this.requestPhoneModalHttpService.addPhone(this.orderPid, phoneNumber)
      .pipe(
        catchError((err: HttpErrorResponse) => {
          const errors = remapArrayErrors(
            requestPhoneV2ModalErrorsMap,
            err?.error?.map((err: any) => err.message)
          );
          this.errorMessage.set(errors[0] ?? '');

          internalAnalyticsHelper('errorEvent', {
            type: 'validation',
            value: this.errorMessage(),
          });

          return throwError(() => err);
        }),
        tap(() => {
          this.isThankYouShown.set(true);
          sdkLogHelper(TrackerEventsEnum.successPagePhoneAdded, { order_pid: this.orderPid });
        }),
        delay(2000),
        tap(() => this.isStepTwoShown.set(true)),
        takeUntil(this.ngUnsubscribe$),
      )
      .subscribe();
  }

  private close(isSaved: boolean) {
    this.dialogRef.close(isSaved);
  }

  onClose() {
    internalAnalyticsHelper('syntheticClick', { element: 'close_request_phone_packstation_modal' });

    if (this.isStepTwoShown()) {
      this.close(false);
      return;
    }

    sdkLogHelper(TrackerEventsEnum.successPageModalCloseWithoutPhone, { order_pid: this.orderPid });
    this.isStepTwoShown.set(true);
  }
}
