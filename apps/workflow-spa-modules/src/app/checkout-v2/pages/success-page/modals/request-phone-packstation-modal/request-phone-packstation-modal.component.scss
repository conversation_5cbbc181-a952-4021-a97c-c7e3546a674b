@use 'scss/variables';
@use 'scss/colors';
@use 'scss/mixins';

:host {
  min-width: 460px;

  @include mixins.small-tablet() {
    min-width: 343px;
  }
}

kit-modal-default-header {
  span {
    color: colors.$dark-blue;
    font-family: var(--kit-font-family);
    font-size: var(--kit-typography-headings-font-size-md);
    font-weight: 500;
    line-height: var(--kit-typography-headings-line-height-md);
  }
}

.modal {
  position: relative;
  overflow: hidden;
  padding: 60px 60px 196px;
  background: url(../../../../../../assets/img/postnumber-with-phone-modal-top-bg.svg) right top,
  url(../../../../../../assets/img/postnumber-with-phone-modal-bottom-bg.svg) center bottom;
  background-repeat: no-repeat;
  text-align: center;
  min-height: 505px;

  &--step-two {
    background: url(../../../../../../assets/img/postnumber-with-phone-modal-top-bg.svg) no-repeat right top;
    padding-bottom: 55px;
  }

  @include mixins.mobile {
    padding: 32px 25px 135px;
    margin-bottom: 32px;
    background-image: url(../../../../../../assets/img/postnumber-with-phone-modal-top-bg-mob.svg),
    url(../../../../../../assets/img/postnumber-with-phone-modal-bottom-bg-mob.svg);
    min-height: 480px;

    &--step-two {
      background: url(../../../../../../assets/img/postnumber-with-phone-modal-top-bg-mob.svg) no-repeat right top;
      padding-bottom: 35px;
      margin-bottom: 0;
    }
  }

  @media (max-height: variables.$mobile-small-screen-height) and (orientation: landscape) {
    padding: 15px 60px 55px;
    margin-bottom: 0;
    background: none;
    min-height: auto;

    &--step-two {
      padding: 20px 30px 15px;
      background: url(../../../../../../assets/img/postnumber-with-phone-modal-top-bg-mob.svg) no-repeat right top;
    }
  }

  &-button {
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    position: relative;
    font-family: "Roboto", sans-serif;
    width: 100%;
    font-weight: 500;
    font-size: 16px;
    line-height: 20px;
    color: colors.$white;
    text-align: center;
    background: linear-gradient(270deg, colors.$yellow 0%, colors.$darken-orange 100%);
    box-shadow: 0 3px 30px colors.$brown-box-shadow;
    border-radius: 32px;
    text-transform: none;
    height: 48px;
    border: none;
    outline: none;
    margin-bottom: 24px;

    @include mixins.mobile {
      font-size: 13px;
    }

    @include mixins.small-mobile {
      font-size: 12px;
    }

    @media (max-height: variables.$mobile-small-screen-height) and (orientation: landscape) {
      font-size: 16px;
      margin-bottom: 15px;
    }

    &:active,
    &:hover {
      cursor: pointer;
      background: colors.$darken-orange;

      @include mixins.mobile {
        background: linear-gradient(270deg, colors.$yellow 0%, colors.$darken-orange 100%);
      }
    }
  }

  &-warning {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 10px;
    background: colors.$pale-blue;
    padding: 7px 14px;

    &-icon {
      flex-shrink: 0;
      font-style: normal;
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background-color: colors.$main-blue;
      color: colors.$white;
      margin-right: 20px;
    }

    &-info {
      font-size: 14px;
      line-height: 20px;
      color: colors.$main-blue;
      text-align: center;

      @include mixins.mobile {
        font-size: 12px;
        line-height: 18px;
        text-align: left;
      }
    }
  }

  &-close-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    border: none;
    background: none;

    &:hover {
      cursor: pointer;
    }
  }

  &-title {
    font-weight: 500;
    font-size: 28px;
    line-height: 40px;
    color: colors.$main-blue;
    margin: 0;

    @include mixins.mobile {
      font-size: 22px;
      line-height: 28px;
      font-weight: 600;
      max-width: 85%;
      margin: 0 auto;
    }

    @media (max-height: variables.$mobile-small-screen-height) and (orientation: landscape) {
      margin: 10px auto -10px;

      &--step-two {
        margin: 0 auto;
      }
    }
  }

  &-subtitle {
    font-weight: 600;
    font-size: 20px;
    line-height: 24px;
    color: colors.$main-blue;
    margin: 24px 0 16px;

    @include mixins.mobile {
      font-weight: 600;
      font-size: 16px;
      line-height: 24px;
      margin-top: 25px;
    }

    @media (max-height: variables.$mobile-small-screen-height) and (orientation: landscape) {
      margin-top: 25px;

      &--step-two {
        margin-top: 5px;
      }
    }

    &--error {
      color: colors.$red;

      &-step {
        background-color: colors.$orange;
      }
    }

    &-step {
      display: inline-block;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background-color: colors.$light-blue;
      margin-right: 8px;

      &--error {
        background-color: colors.$orange;
      }
    }
  }

  &-description {
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    color: colors.$dark-blue;
    margin-bottom: 24px;

    @media (max-height: variables.$mobile-small-screen-height) and (orientation: landscape) {
      font-size: 14px;
      line-height: 20px;

      &--step-two {
        margin-bottom: 10px;
        font-size: 14px;
        line-height: 16px;
      }
    }

    @media (max-width: variables.$mini-screen-width) {
      font-size: 14px;
      line-height: 20px;
    }
  }

  &-enter-email {
    margin: -16px 0 0;

    &--error {
      color: colors.$red;
    }

    @include mixins.mobile {
      margin-top: auto;
    }

    @media (max-height: variables.$mobile-small-screen-height) and (orientation: landscape) {
      margin-top: auto;
    }
  }

  &-thank-you {
    position: relative;
    font-weight: 500;
    font-size: 22px;
    line-height: 30px;
    text-align: center;
    color: colors.$main-blue;
    background: url(../../../../../../assets/img/checked.svg) no-repeat center top;
    background-size: 32px 25px;
    display: inline-block;
    padding-top: 35px;
    animation-duration: 300ms;
    animation-name: text-rise-up;
    margin: 40px 0 25px;

    @media (max-width: variables.$mobile-screen-width),
    (max-height: variables.$mobile-small-screen-height) and (orientation: landscape) {
      font-size: 22px;
      line-height: 30px;
      margin-top: 50px;
    }

    @keyframes text-rise-up {
      from {
        opacity: 0;
        top: 20px;
      }

      to {
        opacity: 1;
        top: 0;
      }
    }
  }
}
