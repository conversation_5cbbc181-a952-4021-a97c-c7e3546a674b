<kit-modal-default-content>
  <div class="modal" data-test="request-phone-modal-main-wrapper">
    <div class="modal-content" [class.modal-content--thank-you]="isThankYouShown()">
      @if (!isThankYouShown()) {
        <img class="modal-alert-triangle" src="assets/img/alert-triangle.svg" alt="alert" data-test="request-phone-modal-alert-triangle">

        <h4 class="modal-title" data-test="request-phone-modal-title">
          {{ 'successPageV2.main.requestPhoneModal.title' | translate }}
        </h4>

        <p class="modal-description" data-test="request-phone-modal-description">
          {{ 'successPageV2.main.requestPhoneModal.description' | translate }}
        </p>

        <apo-add-phone-number
          data-test="request-phone-modal-add-phone-number-component"
          [errorMessage]="errorMessage()"
          (addPhone)="addPhone($event)">
        </apo-add-phone-number>
      } @else {
        <div class="modal-thank-you" data-test="request-phone-modal-thank-you-text">
          {{ 'successPageV2.main.requestPhoneModal.thankYou' | translate }}
        </div>
      }
    </div>
  </div>
</kit-modal-default-content>
