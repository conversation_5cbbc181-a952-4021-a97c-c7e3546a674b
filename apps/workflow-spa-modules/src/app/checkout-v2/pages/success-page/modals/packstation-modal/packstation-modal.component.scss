@use 'scss/variables';
@use 'scss/colors';
@use 'scss/mixins';

:host {
  min-width: 460px;

  @include mixins.small-tablet() {
    min-width: 343px;
  }
}

.modal {
  position: relative;
  overflow: hidden;
  padding: 40px 60px;
  background: url(../../../../../../assets/img/postnumber-modal-top-bg.svg) no-repeat right top;
  text-align: center;

  @include mixins.mobile {
    padding: 32px 20px;
    background-image: url(../../../../../../assets/img/postnumber-modal-top-bg-mob.svg);
  }

  @media (max-height: variables.$mobile-small-screen-height) and (orientation: landscape) {
    padding: 20px 20px;
    background: none;
  }

  &-dhl-img {
    width: 124px;

    @include mixins.mobile {
      width: 90px;
    }

    @media (max-height: variables.$mobile-small-screen-height) and (orientation: landscape) {
      display: none;
    }
  }

  &-button {
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    position: relative;
    font-family: "Roboto", sans-serif;
    width: 100%;
    font-weight: 500;
    font-size: 16px;
    line-height: 20px;
    color: colors.$white;
    text-align: center;
    background: linear-gradient(270deg, colors.$yellow 0%, colors.$darken-orange 100%);
    box-shadow: 0 3px 30px colors.$brown-box-shadow;
    border-radius: 32px;
    text-transform: none;
    height: 48px;
    border: none;
    outline: none;
    margin-bottom: 24px;

    @include mixins.mobile {
      font-size: 13px;
    }

    @include mixins.small-mobile {
      font-size: 12px;
    }

    @media (max-height: variables.$mobile-small-screen-height) and (orientation: landscape) {
      font-size: 16px;
    }

    &:active,
    &:hover {
      cursor: pointer;
      background: colors.$darken-orange;

      @include mixins.mobile {
        background: linear-gradient(270deg, colors.$yellow 0%, colors.$darken-orange 100%);
      }
    }
  }

  &-warning {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 10px;
    background: colors.$pale-blue;
    padding: 7px 14px;

    &-icon {
      flex-shrink: 0;
      font-style: normal;
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background-color: colors.$main-blue;
      color: colors.$white;
      margin-right: 20px;
    }

    &-info {
      font-size: 14px;
      line-height: 20px;
      color: colors.$main-blue;
      text-align: center;

      @include mixins.mobile {
        font-size: 12px;
        line-height: 18px;
        text-align: left;
      }
    }
  }

  &-close-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    border: none;
    background: none;

    &:hover {
      cursor: pointer;
    }
  }

  &-title {
    font-weight: 500;
    font-size: 24px;
    line-height: 28px;
    color: colors.$main-blue;
    margin: 20px 0 0;

    @include mixins.mobile {
      font-size: 22px;
    }

    @media (max-height: variables.$mobile-small-screen-height) and (orientation: landscape) {
      margin: 10px 0 -10px;
    }
  }

  &-description {
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    color: colors.$dark-blue;

    @media (max-height: variables.$mobile-small-screen-height) and (orientation: landscape),
    (max-width: variables.$mini-screen-width) {
      font-size: 14px;
      line-height: 20px;
    }
  }
}
