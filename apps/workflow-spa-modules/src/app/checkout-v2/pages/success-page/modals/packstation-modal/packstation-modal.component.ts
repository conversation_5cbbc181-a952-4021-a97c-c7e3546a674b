import { Component } from '@angular/core';
import { TranslatePipe } from '@ngx-translate/core';
import { DialogRef, ModalDefaultModule } from '@apo/ui-kit';
import { internalAnalyticsHelper } from '../../../../../../../../../shared/helpers/internal-analytics.helper';

@Component({
  selector: 'apo-packstation-modal',
  templateUrl: './packstation-modal.component.html',
  styleUrls: ['./packstation-modal.component.scss'],
  imports: [
    TranslatePipe,
    ModalDefaultModule,
  ],
})
export class PackstationModalComponent {
  constructor(
    private dialogRef: DialogRef<PackstationModalComponent>,
  ) {}

  onClose() {
    internalAnalyticsHelper('syntheticClick', {element: 'close_packstation_modal'})

    this.dialogRef.close();
  }
}
