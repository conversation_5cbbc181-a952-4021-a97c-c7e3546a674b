import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';


@Injectable()
export class RequestPhoneModalHttpService {
  baseUrl = '/workflow/public';

  constructor(private http: HttpClient) {
  }

  addPhone(orderPid: string, phoneNumber: string): Observable<void> {
    return this.http
      .patch<void>(
        `${this.baseUrl}/order/${orderPid}`,
        {
          phone_number: phoneNumber,
        },
      );
  }
}
