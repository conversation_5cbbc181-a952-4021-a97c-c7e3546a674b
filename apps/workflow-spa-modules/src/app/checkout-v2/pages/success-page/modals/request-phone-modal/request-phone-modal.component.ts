import { Component, Inject, signal } from '@angular/core';
import { AbstractComponent } from '@workflow-spa/shared/helpers';
import { RequestPhoneModalHttpService } from '../services/request-phone-modal.http.service';
import { catchError, delay, takeUntil, tap } from 'rxjs/operators';
import { OrderPid } from '../../../../../shared/models/order-pid';
import { HttpErrorResponse } from '@angular/common/http';
import { EMPTY } from 'rxjs';
import { TrackerEventsEnum } from '../../../../../../../../../shared/models/tracker-events.enum';
import { sdkLogHelper } from '../../../../../../../../../shared/helpers/sdk-log.helper';
import { remapArrayErrors } from '@workflow-spa/shared/helpers/remap-errors.helper';
import { internalAnalyticsHelper } from '../../../../../../../../../shared/helpers/internal-analytics.helper';
import { TranslatePipe } from '@ngx-translate/core';
import { AddPhoneNumberComponent } from '../../components/add-phone-number/add-phone-number.component';
import { DIALOG_DATA, DialogRef, ModalDefaultModule } from '@apo/ui-kit';
import { requestPhoneV2ModalErrorsMap } from '@workflow-spa/shared/consts/errors-map-v2.const';

@Component({
  selector: 'apo-request-phone-modal',
  templateUrl: './request-phone-modal.component.html',
  styleUrls: ['./request-phone-modal.component.scss'],
  imports: [TranslatePipe, AddPhoneNumberComponent, ModalDefaultModule],
  providers: [RequestPhoneModalHttpService],
})
export class RequestPhoneModalComponent extends AbstractComponent {
  readonly errorMessage = signal<string>('');
  readonly isThankYouShown = signal<boolean>(false);

  constructor(
    private dialogRef: DialogRef<RequestPhoneModalComponent>,
    private requestPhoneModalHttpService: RequestPhoneModalHttpService,
    @Inject(DIALOG_DATA) public orderPid: OrderPid,
  ) {
    super();
  }

  addPhone(phoneNumber: string) {
    this.requestPhoneModalHttpService.addPhone(this.orderPid, phoneNumber)
      .pipe(
        catchError((err: HttpErrorResponse) => {
          const errors = remapArrayErrors(
            requestPhoneV2ModalErrorsMap,
            err?.error?.map((err: any) => err.message),
          );
          this.errorMessage.set(errors[0] ?? '');

          internalAnalyticsHelper('errorEvent', {
            type: 'validation',
            value: this.errorMessage(),
          });

          return EMPTY;
        }),
        tap(() => {
          this.isThankYouShown.set(true);

          sdkLogHelper(TrackerEventsEnum.successPagePhoneAdded, {
            order_pid: this.orderPid,
          });
        }),
        delay(2000),
        tap(() => this.close(true)),
        takeUntil(this.ngUnsubscribe$),
      )
      .subscribe();
  }

  private close(isSaved: boolean) {
    this.dialogRef.close(isSaved);
  }

  onClose() {
    sdkLogHelper(TrackerEventsEnum.successPageModalCloseWithoutPhone, {
      order_pid: this.orderPid,
    });

    internalAnalyticsHelper('syntheticClick', { element: 'close_request_phone_modal' });

    this.close(false);
  }
}
