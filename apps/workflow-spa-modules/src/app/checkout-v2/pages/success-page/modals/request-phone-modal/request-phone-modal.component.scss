@use 'scss/mixins';
@use 'scss/variables';
@use 'scss/colors';

:host {
  ::ng-deep {
    kit-modal-default-content {
      position: relative;
      z-index: 1000;
    }
  }

  width: 898px!important;
  max-width: 80vw;

  @include mixins.small-tablet() {
    width: 343px!important;
  }

  kit-modal-default-content {
    padding: 0;
  }
}

.modal {
  position: relative;
  overflow: hidden;
  padding: 55px 50px 90px;
  min-height: 480px;
  background: url(../../../../../../assets/img/phone-modal-bg.svg) no-repeat right center;
  background-size: contain;

  @media (max-width: variables.$small-screen-width) {
    padding: 55px 30px 90px;
  }

  @media (max-width: variables.$mobile-screen-width),
  (max-height: variables.$mobile-small-screen-height) and (orientation: landscape) {
    display: flex;
    justify-content: center;
    background: url(../../../../../../assets/img/phone-modal-bg-mobile.svg) no-repeat bottom center;
    background-size: 100%;
    padding: 27px 15px 255px;
    overflow: auto;
  }

  @media (max-height: variables.$mobile-small-screen-height) and (orientation: landscape) {
    background: none;
    min-height: 280px;
    height: 280px;
    padding-bottom: 0;
  }

  &-alert-triangle {
    width: 62px;
    height: 57px;

    @media (max-width: variables.$mobile-screen-width),
    (max-height: variables.$mobile-small-screen-height) and (orientation: landscape) {
      width: 39px;
      height: 33px;
    }
  }

  &-content {
    width: 316px;

    @media (max-width: variables.$small-screen-width) {
      width: 285px;
    }

    @media (max-width: variables.$mobile-screen-width),
    (max-height: variables.$mobile-small-screen-height) and (orientation: landscape) {
      text-align: center;
    }

    &--thank-you {
      display: flex;
      align-items: center;
      justify-content: center;
      padding-top: 170px;
    }
  }

  &-close-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    border: none;
    background: none;

    &:hover {
      cursor: pointer;
    }
  }

  &-title {
    font-weight: 500;
    font-size: 30px;
    line-height: 41px;
    color: colors.$main-blue;
    margin: 8px 0 -2px;

    @media (max-width: variables.$small-screen-width) {
      font-size: 26px;
    }

    @media (max-width: variables.$mobile-screen-width),
    (max-height: variables.$mobile-small-screen-height) and (orientation: landscape) {
      font-size: 22px;
      line-height: 30px;
      margin-bottom: -8px;
    }

    @media (max-height: 480px) and (orientation: landscape) {
      font-size: 18px;
      line-height: 24px;
      margin-top: 3px;
    }
  }

  &-description {
    font-weight: 500;
    font-size: 18px;
    line-height: 32px;
    color: colors.$black;

    @media (max-width: variables.$small-screen-width) {
      font-size: 16px;
    }

    @media (max-width: variables.$mobile-screen-width),
    (max-height: variables.$mobile-small-screen-height) and (orientation: landscape) {
      font-size: 14px;
      line-height: 30px;
      font-weight: normal;
    }

    @media (max-height: 480px) and (orientation: landscape) {
      font-size: 13px;
      line-height: 18px;
    }
  }

  &-thank-you {
    position: relative;
    font-weight: 500;
    font-size: 30px;
    line-height: 41px;
    text-align: center;
    color: colors.$main-blue;
    background: url(../../../../../../assets/img/checked.svg) no-repeat left top;
    display: inline-block;
    padding-left: 60px;
    animation-duration: 300ms;
    animation-name: text-rise-up;

    @media (max-width: variables.$mobile-screen-width),
    (max-height: variables.$mobile-small-screen-height) and (orientation: landscape) {
      font-size: 22px;
      line-height: 30px;
      background-position: top center;
      padding: 35px 0 0;
      margin-top: -170px;
      background-size: 32px 25px;
    }

    @keyframes text-rise-up {
      from {
        opacity: 0;
        top: 20px;
      }
      to {
        opacity: 1;
        top: 0;
      }
    }
  }
}
