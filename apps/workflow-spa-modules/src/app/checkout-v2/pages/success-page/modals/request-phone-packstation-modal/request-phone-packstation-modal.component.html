<kit-modal-default-header>
  <span data-test="request-phone-packstation-modal-header-title">
    {{ 'successPageV2.main.requestPhonePackstationModal.close' | translate}}
  </span>
</kit-modal-default-header>

<kit-modal-default-content>
  <div class="modal"
       [class.modal--step-two]="isStepTwoShown()"
       data-test="request-phone-packstation-modal-main-wrapper">
    <div class="modal-content">
      <h4 class="modal-title"
          [class.modal-title--step-two]="isStepTwoShown()"
          data-test="request-phone-packstation-modal-title">
        {{ 'successPageV2.main.requestPhonePackstationModal.title' | translate }}
      </h4>

      @if (!isStepTwoShown()) {
        <h5 class="modal-subtitle"
            [class.modal-subtitle--error]="errorMessage()"
            data-test="request-phone-packstation-modal-subtitle-wrapper">
          <span class="modal-subtitle-step"
                [class.modal-subtitle-step--error]="errorMessage()">
            1
          </span>

          @if (!isThankYouShown()) {
            {{ 'successPageV2.main.requestPhonePackstationModal.required' | translate }}
          } @else {
            {{ 'successPageV2.main.requestPhonePackstationModal.received' | translate }}
          }
        </h5>

        @if (!isThankYouShown()) {
          <div class="modal-description" data-test="request-phone-packstation-modal-description-wrapper">
            <p>{{ 'successPageV2.main.requestPhonePackstationModal.description' | translate }}</p>
            <p class="modal-enter-email" [class.modal-enter-email--error]="errorMessage()">
              {{ 'successPageV2.main.requestPhonePackstationModal.enterPhone' | translate }}
            </p>
          </div>

          <apo-add-phone-number
            data-test="request-phone-packstation-modal-add-phone-number-component"
            [errorMessage]="errorMessage()"
            (addPhone)="addPhone($event)">
          </apo-add-phone-number>
        } @else {
          <div class="modal-thank-you" data-test="request-phone-packstation-modal-thank-you-text">
            {{ 'successPageV2.main.requestPhoneModal.thankYou' | translate }}
          </div>
        }
      } @else {
        <h5 class="modal-subtitle modal-subtitle--step-two"
            data-test="request-phone-packstation-modal-subtitle-step-two-wrapper">
          <span class="modal-subtitle-step">2</span>
          <span data-test="request-phone-packstation-modal-subtitle-step-two-title">
            {{ 'successPageV2.main.packstationModal.title' | translate }}
          </span>
        </h5>

        <div class="modal-description modal-description--step-two"
             data-test="request-phone-packstation-modal-description-step-two-description"
             [innerHTML]="'successPageV2.main.packstationModal.description' | translate">
        </div>

        <a class="modal-button"
           target="_blank"
           href="https://www.dhl.de/de/privatkunden/pakete-empfangen/an-einem-abholort-empfangen/packstation/packstation-registrierung.html#registrierung"
           (click)="onClose()"
           data-test="request-phone-packstation-modal-cta"
           data-analytics-click="dhl">
          {{ 'successPageV2.main.packstationModal.ctaButtonText' | translate }}
        </a>

        <div class="modal-warning" data-test="request-phone-packstation-modal-warning-wrapper">
          <i class="modal-warning-icon">!</i>
          <span class="modal-warning-info" data-test="request-phone-packstation-modal-warning-text">
            {{ 'successPageV2.main.packstationModal.warning' | translate }}
          </span>
        </div>
      }
    </div>
  </div>
</kit-modal-default-content>
