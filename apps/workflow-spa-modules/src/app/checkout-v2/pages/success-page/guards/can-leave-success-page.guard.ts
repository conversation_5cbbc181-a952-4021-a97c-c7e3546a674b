import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, RouterStateSnapshot, UrlTree } from '@angular/router';
import { Observable, of } from 'rxjs';
import { map, switchMap } from 'rxjs/operators';
import { CheckoutRenderComponentCommand } from '../../../commands/checkout-render-component.command';
import { CheckoutV2Actions, CheckoutV2Selectors } from '../../../store';
import { Store } from '@ngxs/store';
import { CheckoutRenderSuccessPageCommand } from '../../../commands/checkout-render-success-page.command';
import {
  OrderHasBeenProcessedModalService,
} from '../../../shared/components/order-has-been-processed-modal/order-has-been-processed-modal.service';

@Injectable()
export class CanLeaveSuccessPageGuard  {
  get currentCommand(): CheckoutRenderComponentCommand | null {
    return this.store.selectSnapshot(CheckoutV2Selectors.currentCommand) || null;
  }

  constructor(
    private orderHasBeenProcessedModalService: OrderHasBeenProcessedModalService,
    private store: Store,
  ) {
  }

  canDeactivate(
    component: any,
    currentRoute: ActivatedRouteSnapshot,
    currentState: RouterStateSnapshot,
    nextState?: RouterStateSnapshot,
  ): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {
    if (this.currentCommand && !(this.currentCommand instanceof CheckoutRenderSuccessPageCommand)) {
      return true;
    }

    return this.orderHasBeenProcessedModalService
      .openModal(currentRoute.queryParams['order_pid'])
      .pipe(
        switchMap(res => {
          if (!res) {
            /** returns history order and disallows deactivation */
            history.replaceState(null, null, nextState.url);
            history.pushState(null, null, location.href);
            return of(false);
          }

          /** returns false to save history order.
           * otherwise when user creates new order and clicks go back, he returns to payment step instead of success page */
          return this.store
            .dispatch(new CheckoutV2Actions.ExecuteRestartOrder())
            .pipe(
              map(() => false),
            );
        }),
      );
  }

}
