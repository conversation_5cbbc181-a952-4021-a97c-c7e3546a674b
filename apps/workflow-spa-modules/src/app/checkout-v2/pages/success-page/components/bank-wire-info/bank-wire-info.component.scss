@use 'scss/colors';
@use 'scss/mixins';

.success-page {
  width: 100%;

  &__bank-wire-info {
    margin-top: 24px;
    padding: 16px;
    background: colors.$white;
    border-radius: 8px;

    &--row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: colors.$dark-blue;
      font-family: var(--kit-font-family);
      font-size: var(--kit-typography-text-font-size-md);
      font-weight: 400;
      line-height: var(--kit-typography-text-line-height-md);
      column-gap: 4px;

      @include mixins.small-tablet {
        display: block;
      }

      &:not(:first-child) {
        margin-top: 16px;
      }

      > div {
        flex: 1 0 calc(50% - 2px);

        &:last-child {
          text-align: right;
          word-break: break-word;

          @include mixins.small-tablet {
            text-align: left;
          }
        }
      }
    }

    &--value {
      text-align: right;
      font-weight: 500;
    }

    .note {
      margin-top: 16px;
      color: colors.$dark-grey;
      font-family: var(--kit-font-family);
      font-size: var(--kit-typography-text-font-size-xs);
      font-weight: 400;
      line-height: var(--kit-typography-text-line-height-xs);
    }

    .download-link {
      margin-top: 16px;
      display: flex;
      cursor: pointer;
      font-family: var(--kit-font-family);
      font-size: var(--kit-typography-input-font-size-link);
      font-weight: 500;
      line-height: var(--kit-typography-input-line-height-link);
    }
  }

  &-bank-wire-info {
    margin-top: 32px;
    color: colors.$dark-blue;
    font-family: var(--kit-font-family);
    font-size: var(--kit-typography-headings-font-size-lg);
    font-weight: 500;
    line-height: var(--kit-typography-headings-line-height-lg);

    @include mixins.small-tablet {
      margin-top: 24px;
    }
  }
}
