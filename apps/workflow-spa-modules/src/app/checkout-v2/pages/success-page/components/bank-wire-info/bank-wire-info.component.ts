import { Component, input } from '@angular/core';
import { BankDetails } from '@workflow-spa/shared/models';
import { takeUntil } from 'rxjs/operators';
import { AbstractComponent, downloadFile } from '@workflow-spa/shared/helpers';
import { AccountService } from '../../../../../checkout/core/services/account.service';
import { TranslatePipe } from '@ngx-translate/core';
import { CurrencyExtendedPipe } from '@workflow-spa/shared/pipes/currency-extend.pipe';
import { LinkComponent } from '@apo/ui-kit';

@Component({
  selector: 'apo-bank-wire-info',
  templateUrl: 'bank-wire-info.component.html',
  styleUrls: ['bank-wire-info.component.scss'],
  imports: [
    TranslatePipe,
    CurrencyExtendedPipe,
    LinkComponent,
  ],
})
export class BankWireInfoComponent extends AbstractComponent {
  readonly orderPid = input<string>('');
  readonly totalPrice = input<number>(0);
  readonly currencyCode = input<string>('');
  readonly bankDetails = input<BankDetails | undefined>(undefined);

  constructor(private accountService: AccountService) {
    super();
  }

  onDownloadInvoice() {
    this.accountService
      .getInvoice(this.orderPid())
      .pipe(takeUntil(this.ngUnsubscribe$))
      .subscribe(data => downloadFile(data, 'invoice.pdf'));
  }
}
