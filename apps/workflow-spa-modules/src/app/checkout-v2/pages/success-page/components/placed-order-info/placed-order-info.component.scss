@use 'scss/colors';
@use 'scss/mixins';

:host {
  display: flex;
  flex-direction: column;

  .placed-order-info-wrapper {
    margin-top: 64px;
    display: flex;
    flex-direction: column;

    @include mixins.small-tablet {
      margin-top: 24px;
    }

    .placed-order-info-title {
      color: colors.$dark-blue;
      font-family: var(--kit-font-family);
      font-size: var(--kit-typography-headings-font-size-xl);
      font-weight: var(--kit-typography-text-font-weight-bold);
      line-height: var(--kit-typography-headings-line-height-xl);
    }

    .placed-order-info-description {
      margin-top: 4px;
      color: colors.$dark-blue;
      font-family: var(--kit-font-family);
      font-size: var(--kit-typography-headings-font-size-xs);
      font-weight: 400;
      line-height: var(--kit-typography-text-line-height-sm);
    }
  }
}
