import { Component, EventEmitter, OnInit, Output, input, signal } from '@angular/core';
import {
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import { UserInfo } from '@workflow-spa/shared/models';
import { TranslatePipe } from '@ngx-translate/core';
import {
  AlertComponent,
  AlertSizeEnum,
  AlertTypeEnum,
  ButtonComponent,
  FormFieldComponent,
  InputComponent,
  LabelComponent,
} from '@apo/ui-kit';

@Component({
  selector: 'apo-gender-verification',
  templateUrl: 'gender-verification.component.html',
  styleUrls: ['gender-verification.component.scss'],
  imports: [
    ReactiveFormsModule,
    TranslatePipe,
    FormFieldComponent,
    InputComponent,
    LabelComponent,
    ButtonComponent,
    AlertComponent,
  ],
})
export class GenderVerificationComponent implements OnInit {
  readonly userInfo = input<UserInfo | null>(null);
  readonly userInfoUpdated = input<boolean>(false);

  @Output() confirmUserInfo = new EventEmitter();
  @Output() updateUserInfo = new EventEmitter<UserInfo>();

  readonly isFormVisible = signal(false);
  readonly isConfirmed = signal(false);
  readonly isUpdated = signal(false);

  form: UntypedFormGroup;

  constructor(private fb: UntypedFormBuilder) {}

  ngOnInit() {
    this.initForm();
  }

  private initForm() {
    this.form = this.fb.group({
      firstName: ['', Validators.required],
      lastName: ['', Validators.required],
      email: this.userInfo()?.email,
    });
  }

  updateForm() {
    if (this.form.invalid) return;
    this.isUpdated.set(true);
    this.updateUserInfo.emit(this.form.value);
  }

  confirmForm() {
    this.isConfirmed.set(true);
    this.confirmUserInfo.emit();
  }

  showUserForm() {
    this.isFormVisible.set(true);
  }

  hideUserForm() {
    this.isFormVisible.set(false);
  }

  protected readonly AlertSizeEnum = AlertSizeEnum;
  protected readonly AlertTypeEnum = AlertTypeEnum;
}
