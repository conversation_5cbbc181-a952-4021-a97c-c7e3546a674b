<div class="bank-wire-header-wrapper" data-test="bank-wire-header-wrapper">
  <div class="bank-wire-title" data-test="bank-wire-header-title">
    {{ "successPageV2.main.bankWireHeader.title" | translate }}
  </div>

  <div class="bank-wire-description"
       data-test="bank-wire-header-description"
       [innerHTML]="'successPageV2.main.bankWireHeader.description' | translate"></div>
</div>

@if (userInfo()?.requiredGenderNameValidation) {
  <apo-gender-verification
    [userInfo]="userInfo()"
    [userInfoUpdated]="userInfoUpdated()"
    (confirmUserInfo)="confirmUserInfo.emit($event)"
    (updateUserInfo)="updateUserInfo.emit($event)">
  </apo-gender-verification>
}
