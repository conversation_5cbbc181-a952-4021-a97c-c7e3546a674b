<div class="success-page__order-details" data-test="common-order-info-main-wrapper">
  <h4 class="success-page__order-details--order-number" data-test="common-order-info-order-title">
    {{ 'successPageV2.main.commonOrderInfo.order' | translate }} #{{ orderPid() }}
  </h4>

  <div class="success-page__order-details--row top-row">
    <span class="success-page__order-details--order-included" data-test="common-order-info-prescription-included">
      {{ 'successPageV2.main.commonOrderInfo.prescriptionIncluded' | translate }}
    </span>
    <div class="success-page__order-details--title" data-test="common-order-info-order-total-title">
      {{ 'successPageV2.main.commonOrderInfo.orderTotal' | translate }}:
    </div>
    <div class="success-page__order-details--value" data-test="common-order-info-order-total-value">
      {{ orderInfo()?.totalPrice | currency: orderInfo()?.currencyCode }}
    </div>
  </div>

  @if (toggleOrderDetails()) {
    <div class="success-page__order-details--row">
      <div class="success-page__order-details--title" data-test="common-order-info-product-title">
        {{ 'successPageV2.main.commonOrderInfo.product' | translate }}:
      </div>
      <div class="success-page__order-details--value" data-test="common-order-info-product-value">
        @for (modification of orderInfo()?.modifications; track $index) {
          <div class="item mobile-toggled-hide">
            <span [innerHTML]="productName(modification) | safeHtml"></span>
          </div>
        }
      </div>
    </div>

    @for (item of orderInfo()?.items; track item.name; let i = $index) {
      <div class="success-page__order-details--row"
           [attr.data-test]="'common-order-info-product-row-' + i">
        <div class="success-page__order-details--title"
             [attr.data-test]="'common-order-info-product-title-' + i">
          {{ 'successPageV2.main.commonOrderInfo.product' | translate }}:
        </div>
        <div class="success-page__order-details--value"
             [attr.data-test]="'common-order-info-product-value-' + i">
          {{ item.name }}
        </div>
      </div>
    }

    <div class="success-page__order-details--row">
      <div class="success-page__order-details--title" data-test="common-order-info-delivery-title">
        {{ 'successPageV2.main.commonOrderInfo.delivery' | translate }}:
      </div>
      <div class="success-page__order-details--value" data-test="common-order-info-delivery-value">
        {{ 'successPageV2.main.commonOrderInfo.free' | translate }}
      </div>
    </div>

    <div class="success-page__order-details--row">
      <div class="success-page__order-details--title" data-test="common-order-info-payment-method-title">
        {{ 'successPageV2.main.commonOrderInfo.paymentMethod' | translate }}:
      </div>
      <div class="success-page__order-details--value" data-test="common-order-info-payment-method-value">
        {{ paymentMethod()?.name }}
      </div>
    </div>

    <div class="success-page__order-details--row">
      <div class="success-page__order-details--title" data-test="common-order-info-shipment-date-title">
        {{
          (orderInfo()?.isSubscription
            ? 'successPageV2.main.commonOrderInfo.firstShipmentExpectationText'
            : 'successPageV2.main.commonOrderInfo.firstShipmentExpectationOneTimeText')
            | translate
        }}:
      </div>
      <div class="success-page__order-details--value" data-test="common-order-info-shipment-date-value">
        {{ orderInfo()?.expectedDeliveryDate | date: 'dd.MM.yyyy' }}
      </div>
    </div>
  }

  <span class="success-page__order-details--toggle"
        [attr.data-test]="toggleOrderDetails() ? 'common-order-info-details-toggle-hide' : 'common-order-info-details-toggle-show'"
        (click)="toggleOrderDetailsHandler()">
    @if (toggleOrderDetails()) {
      {{ 'successPageV2.main.commonOrderInfo.hideOrderDetails' | translate }}
    } @else {
      {{ 'successPageV2.main.commonOrderInfo.showOrderDetails' | translate }}
    }
  </span>
</div>
