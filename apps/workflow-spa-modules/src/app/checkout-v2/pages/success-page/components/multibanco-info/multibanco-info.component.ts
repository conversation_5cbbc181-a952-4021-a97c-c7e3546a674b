import { Component, input } from '@angular/core';
import { PaymentMethod } from '@workflow-spa/shared/methods';
import { TranslatePipe } from '@ngx-translate/core';
import { CurrencyExtendedPipe } from '@workflow-spa/shared/pipes/currency-extend.pipe';

@Component({
  selector: 'apo-multibanco-info',
  templateUrl: 'multibanco-info.component.html',
  styleUrls: ['multibanco-info.component.scss'],
  imports: [TranslatePipe, CurrencyExtendedPipe],
})
export class MultibancoInfoComponent {
  readonly totalPrice = input<number>(0);
  readonly currencyCode = input<string>('');
  readonly bankDetails = input<PaymentMethod | null>(null);
}
