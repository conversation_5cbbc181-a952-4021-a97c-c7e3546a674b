import { Component, input, signal } from '@angular/core';
import { takeUntil } from 'rxjs/operators';
import { AbstractComponent, copyToClipboard } from '@workflow-spa/shared/helpers';
import { AddressInfoModel, UserInfo } from '@workflow-spa/shared/models';
import { sdkLogHelper } from '../../../../../../../../../shared/helpers/sdk-log.helper';
import { TrackerEventsEnum } from '../../../../../../../../../shared/models/tracker-events.enum';
import { TranslatePipe } from '@ngx-translate/core';
import {
  AlertComponent,
  AlertSizeEnum,
  AlertTypeEnum,
  ButtonComponent,
  DialogService,
  IconComponent,
} from '@apo/ui-kit';
import { IconIdEnum } from '@apo/ui-kit';
import {
  ChangePasswordModalComponent,
} from '../../change-password-modal/components/change-password-modal/change-password-modal.component';

@Component({
  selector: 'apo-user-info',
  templateUrl: 'user-info.component.html',
  styleUrls: ['user-info.component.scss'],
  imports: [
    TranslatePipe,
    ButtonComponent,
    IconComponent,
    AlertComponent,
  ],
})
export class UserInfoComponent extends AbstractComponent {
  readonly userInfo = input<UserInfo | null>(null);
  readonly shippingInfo = input<AddressInfoModel | null>(null);
  readonly isUserChangePasswordShown = input<boolean>(false);

  readonly passwordIsNotChanged = signal(true);

  constructor(private dialogService: DialogService) {
    super();
  }

  onCopyPassword() {
    sdkLogHelper(TrackerEventsEnum.thankYouPageCopyToClipboardClicked, {
      query_params: window.location.search,
    });

    const pwd = this.userInfo()?.password ?? '';
    copyToClipboard(pwd);
  }

  onResetPassword() {
    const currentPassword = this.passwordIsNotChanged() ? this.userInfo()?.password ?? null : null;

    sdkLogHelper(TrackerEventsEnum.thankYouPageChangePasswordClicked, {
      query_params: window.location.search,
    });

    this.dialogService
      .open(ChangePasswordModalComponent, { data: { currentPassword } })
      .afterClosed()
      .pipe(takeUntil(this.ngUnsubscribe$))
      .subscribe((isChanged: boolean) => this.passwordIsNotChanged.set(!isChanged));
  }

  protected readonly IconIdEnum = IconIdEnum;
  protected readonly AlertTypeEnum = AlertTypeEnum;
  protected readonly AlertSizeEnum = AlertSizeEnum;
}
