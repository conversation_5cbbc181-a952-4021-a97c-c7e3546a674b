@if (isUserChangePasswordShown()) {
  <div class="success-page__customer-password" data-test="customer-password-main-wrapper">
    <div class="success-page-customer-password" data-test="customer-password-title">
      {{ 'successPageV2.main.userInfo.yourCredentials' | translate }}
    </div>

    <div class="success-page__customer-password--row"
         [class.border-bottom]="(userInfo()?.password && passwordIsNotChanged())">
      <div class="success-page__customer-password--title" data-test="customer-password-email-title">
        {{ 'successPageV2.main.userInfo.yourEmail' | translate }}
      </div>
      <div class="success-page__customer-password--value" data-test="customer-password-email-value">
        {{ userInfo()?.email }}
      </div>
    </div>

    @if (userInfo()?.password && passwordIsNotChanged()) {
      <div class="success-page__customer-password--row">
        <div class="success-page__customer-password--title" data-test="customer-password-password-title">
          {{ 'successPageV2.main.userInfo.yourPassword' | translate }}
        </div>
        <div class="success-page__customer-password--value success-page__customer-password--password-value"
             data-test="customer-password-password-value">
          {{ userInfo()?.password }}
          <button class="copy-password"
                  kit-button-ghost
                  kit-button-sm
                  (click)="onCopyPassword()"
                  data-test="customer-password-copy-button">
            <kit-icon [id]="IconIdEnum.copy"></kit-icon>
            {{ 'successPageV2.main.userInfo.copyClipboard' | translate }}
          </button>
        </div>
      </div>

      <kit-alert class="customer-password-alert"
                 [type]="AlertTypeEnum.warning"
                 [size]="AlertSizeEnum.md"
                 [isCloseButtonVisible]="false"
                 data-test="customer-password-warning">
        <p>{{ 'successPageV2.main.userInfo.warningMessage' | translate }}</p>
      </kit-alert>

      <button class="change-password-button"
              kit-button-secondary
              kit-button-sm
              (click)="onResetPassword()"
              data-test="customer-password-change-password-button">
        {{ 'successPageV2.main.userInfo.changePassword' | translate }}
      </button>
    }
  </div>
} @else {
  <div class="success-page-patient-details" data-test="patient-details-title">
    {{ 'successPageV2.main.userInfo.patientDetails' | translate }}
  </div>
  <div class="success-page__customer-details" data-test="patient-details-main-wrapper">
    <div class="success-page__customer-details--row">
      <div class="success-page__customer-details--title" data-test="patient-details-name-title">
        {{ 'successPageV2.main.userInfo.patientsName' | translate }}:
      </div>
      <div class="success-page__customer-details--value" data-test="patient-details-name-value">
        {{ userInfo()?.firstName }} {{ userInfo()?.lastName }}
      </div>
    </div>
    <div class="success-page__customer-details--row">
      <div class="success-page__customer-details--title" data-test="patient-details-email-title">
        {{ 'successPageV2.main.userInfo.email' | translate }}:
      </div>
      <div class="success-page__customer-details--value" data-test="patient-details-email-value">
        {{ userInfo()?.email }}
      </div>
    </div>
    <div class="success-page__customer-details--row">
      <div class="success-page__customer-details--title" data-test="patient-details-address-title">
        {{ 'successPageV2.main.userInfo.deliveryAddress' | translate }}:
      </div>
      <div class="success-page__customer-details--value" data-test="patient-details-address-value">
        {{ shippingInfo()?.countryCode }},
        {{ shippingInfo()?.city }},
        {{ shippingInfo()?.street }}
        {{ shippingInfo()?.houseNumber }}
      </div>
    </div>
  </div>
}
