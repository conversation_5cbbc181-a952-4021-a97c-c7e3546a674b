@use 'scss/colors';
@use 'scss/mixins';

.success-page {
  &__multibanco-info {
    margin-top: 24px;
    padding: 16px;
    background: colors.$white;
    border-radius: 8px;

    &--row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: colors.$dark-blue;
      font-family: var(--kit-font-family);
      font-size: var(--kit-typography-text-font-size-md);
      font-weight: 400;
      line-height: var(--kit-typography-text-line-height-md);
      column-gap: 4px;

      @include mixins.small-tablet {
        display: block;
      }

      &:not(:first-child) {
        margin-top: 16px;
      }

      > div {
        flex: 1 0 calc(50% - 2px);

        &:last-child {
          text-align: right;
          word-break: break-word;

          @include mixins.small-tablet {
            text-align: left;
          }
        }
      }
    }

    &--value {
      text-align: right;
      font-weight: 500;
    }

    .note {
      margin-top: 16px;
      color: colors.$dark-grey;
      font-family: var(--kit-font-family);
      font-size: var(--kit-typography-text-font-size-xs);
      font-weight: 400;
      line-height: var(--kit-typography-text-line-height-xs);
    }
  }
}
