<div class="success-page-bank-wire-info" data-test="bank-wire-info-title">
  {{ 'successPageV2.main.paymentInfo.bank' | translate }}
</div>

<div class="success-page__bank-wire-info" data-test="bank-wire-info-main-wrapper">
  <div class="success-page__bank-wire-info--row">
    <div class="success-page__bank-wire-info--title" data-test="bank-wire-info-remittance-amount-title">
      {{ 'successPageV2.main.paymentInfo.remittanceAmount' | translate }}:
    </div>
    <div class="success-page__bank-wire-info--value" data-test="bank-wire-info-remittance-amount-value">
      {{ totalPrice() | currency: currencyCode() }}
    </div>
  </div>

  <div class="success-page__bank-wire-info--row">
    <div class="success-page__bank-wire-info--title" data-test="bank-wire-info-beneficiary-name-title">
      {{ 'successPageV2.main.paymentInfo.beneficiaryName' | translate }}:
    </div>
    <div class="success-page__bank-wire-info--value" data-test="bank-wire-info-beneficiary-name-value">
      {{ bankDetails()?.beneficiaryName }}
    </div>
  </div>

  <div class="success-page__bank-wire-info--row">
    <div class="success-page__bank-wire-info--title" data-test="bank-wire-info-beneficiary-address-title">
      {{ 'successPageV2.main.paymentInfo.beneficiaryAddress' | translate }}:
    </div>
    <div class="success-page__bank-wire-info--value" data-test="bank-wire-info-beneficiary-address-value">
      {{ bankDetails()?.beneficiaryAddress }}
    </div>
  </div>

  <div class="success-page__bank-wire-info--row">
    <div class="success-page__bank-wire-info--title" data-test="bank-wire-info-bank-name-title">
      {{ 'successPageV2.main.paymentInfo.bankName' | translate }}:
    </div>
    <div class="success-page__bank-wire-info--value" data-test="bank-wire-info-bank-name-value">
      {{ bankDetails()?.bankName }}
    </div>
  </div>

  <div class="success-page__bank-wire-info--row">
    <div class="success-page__bank-wire-info--title" data-test="bank-wire-info-bank-address-title">
      {{ 'successPageV2.main.paymentInfo.bankAddress' | translate }}:
    </div>
    <div class="success-page__bank-wire-info--value" data-test="bank-wire-info-bank-address-value">
      {{ bankDetails()?.bankAddress }}
    </div>
  </div>

  @if (bankDetails()?.accountNumber) {
    <div class="success-page__bank-wire-info--row">
      <div class="success-page__bank-wire-info--title" data-test="bank-wire-info-account-number-title">
        {{ 'successPageV2.main.paymentInfo.accountNumber' | translate }}:
      </div>
      <div class="success-page__bank-wire-info--value" data-test="bank-wire-info-account-number-value">
        {{ bankDetails()?.accountNumber }}
      </div>
    </div>
  }

  <div class="success-page__bank-wire-info--row">
    <div class="success-page__bank-wire-info--title" data-test="bank-wire-info-iban-title">
      {{ 'successPageV2.main.paymentInfo.iban' | translate }}:
    </div>
    <div class="success-page__bank-wire-info--value" data-test="bank-wire-info-iban-value">
      {{ bankDetails()?.iban }}
    </div>
  </div>

  <div class="success-page__bank-wire-info--row">
    <div class="success-page__bank-wire-info--title" data-test="bank-wire-info-swift-title">
      {{ 'successPageV2.main.paymentInfo.swift' | translate }}:
    </div>
    <div class="success-page__bank-wire-info--value" data-test="bank-wire-info-swift-value">
      {{ bankDetails()?.swift }}
    </div>
  </div>

  <div class="success-page__bank-wire-info--row">
    <div class="success-page__bank-wire-info--title" data-test="bank-wire-info-payment-details-title">
      {{ 'successPageV2.main.paymentInfo.paymentDetails' | translate }}:
    </div>
    <div class="success-page__bank-wire-info--value" data-test="bank-wire-info-payment-details-value">
      {{ 'successPageV2.main.paymentInfo.orderId' | translate }} {{ orderPid() }}
    </div>
  </div>

  <div class="note" data-test="bank-wire-info-note">
    {{ 'successPageV2.main.paymentInfo.sureOrderIdInPayment' | translate }}
  </div>

  <a class="download-link"
     kit-link
     data-test="bank-wire-info-download-invoice"
     (click)="onDownloadInvoice()"
     data-analytics-click="download_invoice">
    {{ 'successPageV2.main.paymentInfo.downloadInvoice' | translate }}
  </a>
</div>
