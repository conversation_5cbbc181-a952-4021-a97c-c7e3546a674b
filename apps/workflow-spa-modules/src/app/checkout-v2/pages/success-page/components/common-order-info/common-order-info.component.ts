import { Component, input, signal } from '@angular/core';
import {
  CouponModel,
  OrderInfoModel,
  OrderInfoModificationModel,
} from '@workflow-spa/shared/models';
import { DeliveryType } from '@workflow-spa/shared/enums';
import { PaymentMethod } from '@workflow-spa/shared/methods';
import { TranslatePipe, TranslateService } from '@ngx-translate/core';
import { DiscountModel } from '@workflow-spa/shared/models/discount.model';
import { sdkLogHelper } from '../../../../../../../../../shared/helpers/sdk-log.helper';
import { TrackerEventsEnum } from '../../../../../../../../../shared/models/tracker-events.enum';
import { CurrencyPipe, DatePipe } from '@angular/common';
import { SafeHtmlPipe } from '@workflow-spa/shared/pipes/safe-html.pipe';

@Component({
  selector: 'apo-common-order-info',
  templateUrl: 'common-order-info.component.html',
  styleUrls: ['common-order-info.component.scss'],
  imports: [
    CurrencyPipe,
    DatePipe,
    TranslatePipe,
    SafeHtmlPipe,
  ],
})
export class CommonOrderInfoComponent {
  readonly orderPid = input<string>('');
  readonly paymentMethod = input<PaymentMethod | null>(null);
  readonly coupon = input<CouponModel | undefined>(undefined);
  readonly discounts = input<DiscountModel[]>([]);
  readonly crossSaleEnabled = input<boolean>(false);
  readonly isAboMedsChooseModification = input<boolean>(false);
  readonly orderInfo = input<OrderInfoModel | null>(null);

  readonly toggled = signal(false);
  readonly toggleOrderDetails = signal(false);

  constructor(private translateService: TranslateService) {
    if (typeof window !== 'undefined') {
      this.toggled.set(window.innerWidth <= 576);
    }
  }

  toggleOrderDetailsHandler() {
    this.toggleOrderDetails.update(v => !v);
  }

  productName(modification: OrderInfoModificationModel): string {
    let name = modification.name;

    if (!modification.prescriptionRequired) {
      name += ', x' + modification.quantity;
    } else if (!this.orderInfo()?.isSubscription && !this.crossSaleEnabled()) {
      name += ', ' + this.translateService.instant('successPageV2.main.commonOrderInfo.prescriptionIncluded');
      return name;
    }

    if (this.hasPrescription && this.orderInfo()?.showUnitPrice && !this.crossSaleEnabled()) {
      name += '<br> & ' + this.translateService.instant('successPageV2.main.commonOrderInfo.medicalPrescription');
    }
    return name;
  }

  get hasPrescription(): boolean {
    return Number.isFinite(this.orderInfo()?.prescriptionOriginalPrice);
  }
}
