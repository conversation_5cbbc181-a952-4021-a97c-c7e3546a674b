import { Component, EventEmitter, Output, input } from '@angular/core';
import { UserInfo } from '@workflow-spa/shared/models';
import { EnvironmentLoadService } from '@workflow-spa/shared/services';
import { IEnvironment } from '@workflow-spa/shared/interfaces';
import { TranslatePipe } from '@ngx-translate/core';
import { GenderVerificationComponent } from '../gender-verification/gender-verification.component';

@Component({
  selector: 'apo-placed-order-info',
  templateUrl: 'placed-order-info.component.html',
  styleUrls: ['placed-order-info.component.scss'],
  imports: [
    TranslatePipe,
    GenderVerificationComponent,
  ],
})
export class PlacedOrderInfoComponent {
  readonly userInfo = input<UserInfo | null>(null);
  readonly userInfoUpdated = input<boolean>(false);

  @Output() confirmUserInfo = new EventEmitter();
  @Output() updateUserInfo = new EventEmitter<UserInfo>();

  constructor(private environmentLoadService: EnvironmentLoadService<IEnvironment>) {}
}
