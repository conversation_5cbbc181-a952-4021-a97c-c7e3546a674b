@use 'scss/colors';
@use 'scss/mixins';

:host::ng-deep {
  kit-alert {
    .content {
      p {
        margin: 0;
      }
    }
  }
}

.success-page {
  width: 100%;

  &__customer-details, &__customer-password {
    margin-top: 16px;
    padding: 16px;
    background: colors.$white;
    border-radius: 8px;

    &--row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: colors.$dark-blue;
      font-family: var(--kit-font-family);
      font-size: var(--kit-typography-text-font-size-md);
      font-weight: 400;
      line-height: var(--kit-typography-text-line-height-md);
      column-gap: 4px;

      &:not(:first-child) {
        margin-top: 8px;
      }

      div {
        flex: 1 0 50%;

        &:last-child {
          text-align: right;
          word-break: break-word;

          @include mixins.small-tablet {
            text-align: left;
          }
        }
      }
    }

    &--value {
      color: colors.$dark-blue;
      text-align: right;
      font-family: var(--kit-font-family);
      font-size: var(--kit-typography-text-font-size-md);
      font-weight: 500;
      line-height: var(--kit-typography-text-line-height-md);
    }

    &--password-value {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      fill: colors.$main-blue;

      @include mixins.small-tablet {
        justify-content: flex-start;
      }

      button {
        gap: 4px;
      }
    }
  }

  &__customer-details {
    &--row {
      @include mixins.small-tablet {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
      }

      &:not(:first-child) {
        @include mixins.small-tablet {
          margin-top: 16px;
        }
      }
    }
  }

  &__customer-password {
    margin-top: 24px;

    &--row {
      font-size: var(--kit-typography-text-font-size-sm);
      line-height: var(--kit-typography-text-line-height-sm);
      padding: 12px 0;

      &:not(:first-child) {
        margin-top: 0;
      }
    }

    &--title {

    }

    &--value {
      font-size: var(--kit-typography-text-line-height-3xs);
      line-height: var(--kit-typography-text-line-height-sm);
    }

    .copy-password {
      margin-left: 16px;
      padding: 0;

      svg {
        fill: var(--kit-icon-secondary-blue);
      }
    }

    .customer-password-alert {
      margin-top: 8px;

      @include mixins.small-tablet {
        p {
          font-size: var(--kit-typography-text-font-size-sm);
          line-height: var(--kit-typography-text-line-height-sm);
        }
      }
    }

    .change-password-button {
      margin-top: 12px;
      width: 100%;
    }
  }

  &-customer-password {
    color: colors.$dark-blue;
    font-family: var(--kit-font-family);
    font-size: var(--kit-typography-headings-font-size-md);
    font-weight: 500;
    line-height: var(--kit-typography-headings-line-height-md);
    padding-bottom: 12px;
    border-bottom: 1px solid colors.$very-light-grey;
  }

  &-patient-details {
    margin-top: 32px;
    color: colors.$dark-blue;
    font-family: var(--kit-font-family);
    font-size: var(--kit-typography-headings-font-size-lg);
    font-weight: 500;
    line-height: var(--kit-typography-headings-line-height-lg);

    @include mixins.small-tablet {
      margin-top: 24px;
    }
  }

  &__continue-button button {
    width: 100%;
  }
}

.border-bottom {
  border-bottom: 1px solid colors.$very-light-grey;
}
