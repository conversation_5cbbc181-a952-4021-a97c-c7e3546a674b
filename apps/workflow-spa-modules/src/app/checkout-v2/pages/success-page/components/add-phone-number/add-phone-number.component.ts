import { Component, EventEmitter, Output, ViewChild, computed, input } from '@angular/core';
import { UntypedFormControl, ReactiveFormsModule } from '@angular/forms';
import { AbstractComponent } from '@workflow-spa/shared/helpers';
import { LangCodeToCountryAlpha2Map } from '@workflow-spa/shared/models';
import { EnvironmentLoadService } from '@workflow-spa/shared/services';
import { IEnvironment } from '@workflow-spa/shared/interfaces';
import { PhoneInputModule } from '@workflow-spa/shared/modules';
import { TranslatePipe } from '@ngx-translate/core';
import {
  ButtonComponent,
  ErrorComponent,
  FormFieldComponent,
  InputPhoneComponent,
  LabelComponent,
} from '@apo/ui-kit';
import { LangCode } from '@apo/ui-kit/lib/shared/models/settings-language';

@Component({
  selector: 'apo-add-phone-number',
  templateUrl: 'add-phone-number.component.html',
  styleUrls: ['add-phone-number.component.scss'],
  imports: [
    ReactiveFormsModule,
    PhoneInputModule,
    TranslatePipe,
    FormFieldComponent,
    LabelComponent,
    InputPhoneComponent,
    ErrorComponent,
    ButtonComponent,
  ],
})
export class AddPhoneNumberComponent extends AbstractComponent {
  @ViewChild(InputPhoneComponent) inputPhoneComponent: InputPhoneComponent;

  readonly errorMessage = input<string | null>(null);

  @Output() addPhone = new EventEmitter<string>();

  readonly countryAlpha2 = computed(() =>
    (LangCodeToCountryAlpha2Map(this.environmentLoadService.environment.currentRegion).toLowerCase()) as LangCode,
  );

  phoneControl: UntypedFormControl = new UntypedFormControl();

  constructor(private environmentLoadService: EnvironmentLoadService<IEnvironment>) {
    super();
  }

  onAddPhone() {
    this.addPhone.emit(this.phoneControl.value || this.countryAlpha2());
  }
}
