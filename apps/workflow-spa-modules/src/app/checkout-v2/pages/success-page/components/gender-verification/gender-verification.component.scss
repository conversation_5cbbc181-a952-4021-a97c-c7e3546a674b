@use 'scss/colors';
@use 'scss/mixins';

:host {
  kit-form-field {
    margin: 0;

    &:not(:first-child) {
      margin-top: 12px;
    }

    kit-input {
      margin: 0;
    }
  }

  .gender-verification {
    margin-top: 32px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 16px;
    border-radius: 8px;
    border: 1px solid colors.$default-orange;
    background: colors.$white;

    @include mixins.small-tablet {
      margin-top: 24px;
    }

    h4 {
      color: colors.$dark-blue;
      font-family: var(--kit-font-family);
      font-size: var(--kit-typography-headings-font-size-sm);
      font-weight: 500;
      line-height: var(--kit-typography-headings-line-height-sm);
      margin: 0;
      width: 100%;
    }

    p {
      margin: 4px 0 0 0;
      color: colors.$dark-blue;
      font-family: var(--kit-font-family);
      font-size: var(--kit-typography-text-font-size-md);
      font-weight: 400;
      line-height: var(--kit-typography-text-line-height-md);
      width: 100%;
    }

    span {
      margin: 4px 0 0 0;
      color: colors.$dark-grey;
      font-family: var(--kit-font-family);
      font-size: var(--kit-typography-text-font-size-sm);
      font-weight: 400;
      line-height: var(--kit-typography-text-line-height-sm);
      width: 100%;
    }

    form {
      margin-top: 24px;
      width: 100%;
    }

    .buttons {
      display: flex;
      margin-top: 24px;
      width: 100%;
      gap: 8px;
    }
  }

  .success-verification {
    margin-top: 32px;

    @include mixins.small-tablet {
      margin-top: 24px;
    }

    p {
      margin: 0;
    }
  }
}

