@use 'scss/mixins';
@use 'scss/colors';

.success-page {
  width: 100%;

  &__order-details {
    margin-top: 32px;
    padding: 16px;
    background: colors.$white;
    border-radius: 8px;

    @include mixins.small-tablet {
      margin-top: 24px;
    }

    &--order-included {
      width: 100%;
      color: colors.$dark-blue;
      font-family: var(--kit-font-family);
      font-size: var(--kit-typography-text-font-size-md);
      font-weight: 400;
      line-height: var(--kit-typography-text-line-height-md);
      margin-bottom: 4px;
    }

    &--order-number {
      margin: 0;
      color: colors.$dark-blue;
      font-family: var(--kit-font-family);
      font-size: var(--kit-typography-headings-font-size-md);
      font-weight: 500;
      line-height: var(--kit-typography-headings-line-height-md);
      padding-bottom: 12px;
      border-bottom: 1px solid colors.$very-light-grey;
    }

    &--row {
      display: flex;
      justify-content: space-between;
      padding: 12px 0;
      border-bottom: 1px solid colors.$grey-2;
      font-family: var(--kit-font-family);
      font-size: var(--kit-typography-text-font-size-md);
      font-weight: 500;
      line-height: var(--kit-typography-button-line-height-lg);
      color: colors.$dim-gray;
      column-gap: 4px;

      > div {
        flex: 1 0 calc(50% - 2px);

        &:last-child {
          text-align: right;
          word-break: break-word;

          @include mixins.small-tablet {
            text-align: left;
          }
        }
      }

      div {
        &:last-child {
          text-align: right;
        }
      }

      &.top-row {
        flex-wrap: wrap;
      }
    }

    &--value {
      span {
        font-family: var(--kit-font-family);
        font-size: var(--kit-typography-text-font-size-md);
        font-weight: 500;
        line-height: var(--kit-typography-button-line-height-lg);
        color: colors.$dim-gray;

        @include mixins.small-tablet {
          display: block;
        }
      }
    }

    &--toggle {
      padding-top: 12px;
      display: flex;
      color: colors.$main-blue;
      font-family: var(--kit-font-family);
      font-size: var(--kit-typography-button-font-size-link);
      font-weight: 500;
      line-height: var(--kit-typography-button-line-height-link);
      text-decoration-line: underline;
      cursor: pointer;
    }
  }
}
