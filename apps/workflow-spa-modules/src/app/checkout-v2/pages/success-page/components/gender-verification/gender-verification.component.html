@if (!userInfoUpdated()) {
  <div class="gender-verification" data-test="gender-verification-main-wrapper">
    <h4 data-test="gender-verification-title">
      {{ userInfo()?.firstName }}, {{ 'successPageV2.main.genderVerification.verificationTitle' | translate }}
    </h4>
    <p data-test="gender-verification-subtitle">
      {{ 'successPageV2.main.genderVerification.verificationSubtitle' | translate }}
    </p>
    <span data-test="gender-verification-note">
      {{ 'successPageV2.main.genderVerification.note' | translate }}
    </span>

    @if (isFormVisible()) {
      <form [formGroup]="form" (ngSubmit)="updateForm()" data-test="gender-verification-form">
        <kit-form-field>
          <kit-label for="firstName" data-test="gender-verification-first-name-label">
            {{ 'successPageV2.main.genderVerification.firstName' | translate }}
          </kit-label>
          <kit-input formControlName="firstName"
                     data-analytics-typing="first_name"
                     forId="firstName"
                     data-test="gender-verification-first-name-input"></kit-input>
        </kit-form-field>

        <kit-form-field>
          <kit-label for="lastName" data-test="gender-verification-last-name-label">
            {{ 'successPageV2.main.genderVerification.lastName' | translate }}
          </kit-label>
          <kit-input formControlName="lastName"
                     data-analytics-typing="last_name"
                     forId="lastName"
                     data-test="gender-verification-last-name-input"></kit-input>
        </kit-form-field>

        <div class="buttons" data-test="gender-verification-buttons-wrapper">
          <button kit-button-secondary kit-button-md
                  (click)="hideUserForm()"
                  data-analytics-click="hide_user_form"
                  data-test="gender-verification-cancel-button">
            {{ 'successPageV2.main.genderVerification.cancelButtonText' | translate }}
          </button>

          <button kit-button-primary kit-button-md
                  type="submit"
                  [disabled]="form.invalid"
                  data-test="gender-verification-submit-button">
            {{ 'successPageV2.main.genderVerification.submitButtonText' | translate }}
          </button>
        </div>
      </form>
    } @else {
      <div class="buttons" data-test="gender-verification-buttons-wrapper">
        <button kit-button-secondary kit-button-md
                (click)="showUserForm()"
                data-test="gender-verification-update-button"
                data-analytics-click="show_user_form">
          {{ 'successPageV2.main.genderVerification.updateButtonText' | translate }}
        </button>

        <button kit-button-primary kit-button-md
                (click)="confirmForm()"
                data-test="gender-verification-confirm-button"
                data-analytics-click="confirm_form">
          {{ 'successPageV2.main.genderVerification.confirmButtonText' | translate }}
        </button>
      </div>
    }
  </div>
}

@if (isConfirmed() && userInfoUpdated()) {
  <div class="success-verification" data-test="success-verification-name-confirmed-wrapper">
    <kit-alert [type]="AlertTypeEnum.success" [size]="AlertSizeEnum.md" [isCloseButtonVisible]="false">
      <p data-test="success-verification-name-confirmed-text">
        {{ 'successPageV2.main.genderVerification.nameConfirmed' | translate }}
      </p>
    </kit-alert>
  </div>
}

@if (isUpdated() && userInfoUpdated()) {
  <div class="success-verification" data-test="success-verification-success-wrapper">
    <kit-alert [type]="AlertTypeEnum.success" [size]="AlertSizeEnum.md" [isCloseButtonVisible]="false">
      <p data-test="success-verification-name-confirmed-text">
        {{ 'successPageV2.main.genderVerification.nameChanged' | translate }}
      </p>
    </kit-alert>
  </div>
}
