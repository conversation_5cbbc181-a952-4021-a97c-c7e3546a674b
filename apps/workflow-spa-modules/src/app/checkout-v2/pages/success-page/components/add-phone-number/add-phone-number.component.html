<div class="add-phone-number-main-wrapper" data-test="add-phone-number-main-wrapper">
  <kit-form-field data-test="add-phone-number-field-wrapper">
    <kit-label for="phone"
               data-test="add-phone-number-label">{{ 'successPageV2.main.requestPhoneModal.phonePlaceholder' | translate }}
    </kit-label>
    <kit-input-phone
      forId="phone"
      [id]="'phone-input'"
      [defaultCountryCode]="countryAlpha2()"
      data-test="add-phone-number-input-component"
      [formControl]="phoneControl"
      [class.error]="errorMessage()"
    ></kit-input-phone>

    <button kit-button-primary kit-button-lg (click)="onAddPhone()" data-analytics-click="add_phone" data-test="add-phone-number-button">
      {{ 'successPageV2.main.requestPhoneModal.addPhoneButton' | translate }}
    </button>

    @if (errorMessage()) {
      <kit-error data-test="add-phone-number-error">
        {{ errorMessage() | translate }}
      </kit-error>
    }
  </kit-form-field>
</div>
