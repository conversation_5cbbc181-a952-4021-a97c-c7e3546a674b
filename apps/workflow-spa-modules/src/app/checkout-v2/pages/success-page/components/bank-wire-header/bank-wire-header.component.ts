import { Component, EventEmitter, Output, input } from '@angular/core';
import { UserInfo } from '@workflow-spa/shared/models';
import { TranslatePipe } from '@ngx-translate/core';
import { GenderVerificationComponent } from '../gender-verification/gender-verification.component';

@Component({
  selector: 'apo-bank-wire-header',
  templateUrl: 'bank-wire-header.component.html',
  styleUrls: ['bank-wire-header.component.scss'],
  imports: [
    TranslatePipe,
    GenderVerificationComponent,
  ],
})
export class BankWireHeaderComponent {
  readonly userInfo = input<UserInfo | null>(null);
  readonly userInfoUpdated = input<boolean>(false);

  @Output() confirmUserInfo = new EventEmitter();
  @Output() updateUserInfo = new EventEmitter<UserInfo>();
}
