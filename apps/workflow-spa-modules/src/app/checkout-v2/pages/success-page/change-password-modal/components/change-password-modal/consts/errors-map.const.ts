export const changePasswordV2ModalErrorsMap = {
  'CHANGE_PASSWORD_FORM.VALIDATION.OLD_PASSWORD.REQUIRED': 'successPageV2.main.errors.resetPassword.oldPassRequired',
  'CHANGE_PASSWORD_FORM.VALIDATION.OLD_PASSWORD.INCORRECT': 'successPageV2.main.errors.resetPassword.oldPassIncorrect',
  'CHANGE_PASSWORD_FORM.VALIDATION.PASSWORD.REQUIRED': 'successPageV2.main.errors.resetPassword.passRequired',
  'CHANGE_PASSWORD_FORM.VALIDATION.PASSWORD.EMPTY': 'successPageV2.main.errors.resetPassword.passEmpty',
  'CHANGE_PASSWORD_FORM.VALIDATION.PASSWORD.MIN_LENGTH': 'successPageV2.main.errors.resetPassword.passMinLength',
  '<PERSON>AN<PERSON>_PASSWORD_FORM.VALIDATION.PASSWORD.MAX_LENGTH': 'successPageV2.main.errors.resetPassword.passMaxLength',
  'CHANGE_PASSWORD_FORM.VALIDATION.CONFIRM_PASSWORD.REQUIRED': 'successPageV2.main.errors.resetPassword.confirmPassRequired',
  'CHANGE_PASSWORD_FORM.VALIDATION.CONFIRM_PASSWORD.NOT_MATCH': 'successPageV2.main.errors.resetPassword.confirmPassNotMatch',
};
