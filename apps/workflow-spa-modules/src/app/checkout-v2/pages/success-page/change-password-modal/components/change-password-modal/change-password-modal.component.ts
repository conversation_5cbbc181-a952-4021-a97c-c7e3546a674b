import { Component, Inject, OnInit, signal } from '@angular/core';
import { ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { pairwise, startWith, takeUntil } from 'rxjs/operators';
import { AbstractComponent } from '@workflow-spa/shared/helpers';
import { MethodValidationErrors } from '@workflow-spa/shared/methods';
import { PasswordService } from '../../../../../../shared/services/password.service';
import { sdkLogHelper } from '../../../../../../../../../../shared/helpers/sdk-log.helper';
import { TrackerEventsEnum } from '../../../../../../../../../../shared/models/tracker-events.enum';
import { changePasswordV2ModalErrorsMap } from './consts/errors-map.const';
import { remapErrors } from '@workflow-spa/shared/helpers/remap-errors.helper';
import { internalAnalyticsHelper } from '../../../../../../../../../../shared/helpers/internal-analytics.helper';
import { TranslatePipe } from '@ngx-translate/core';
import {
  ButtonComponent,
  DIALOG_DATA,
  DialogRef,
  ErrorComponent,
  FormFieldComponent,
  IconComponent,
  IconIdEnum,
  InputComponent,
  LabelComponent,
  ModalDefaultModule,
} from '@apo/ui-kit';

type ErrorsShape = {
  old_password: string;
  new_password: string;
  confirm_new_password: string;
};

@Component({
  selector: 'apo-change-password-modal',
  templateUrl: './change-password-modal.component.html',
  styleUrls: ['./change-password-modal.component.scss'],
  imports: [
    TranslatePipe,
    ReactiveFormsModule,
    ModalDefaultModule,
    ButtonComponent,
    FormFieldComponent,
    LabelComponent,
    InputComponent,
    ErrorComponent,
    IconComponent,
  ],
})
export class ChangePasswordModalComponent extends AbstractComponent implements OnInit {
  form: UntypedFormGroup;

  readonly showOldPassword = signal(false);
  readonly showNewPassword = signal(false);
  readonly showConfirmPassword = signal(false);

  readonly errors = signal<MethodValidationErrors<ErrorsShape> | null>(null);

  constructor(
    private dialogRef: DialogRef<ChangePasswordModalComponent>,
    private formBuilder: UntypedFormBuilder,
    private passwordService: PasswordService,
    @Inject(DIALOG_DATA) public data: { currentPassword: string },
  ) {
    super();
  }

  ngOnInit() {
    this.initForm();
    this.deleteErrors();
  }

  private initForm(): void {
    this.form = this.formBuilder.group({
      old_password: this.data.currentPassword,
      new_password: '',
      confirm_new_password: '',
    });
  }

  private deleteErrors() {
    this.form.valueChanges.pipe(
      takeUntil(this.ngUnsubscribe$),
      startWith(this.form.value),
      pairwise(),
    ).subscribe(([curr, prev]) => {
      const errs = this.errors();
      if (!errs) return;

      Object.keys(curr)
        .map(key => [key, curr[key], prev[key]] as const)
        .filter(([key, currValue, prevValue]) => currValue !== prevValue && (errs as any)?.[key])
        .forEach(([key]) => {
          const next = { ...errs, [key]: null } as typeof errs;
          this.errors.set(next);
        });
    });
  }

  onClose() {
    internalAnalyticsHelper('syntheticClick', { element: 'change_password_modal_close' });
    this.dialogRef.close(false);
  }

  onSubmit() {
    sdkLogHelper(TrackerEventsEnum.thankYouPageChangePasswordSaveClicked, {
      query_params: window.location.search,
    });

    this.passwordService.changePassword(this.form.value).subscribe(
      () => this.dialogRef.close(true),
      error => {
        const mapped = remapErrors(changePasswordV2ModalErrorsMap, error?.error) as MethodValidationErrors<ErrorsShape>;
        this.errors.set(mapped);

        internalAnalyticsHelper('errorEvent', {
          type: 'validation',
          value: this.errors(),
        });
      },
    );
  }

  protected readonly IconIdEnum = IconIdEnum;
}
