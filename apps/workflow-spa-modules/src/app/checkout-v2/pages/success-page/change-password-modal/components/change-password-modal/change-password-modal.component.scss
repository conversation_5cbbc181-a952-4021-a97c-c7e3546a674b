@use 'scss/colors';
@use 'scss/mixins';

:host {
  min-width: 460px;

  @include mixins.small-tablet() {
    min-width: 343px;
  }
}

.modal-content {
  margin: 20px;

  @include mixins.small-tablet {
    margin: 15px;
  }
}

.modal-content {
  .modal-body {
    color: colors.$dark-blue;
    font-size: 15px;
    font-weight: 400;

    .password-strength-bar-bottom {
      color: colors.$light-blue;
      font-size: 12px;
      font-weight: 400;
      line-height: 24px;
    }
  }

  .submit {
    margin-top: 20px;

    &__btn {
      width: 100%;
    }
  }
}

kit-form-field {
  input {
    margin-top: 0;
  }

  kit-icon {
    color: colors.$storm-gray;
  }
}

.suffix-btn {
  background: none;
  border: none;
  outline: none;
  color: colors.$dark-blue;

  &:hover {
    cursor: pointer;
    outline: none;
  }
}
