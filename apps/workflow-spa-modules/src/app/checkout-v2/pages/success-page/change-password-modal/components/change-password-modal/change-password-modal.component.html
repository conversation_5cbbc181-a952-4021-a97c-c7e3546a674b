<kit-modal-default-header>
  <span data-test="change-password-modal-header-title">{{ 'successPageV2.main.changePasswordModal.changePassword' | translate }}</span>
</kit-modal-default-header>

<kit-modal-default-content>
  <div class="modal-content" data-test="change-password-modal-content">
    <div class="modal-body">
      <form [formGroup]="form" (ngSubmit)="onSubmit()" data-test="change-password-modal-form">
        <div class="form-row" data-test="change-password-modal-old-password-row">
          <kit-form-field>
            <kit-label for="old-password" data-test="change-password-modal-old-password-label">
              {{ 'successPageV2.main.changePasswordModal.currentPassword' | translate }}
            </kit-label>
            <kit-input formControlName="old_password"
                       [type]="showOldPassword() ? 'text' : 'password'"
                       forId="old-password"
                       [class.error]="errors()?.old_password"
                       data-test="change-password-modal-old-password-input"></kit-input>
            @if (showOldPassword()) {
              <kit-icon [id]="IconIdEnum.eye"
                        (click)="showOldPassword.set(!showOldPassword())"
                        data-analytics-click="toggle_visibility_old_password"
                        data-test="change-password-modal-old-password-eye"></kit-icon>
            } @else {
              <kit-icon [id]="IconIdEnum.eyeSlash"
                        (click)="showOldPassword.set(!showOldPassword())"
                        data-analytics-click="toggle_visibility_old_password"
                        data-test="change-password-modal-old-password-eye-slash"></kit-icon>
            }
            @if (errors()?.old_password) {
              <kit-error data-test="change-password-modal-old-password-error">{{ errors()?.old_password[0] | translate }}</kit-error>
            }
          </kit-form-field>
        </div>

        <div class="form-row" data-test="change-password-modal-new-password-row">
          <kit-form-field>
            <kit-label for="new-password" data-test="change-password-modal-new-password-label">
              {{ 'successPageV2.main.changePasswordModal.newPassword' | translate }}
            </kit-label>
            <kit-input formControlName="new_password"
                       [type]="showNewPassword() ? 'text' : 'password'"
                       forId="new-password"
                       [class.error]="errors()?.new_password"
                       data-test="change-password-modal-new-password-input"></kit-input>
            @if (showNewPassword()) {
              <kit-icon [id]="IconIdEnum.eye"
                        (click)="showNewPassword.set(!showNewPassword())"
                        data-analytics-click="toggle_visibility_new_password"
                        data-test="change-password-modal-new-password-eye"></kit-icon>
            } @else {
              <kit-icon [id]="IconIdEnum.eyeSlash"
                        (click)="showNewPassword.set(!showNewPassword())"
                        data-analytics-click="toggle_visibility_new_password"
                        data-test="change-password-modal-new-password-eye-slash"></kit-icon>
            }
            @if (errors()?.new_password) {
              <kit-error data-test="change-password-modal-new-password-error">{{ errors()?.new_password[0] | translate }}</kit-error>
            }
          </kit-form-field>
        </div>

        <div class="form-row" data-test="change-password-modal-confirm-password-row">
          <kit-form-field>
            <kit-label for="confirm-new-password" data-test="change-password-modal-confirm-password-label">
              {{ 'successPageV2.main.changePasswordModal.repeatPassword' | translate }}
            </kit-label>
            <kit-input formControlName="confirm_new_password"
                       [type]="showConfirmPassword() ? 'text' : 'password'"
                       forId="confirm-new-password"
                       [class.error]="errors()?.confirm_new_password"
                       data-test="change-password-modal-confirm-password-input"></kit-input>
            @if (showConfirmPassword()) {
              <kit-icon [id]="IconIdEnum.eye"
                        (click)="showConfirmPassword.set(!showConfirmPassword())"
                        data-analytics-click="toggle_showing_confirmed_password"
                        data-test="change-password-modal-confirm-password-eye"></kit-icon>
            } @else {
              <kit-icon [id]="IconIdEnum.eyeSlash"
                        (click)="showConfirmPassword.set(!showConfirmPassword())"
                        data-analytics-click="toggle_showing_confirmed_password"
                        data-test="change-password-modal-confirm-password-eye-slash"></kit-icon>
            }
            @if (errors()?.confirm_new_password) {
              <kit-error data-test="change-password-modal-confirm-password-error">{{ errors()?.confirm_new_password[0] | translate }}</kit-error>
            }
          </kit-form-field>
        </div>

        <div class="submit" data-test="change-password-modal-submit-row">
          <button kit-button-primary kit-button-lg
                  class="submit__btn"
                  id="change-password-submit-button"
                  data-test="change-password-modal-submit-button"
                  data-analytics-click="submit_change_password">
            {{ 'successPageV2.main.changePasswordModal.apply' | translate }}
          </button>
        </div>
      </form>
    </div>
  </div>
</kit-modal-default-content>
