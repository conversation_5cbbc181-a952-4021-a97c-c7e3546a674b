import { Injectable } from '@angular/core';
import { CommandError, CommandErrorCode } from '@workflow-spa/shared/commands';
import { Observable, of } from 'rxjs';
import { filter, first, switchMap } from 'rxjs/operators';
import { HttpErrorResponse } from '@angular/common/http';
import { SessionExpiredModalService } from '../shared/modals/session-expired-modal/session-expired-modal.service';
import { CheckoutV2Actions, CheckoutV2Selectors } from '../store';
import { Store } from '@ngxs/store';

@Injectable({
  providedIn: 'root',
})
export class CheckoutWorkflowErrorHandlerV2Service {
  constructor(
    private sessionExpiredModalService: SessionExpiredModalService,
    private store: Store,
  ) {
  }

  handleError(response: HttpErrorResponse): Observable<any> {
    const error = response.error;

    switch (error?.code) {
      case CommandErrorCode.SessionExpiredError:
        return this.processExpirationError(error);
      default:
        return of(null);
    }
  }

  processExpirationError(error: CommandError) {
    const isSessionExpired = true;
    this.sessionExpiredModalService.showModal().pipe(
      filter(res => !!res),
      switchMap(() => this.store.dispatch(new CheckoutV2Actions.ExecuteErrorRestartOrder(error, isSessionExpired))),
      first(),
    ).subscribe();

    return of(null);
  }


}
