import { Component, Inject } from '@angular/core';
import { TranslatePipe } from '@ngx-translate/core';
import { ButtonComponent, DIALOG_DATA, DialogRef, ModalDefaultModule } from '@apo/ui-kit';

@Component({
  selector: 'apo-session-expired-modal',
  templateUrl: './session-expired-modal.component.html',
  styleUrls: ['./session-expired-modal.component.scss'],
  imports: [
    TranslatePipe,
    ModalDefaultModule,
    ButtonComponent,
  ],
})
export class SessionExpiredModalComponent {

  constructor(
    private dialogRef: DialogRef<SessionExpiredModalComponent>,
    @Inject(DIALOG_DATA) public data,
  ) {
  }

  onConfirmClick() {
    this.dialogRef.close(true);
  }
}
