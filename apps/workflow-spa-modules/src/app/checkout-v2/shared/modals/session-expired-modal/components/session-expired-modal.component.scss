@use 'scss/colors';
@use 'scss/mixins';

:host {
  min-width: 460px;

  @include mixins.small-tablet() {
    min-width: 343px;
  }
}

.modal {
  position: relative;
  padding: 30px;
  text-align: center;

  @include mixins.small-tablet {
    padding: 15px;
  }
}

.info {
  font-size: 50px;
  color: colors.$blue;
}

.content {
  color: var(--text-default-color);
  margin: 30px 0;
}

.button {
  min-width: 120px;
}
