import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { SessionExpiredModalComponent } from './components/session-expired-modal.component';
import { DialogService } from '@apo/ui-kit';

@Injectable({ providedIn: 'root' })
export class SessionExpiredModalService {

  constructor(
    private dialogRef: DialogService,
  ) {}

  showModal(): Observable<boolean> {
    const dialog = this.dialogRef.open(SessionExpiredModalComponent, {
      data: {},
    });

    return dialog.afterClosed();
  }
}
