import { Component, Inject } from '@angular/core';
import { LanguageService } from '@workflow-spa/shared/services';
import { sdkLogHelper } from '../../../../../../../../shared/helpers/sdk-log.helper';
import { TrackerEventsEnum } from '../../../../../../../../shared/models/tracker-events.enum';
import { TranslatePipe } from '@ngx-translate/core';
import { ButtonComponent, DIALOG_DATA, DialogRef, ModalDefaultModule } from '@apo/ui-kit';
import { internalAnalyticsHelper } from '../../../../../../../../shared/helpers/internal-analytics.helper';

@Component({
  selector: 'apo-order-has-been-processed-modal',
  templateUrl: './order-has-been-processed-modal.component.html',
  styleUrls: ['order-has-been-processed-modal.component.scss'],
  imports: [
    TranslatePipe,
    ModalDefaultModule,
    ButtonComponent,
  ],
})
export class OrderHasBeenProcessedModalComponent {

  get baseUrl() {
    return location.origin;
  }

  get langCode() {
    return this.languageService.currentLanguage;
  }

  constructor(
    private languageService: LanguageService,
    @Inject(DIALOG_DATA) public orderPid: string,
    public dialogRef: DialogRef<OrderHasBeenProcessedModalComponent>,
  ) {}

  onCloseClick() {
    internalAnalyticsHelper('syntheticClick', {element: 'processed_order_modal_close'});

    this.dialogRef.close();
  }

  onCreateOrderClick() {
    this.dialogRef.close(true);
  }

  onGoToProfile() {
    this.dialogRef.close();
    setTimeout(() => {
      location.href = `${this.baseUrl}/${this.langCode}/account/order-history`;
    }, 100);

    sdkLogHelper(TrackerEventsEnum.orderHistoryHasBeenProcessedModalClicked, {
      query_params: window.location.search,
    });
  }
}
