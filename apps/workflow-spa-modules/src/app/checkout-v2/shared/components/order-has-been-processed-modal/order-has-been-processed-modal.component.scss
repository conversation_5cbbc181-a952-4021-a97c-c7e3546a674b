@use 'scss/colors';
@use 'scss/mixins';

:host {
  min-width: 460px;

  @include mixins.small-tablet() {
    min-width: 343px;
  }

  &::ng-deep {
    kit-modal-default-content {
      .content {
        p {
          color: colors.$dark-blue;
          font-family: var(--kit-font-family);
          font-size: var(--kit-typography-text-font-size-md);
          font-weight: 400;
          line-height: var(--kit-typography-text-line-height-md);

          a {
            text-decoration-line: underline;
          }
        }
      }
    }
  }

  kit-modal-default-header {
    span {
      color: colors.$dark-blue;
      font-family: var(--kit-font-family);
      font-size: var(--kit-typography-headings-font-size-md);
      font-weight: 500;
      line-height: var(--kit-typography-headings-line-height-md);
    }
  }

  kit-modal-default-content {
    padding: 32px 24px 40px 24px;
  }

  kit-modal-default-footer {
    padding: 12px 24px;

    .buttons {
      width: 100%;

      button {
        width: 100%;
      }
    }
  }
}
