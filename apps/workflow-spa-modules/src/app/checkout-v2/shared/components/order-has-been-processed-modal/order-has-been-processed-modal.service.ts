import { Injectable } from '@angular/core';
import { MODAL_BACKDROP_CSS_CLASS, MODAL_PANEL_CSS_CLASS } from '../../../../shared/consts/modal-classes.const';
import { OrderHasBeenProcessedModalComponent } from './order-has-been-processed-modal.component';
import { DialogRef, DialogService } from '@apo/ui-kit';

@Injectable({ providedIn: 'root' })
export class OrderHasBeenProcessedModalService {
  private currentModal: DialogRef<any>;

  private readonly panelClass = MODAL_PANEL_CSS_CLASS;

  private readonly backdropClass = MODAL_BACKDROP_CSS_CLASS;

  constructor(
    private dialogService: DialogService,
  ) {}

  openModal(orderPid: string) {
    if (this.currentModal) {
      this.currentModal.close();
    }

    this.currentModal = this.dialogService
      .open(
        OrderHasBeenProcessedModalComponent, { data: orderPid },
      );

    return this.currentModal.afterClosed();
  }
}
