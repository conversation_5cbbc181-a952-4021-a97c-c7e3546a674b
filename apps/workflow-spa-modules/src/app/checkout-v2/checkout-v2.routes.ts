import { Routes } from '@angular/router';
// import {
//   CheckoutPageComponentEnum,
//   componentToRoute,
// } from './enums/checkout-path-component.enum';
import { CheckoutSetQueryParamsV2Guard } from './guards/checkout-set-query-params-v2.guard';
import { HaveNoCommandErrorsV2Guard } from './guards/have-no-command-errors-v2.guard';
import { HistoryPreventV2Guard } from './guards/history-prevent-v2.guard';
import { ReinitializeOrderV2Guard } from './guards/reinitialize-order-v2.guard';
import {
  CheckoutRenderComponentEnum,
  MapRenderComponentToRoute,
} from '../checkout/shared/enums/checkout-render-component.enum';
import { CheckoutV2Component } from './checkout-v2.component';
import { CanLeaveSuccessPageGuard } from './pages/success-page/guards/can-leave-success-page.guard';

export default [
  {
    /** Only success page renders without ReinitializeOrderGuard
     * so it's the reason why it's moved to separate route */
    path: MapRenderComponentToRoute[CheckoutRenderComponentEnum.success],
    data: { page: CheckoutRenderComponentEnum.success },

    canDeactivate: [CanLeaveSuccessPageGuard],
    loadComponent: () => import('./pages/success-page/success-page/success-page.component').then(m => m.SuccessPageComponent),
  },
  {
    path: '',
    canActivate: [CheckoutSetQueryParamsV2Guard, ReinitializeOrderV2Guard],
    component: CheckoutV2Component,
    children: [
      {
        path: '',
        data: { page: 'start-order' },
        canActivate: [HistoryPreventV2Guard],
        canDeactivate: [HaveNoCommandErrorsV2Guard],
        loadChildren: () => import('./pages/loader-page/loader-page.routing'),
      },
      {
        path: MapRenderComponentToRoute[
          CheckoutRenderComponentEnum.prescriptionProviderOnline
        ],
        data: { page: CheckoutRenderComponentEnum.prescriptionProviderOnline },
        canActivate: [HistoryPreventV2Guard],
        canDeactivate: [HaveNoCommandErrorsV2Guard],
        loadChildren: () =>
          import(
            './pages/confirm-online-prescription-page/confirm-online-prescription-page.routing'
          ),
      },
      {
        path: 'consultation-redirect',
        data: { page: 'consultation-redirect' },
        canActivate: [HistoryPreventV2Guard],
        canDeactivate: [HaveNoCommandErrorsV2Guard],
        loadChildren: () => import('./pages/loader-page/loader-page.routing'),
      },
      {
        path: 'finish-consultation',
        data: { page: 'finish-consultation' },
        canActivate: [HistoryPreventV2Guard],
        loadChildren: () => import('./pages/loader-page/loader-page.routing'),
      },
      {
        path: '**',
        pathMatch: 'full',
        redirectTo: CheckoutRenderComponentEnum.prescriptionProviderOnline,
      },
    ],
  },
] as Routes;
