import { Component, HostListener, OnInit, signal } from '@angular/core';
import { ExitIntentManagementService } from '../components/exit-intent-modal/services/exit-intent-management.service';
import { ProcessPaymentModalService } from '../components/process-payment-modal/services/process-payment-modal.service';
import { LanguageService } from '@workflow-spa/shared/services';
import { environment } from '../../../environments/environment';

@Component({
  template: 'abstract',
  standalone: false,
})
export abstract class ExitIntentListenerComponent implements OnInit {
  readonly _config = signal<ExitIntentConfig>({
    confirmUnload: true,
    handleTabBecomesInactive: false, // PayPal native window triggers tab to become inactive
  });
  private _exitIntentService: ExitIntentManagementService;

  protected constructor(
    protected processPaymentModalService: ProcessPaymentModalService,
    protected languageService: LanguageService,
    exitIntentService: ExitIntentManagementService,
  ) {
    this._exitIntentService = exitIntentService;
  }

  get shouldConfirmUnload(): boolean {
    return this._config().confirmUnload;
  }

  get shouldHandleTabBecomesInactive(): boolean {
    return this._config().handleTabBecomesInactive;
  }

  abstract get shouldSkipSaveLeave(): boolean;

  ngOnInit() {
    this._exitIntentService.initMouseWatcher();
    this.processPaymentModalService.init();
  }

  @HostListener('window:beforeunload', ['$event'])
  unloadNotification($event: BeforeUnloadEvent): void {
    if (this.shouldSkipSaveLeave) return;

    if (this.shouldConfirmUnload && environment.production) {
      $event.returnValue = true;
    }
  }

  @HostListener('window:blur')
  handleTabBecomeInactive(): void {
    if (this.shouldHandleTabBecomesInactive && this._exitIntentService.isMobile) {
      this._exitIntentService.showModal();
    }
  }

  @HostListener('body:mouseleave')
  mouseLeave(): void {
    this.processPaymentModalService.openIfPaymentIsProcessing();
  }
}

export interface ExitIntentConfig {
  confirmUnload: boolean;
  handleTabBecomesInactive: boolean;
}
