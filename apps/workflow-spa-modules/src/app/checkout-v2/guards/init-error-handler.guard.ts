import { Injectable } from '@angular/core';
import { CanActivate } from '@angular/router';
import { CheckoutWorkflowErrorHandlerV2Service } from '../services/checkout-workflow-error-handler-v2.service';

@Injectable({ providedIn: 'root' })
export class InitErrorHandlerGuard implements CanActivate {
  constructor(private errorHandler: CheckoutWorkflowErrorHandlerV2Service) {}
  canActivate(): boolean {
    this.errorHandler.initErrorHandler();
    return true;
  }
}
