<footer class="container" [class.container--sticky-button]="isCtaSticky" data-test="checkout-footer-main-wrapper">
  <div class="contacts-block">
    <div class="contacts-block__phone" data-test="checkout-footer-phone-wrapper">
      @if (hasLocalPhoneNumber) {
        <a href="tel:{{ 'checkoutV2Footer.main.contacts.main.phone' | translate }}"
           data-test="checkout-footer-local-phone-number"
           data-analytics-click="local_phone_number">
          <i class="icon icon-phone"></i>
          {{ 'checkoutV2Footer.main.contacts.main.phone' | translate }}
        </a>
        {{ 'checkoutV2Footer.main.phoneFreeMessage' | translate }}
        <span
          class='header-tel-dropdown'
          [matMenuTriggerFor]='menu'
        ></span>
        <mat-menu
          #menu='matMenu'
          xPosition='before'
          class='header-tel-drop-item'
        >
          <a href="tel:{{ 'checkoutV2Footer.main.contacts.main.international' | translate }}"
             data-analytics-click="international_phone_number">
            {{ 'checkoutV2Footer.main.contacts.main.international' | translate }}
          </a>
          {{ 'checkoutV2Footer.main.international' | translate }}
        </mat-menu>
      } @else {
        <a href="tel:{{ 'checkoutV2Footer.main.contacts.main.international' | translate }}"
           data-test="checkout-footer-international-phone-number"
           data-analytics-click="international_phone_number">
          <i class="icon icon-phone"></i>
          {{ 'checkoutV2Footer.main.contacts.main.international' | translate }}
        </a>
        {{ 'checkoutV2Footer.main.international' | translate }}
      }
    </div>

    <div class="contacts-block__chat" data-test="checkout-footer-live-chat-wrapper">
      <a (click)="onShowChat()" aria-label="Live Chat" data-analytics-click="show_chat_link" data-test="checkout-footer-live-chat-link">
        <i class="icon icon-comment-alt"></i>
        {{ 'checkoutV2Footer.main.liveChat' | translate }}
      </a>
    </div>
  </div>

  <div class="footer" data-test="checkout-footer-footer-wrapper">
    <div class="footer-content-wrapper">
      <div class="footer__copyright"
           data-test="checkout-footer-copyright-text"
           [innerHTML]="('checkoutV2Footer.main.copyrightText' | translate) | safeHtml">
      </div>
    </div>

    <div class="footer__icon-block">
      <div class="footer__trust-icons" data-test="checkout-footer-trust-icons-wrapper">
        @for (icon of ('checkoutV2Footer.main.iconsGroup.icons' | translate); track $index) {
          @if (icon.link) {
            <a href='{{ icon.link }}' target='_blank' [attr.data-test]="'checkout-footer-trust-icon-link-' + $index">
              @if (icon.externalImage?.src) {
                <img alt="{{ icon.externalImage?.alt }}"
                     src="{{ icon.externalImage?.src }}"
                     width="{{ icon.externalImage?.width }}"
                     height="{{ icon.externalImage?.height }}"
                     loading="lazy"
                     [attr.data-test]="'checkout-footer-trust-icon-img-' + $index" />
              } @else {
                <img alt="{{ icon.internalImage?.alt }}"
                     src="{{ icon.internalImage?.src }}"
                     width="{{ icon.internalImage?.width }}"
                     height="{{ icon.internalImage?.height }}"
                     loading="lazy"
                     [attr.data-test]="'checkout-footer-trust-icon-img-' + $index" />
              }
            </a>
          } @else {
            @if (icon.externalImage?.src) {
              <img alt="{{ icon.externalImage?.alt }}"
                   src="{{ icon.externalImage?.src }}"
                   width="{{ icon.externalImage?.width }}"
                   height="{{ icon.externalImage?.height }}"
                   loading="lazy"
                   [attr.data-test]="'checkout-footer-trust-icon-img-' + $index" />
            } @else {
              <img alt="{{ icon.internalImage?.alt }}"
                   src="{{ icon.internalImage?.src }}"
                   width="{{ icon.internalImage?.width }}"
                   height="{{ icon.internalImage?.height }}"
                   loading="lazy"
                   [attr.data-test]="'checkout-footer-trust-icon-img-' + $index" />
            }
          }
        }
      </div>
    </div>
  </div>

  @if (currentCommand()) {
    @if (isSignUpWithOnlineFlow(currentCommand())) {
      <p class="footer__disclaimer" data-test="checkout-footer-disclaimer-registration">
        *{{ 'checkoutV2Footer.main.regulationDisclaimer.registration' | translate }}
      </p>
    }
    @if (isChooseTreatment(currentCommand())) {
      <p class="footer__disclaimer" data-test="checkout-footer-disclaimer-choose-treatment">
        *{{ 'checkoutV2Footer.main.regulationDisclaimer.chooseTreatment' | translate }}
      </p>
    }
    @if (isAbTest(currentCommand())) {
      <p class="footer__disclaimer" data-test="checkout-footer-disclaimer-choose-treatment-full-price-ab-test">
        **{{ 'checkoutV2Footer.main.regulationDisclaimer.chooseTreatmentFullPriceAbTest' | translate }}
      </p>
    }
  }
</footer>
