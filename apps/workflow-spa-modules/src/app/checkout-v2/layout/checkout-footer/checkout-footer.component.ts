import { Component, computed, Input } from '@angular/core';
import { MatMenuModule } from '@angular/material/menu';
import { TranslatePipe } from '@ngx-translate/core';

import { EnvironmentLoadService, FreshchatService, LanguageService } from '@workflow-spa/shared/services';
import { IEnvironment } from '@workflow-spa/shared/interfaces';
import { SafeHtmlPipe } from '@workflow-spa/shared/pipes/safe-html.pipe';
import { PrescriptionProviderEnum } from '../../enums/prescription-provider.enum';
import { RenderSignUpCommand } from '../../commands/render-sign-up.command';
import { RenderChooseProductV1Command } from '../../commands/render-choose-product-v1.command';
import { RenderChooseProductV2Command } from '../../commands/render-choose-product-v2.command';
import { RenderChoosePackageV1Command } from '../../commands/render-choose-package-v1.command';
import { RenderChoosePackageV2Command } from '../../commands/render-choose-package-v2.command';
import { CheckoutRenderComponentCommand } from '../../commands/checkout-render-component.command';
import { CheckoutV2Selectors } from '../../store';
import { Store } from '@ngxs/store';
import { toSignal } from '@angular/core/rxjs-interop';

@Component({
  selector: 'apo-checkout-footer',
  templateUrl: './checkout-footer.component.html',
  styleUrls: ['./checkout-footer.component.scss'],
  imports: [
    MatMenuModule,
    TranslatePipe,
    SafeHtmlPipe,
  ],
})
export class CheckoutFooterComponent {
  @Input() isMobile = false;

  readonly currentCommand = toSignal<CheckoutRenderComponentCommand | null>(
    this.store.select(CheckoutV2Selectors.currentCommand),
    { initialValue: null },
  );

  readonly isCtaSticky = computed(() =>
    (this.currentCommand()?.component as any)?.isCtaSticky,
  );

  constructor(
    private languageService: LanguageService,
    private _fcService: FreshchatService,
    private environmentLoadService: EnvironmentLoadService<IEnvironment>,
    private store: Store,
  ) {}

  get currentLanguage() {
    return this.languageService.currentLanguage;
  }

  get hasLocalPhoneNumber(): boolean {
    return !!window['apomedsConfigs']?.spaTranslations?.checkoutFooter?.main?.contacts?.main?.phone;
  }

  get imageResizerUrl() {
    return this.environmentLoadService.environment.imageResizerUrl;
  }

  onShowChat() {
    this._fcService.open();
  }

  isChooseTreatment(command) {
    return (
      command instanceof RenderChooseProductV1Command ||
      command instanceof RenderChooseProductV2Command ||
      command instanceof RenderChoosePackageV1Command ||
      command instanceof RenderChoosePackageV2Command
    );
  }

  isAbTest(command): boolean {
    return command.displayFullPrice;
  }

  isSignUpWithOnlineFlow(command) {
    return (
      command instanceof RenderSignUpCommand &&
      command.prescriptionProvider === PrescriptionProviderEnum.online
    );
  }
}
