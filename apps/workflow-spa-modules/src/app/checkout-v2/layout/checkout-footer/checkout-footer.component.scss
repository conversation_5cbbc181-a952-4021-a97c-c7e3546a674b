@use 'scss/colors';
@use 'scss/mixins';

:host {
  display: block;
  width: 100%;
  background: var(--footer-background-color);
}

.container {
  &--sticky-button {
    @include mixins.mobile {
      padding-bottom: 60px;
    }
  }
}

footer {
  width: 100%;
  color: var(--text-default-color);

  .contacts-block {
    color: var(--text-default-color);
    width: 100%;
    display: flex;
    align-items: flex-start;
    padding: 20px 0 10px;

    font-size: 15px;
    line-height: 22px;

    @include mixins.tablet {
      justify-content: center;
      padding-bottom: 15px;
    }

    @include mixins.mobile {
      flex-wrap: wrap;
      justify-content: space-around;
      margin-bottom: 20px;

      & > div {
        margin: 5px 0;
      }
    }

    i {
      color: var(--footer-icons-color);
      margin-right: 5px;
    }

    &__phone {
      margin-right: 30px;

      @include mixins.mobile {
        margin-right: 0;
      }

      a {
        color: var(--text-default-color);
        margin-right: 5px;
        text-decoration: none;
      }
    }

    &__chat {
      a {
        cursor: pointer;
      }
    }

    &__whatsapp {
      margin-left: 30px;

      @include mixins.mobile {
        margin-left: 0;
      }

      a {
        text-decoration: none;
        color: colors.$dark-blue;
      }

      i {
        font-size: 18px;
      }
    }
  }

  .footer {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 15px;

    @include mixins.tablet {
      flex-direction: column-reverse;
    }

    .footer-content-wrapper {
      .footer__copyright {
        font-size: 11px;
        line-height: 18px;
        text-align: center;
        color: colors.$main-blue;
        margin: auto 0;
        padding-right: 15px;

        @include mixins.small-tablet {
          margin: 0 auto;
          padding-right: 0;
        }

        &::ng-deep {
          p {
            text-align: left;

            @include mixins.tablet {
              text-align: center;
              margin: auto;
            }
          }
        }
      }

      .cancellation-policy__link {
        font-size: 13px;
        line-height: 18px;
        color: colors.$main-blue;

        @include mixins.mobile {
          text-align: center;
        }
      }
    }

    &__trust-icons {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      width: 100%;

      @include mixins.small-tablet {
        flex-wrap: wrap;
      }

      > * {
        margin: 0 15px;

        &:first-child {
          margin-left: 0;
        }

        &:last-child {
          margin-right: 0;
        }

        @include mixins.small-tablet {
          margin: 0;
        }
      }

      @include mixins.tablet {
        width: auto;
      }

      @include mixins.small-tablet {
        width: 95%;
        justify-content: space-around;
        margin-bottom: 20px;
      }

      @include mixins.mobile {
        margin-bottom: 10px;
      }

      img {
        display: inline-block;

        @include mixins.mobile {
          margin-bottom: 10px;
          max-height: 65px;
          width: auto;
        }

        @include mixins.medium-mobile {
          max-width: 170px;
        }
      }
    }

    &__region-selector {
      max-width: 200px;
      width: 100%;

      @include mixins.tablet {
        display: flex;
        flex-direction: column;
        align-items: baseline;
      }

      @include mixins.small-tablet {
        max-width: 100%;
        align-items: center;
      }
    }

    &__disclaimer {
      font-size: 11px;
      line-height: 18px;
      text-align: left;
      color: colors.$main-blue;
      padding-bottom: 15px;
      margin: auto;

      @include mixins.tablet {
        max-width: 510px;
        text-align: center;
      }

      @include mixins.small-tablet {
        text-align: center;
      }
    }

    &__icon-block {
      display: flex;
      width: 100%;

      @include mixins.tablet {
        justify-content: center;
      }

      @include mixins.small-tablet {
        flex-direction: column;
        align-items: center;
      }
    }
  }
}
