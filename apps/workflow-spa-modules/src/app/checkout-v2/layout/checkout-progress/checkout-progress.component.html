@if (shouldShowProgress()) {
  <div class="container checkout-progress-bar" data-test="checkout-progress-bar-main-wrapper">
    <mat-progress-bar mode="determinate"
                      data-test="checkout-progress-mat-progress-bar"
                      [class.completed]="progressPercentage() === 100"
                      [value]="progressPercentage()">
    </mat-progress-bar>

    <div class="checkout-progress-bar__legend">
      <div class="checkout-progress-bar__legend__item checkout-progress-bar__legend__item--active">
        <i></i>
      </div>

      <div class="checkout-progress-bar__legend__item" data-test="checkout-progress-bar-item"
           [class.checkout-progress-bar__legend__item--active]="progressPercentage() === 100">
        <i></i>
      </div>
    </div>

    <h3 class="checkout-progress-bar__title" data-test="checkout-progress-bar-title">
      {{ progressBarTitle() | translate }}
    </h3>
  </div>
}
