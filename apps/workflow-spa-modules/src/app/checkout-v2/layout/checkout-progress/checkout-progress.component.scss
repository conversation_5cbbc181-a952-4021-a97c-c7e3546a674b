@use 'scss/colors';
@use 'scss/mixins';

:host::ng-deep {
  z-index: 11;
  display: block;

  @include mixins.small-tablet {
    margin-bottom: 0;
  }

  .mdc-linear-progress {
    margin: 12px 0 20px;
    height: 3px;

    @include mixins.small-tablet {
      margin: 0 0 5px;
      height: 10px;
      border: 1px solid var(--header-progress-passed-color);
      border-radius: 5px;
    }

    &__buffer {
      background-color: var(--header-progress-color);

      @include mixins.small-tablet {
        height: 100%;
      }
    }

    &__bar {
      color: var(--header-progress-passed-color);

      @include mixins.small-tablet {
        height: 100%;
      }

      &-inner {
        @include mixins.small-tablet {
          border-top-width: var(--mdc-linear-progress-active-indicator-height, 8px);
        }
      }
    }
  }
}

.checkout-progress-bar {
  &__legend {
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-between;
    margin-top: 13px;

    @include mixins.small-tablet {
      display: none;
    }

    &__item {
      color: var(--header-progress-passed-color);
      font-weight: 500;
      position: relative;

      i {
        position: absolute;
        top: -34px;
        left: calc(50% - 12.5px);
        width: 25px;
        height: 25px;
        background: #fff;
        border: 3px solid var(--header-progress-color);
        border-radius: 50px;

        &::before {
          content: '';
          position: relative;
          top: 4px;
          left: 4px;
          display: block;
          opacity: 0;
          width: 11px;
          height: 11px;
          border-radius: 20px;
          background: var(--header-progress-passed-color);
        }
      }

      &--active {
        i {
          border: 3px solid var(--header-progress-passed-color);

          &::before {
            opacity: 1 !important;
          }
        }
      }

      &:first-child {
        i {
          left: 0px;
        }
      }

      &:last-child {
        i {
          left: auto;
          right: -3px;
        }
      }

      &:not(:first-child):not(:last-child) {
        i {
          display: none;
        }
      }
    }
  }

  &__title {
    font-size: 18px;
    text-align: center;
    margin-top: 0;
    color: var(--header-progress-title-color);

    @include mixins.small-tablet {
      font-size: 12px;
      margin: 0;
    }
  }
}
