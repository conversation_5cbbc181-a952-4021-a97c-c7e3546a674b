@use 'scss/colors';
@use 'scss/mixins';

.checkout {
  width: 100%;

  &__header-wrapper {
    width: 100%;
    background: colors.$white;
    display: flex;
    justify-content: center;
  }

  &__header {
    display: flex;
    align-items: center;
    padding: 12px 0 16px 0;
    max-width: 1140px;
    width: 100%;
    height: 60px;
    box-sizing: border-box;
    justify-content: center;

    @include mixins.small-tablet {
      background: colors.$white;
      padding: 16px 0;
      height: 64px;
    }

    & > a {
      display: flex;
      cursor: pointer;
    }

    &-logo {
      width: 180px;

      @include mixins.small-tablet {
        width: 141px;
      }
    }
  }
}
