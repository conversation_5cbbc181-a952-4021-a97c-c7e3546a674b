import { CommandError, CommandModel, CommandModelResponse, RedirectToUrlCommand } from '@workflow-spa/shared/commands';
import { TrackerEventsEnum } from '../../../../../../shared/models/tracker-events.enum';
import { CheckoutRenderComponentCommand } from '../commands/checkout-render-component.command';
import { CheckoutArguments } from '../guards/reinitialize-order-v2.guard';
import { CheckoutMethodModel } from '../methods/checkout-method.model';

export namespace CheckoutV2Actions {
  // Workflow actions
  export class ExecuteStartOrder {
    static readonly type = '[CheckoutV2] Execute Start Order';
    constructor(public options: CheckoutArguments) {}
  }

  export class ExecuteFindOrder {
    static readonly type = '[CheckoutV2] Execute Find Order';
  }

  export class ExecuteRestartOrder {
    static readonly type = '[CheckoutV2] Execute Restart Order';
    constructor(public options: Partial<CheckoutArguments> = {}) {}
  }

  export class ExecuteErrorRestartOrder {
    static readonly type = '[CheckoutV2] Execute Error Restart Order';
    constructor(
      public error: CommandError,
      public isSessionExpired = false,
    ) {}
  }

  export class ExecuteMethod {
    static readonly type = '[CheckoutV2] Execute Method';
    constructor(public method: CheckoutMethodModel) {}
  }

  export class ProcessCommand {
    static readonly type = '[CheckoutV2] Process Command';
    constructor(
      public response: CommandModelResponse<CommandModel>,
      public method: CheckoutMethodModel,
    ) {}
  }

  // State management actions

  export class SetQueryParams {
    static readonly type = '[CheckoutV2] Set Query Params';
    constructor(public queryParams: { [key: string]: string }) {}
  }

  // Navigation actions
  export class NavigateToCommand {
    static readonly type = '[CheckoutV2] Navigate To Command';
    constructor(public command: CheckoutRenderComponentCommand) {}
  }

  export class RedirectToUrl {
    static readonly type = '[CheckoutV2] Redirect To URL';
    constructor(public command: RedirectToUrlCommand) {}
  }

  // Audit actions
  export class LogWorkflowEvent {
    static readonly type = '[CheckoutV2] Log Workflow Event';
    constructor(public event: TrackerEventsEnum, public context?: any) {}
  }

  // Reset actions
  export class ResetState {
    static readonly type = '[CheckoutV2] Reset State';
  }
}
