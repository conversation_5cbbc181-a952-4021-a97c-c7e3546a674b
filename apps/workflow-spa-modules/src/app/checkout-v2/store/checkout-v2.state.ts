import { Injectable } from '@angular/core';
import { Action, State, StateContext } from '@ngxs/store';
import { Observable, throwError } from 'rxjs';
import { catchError, finalize, first, map, shareReplay, switchMap, tap } from 'rxjs/operators';
import { CommandModel, CommandModelResponse, RedirectToUrlCommand } from '@workflow-spa/shared/commands';
import { sdkLogHelper } from '../../../../../../shared/helpers/sdk-log.helper';
import { CheckoutRenderComponentCommand } from '../commands/checkout-render-component.command';
import { CheckoutPageComponentEnum, componentToRoute } from '../enums/checkout-path-component.enum';
import { CheckoutWorkflowAuditV2Service } from '../services/checkout-workflow-audit-v2.service';
import { CheckoutWorkflowHttpV2Service } from '../services/checkout-workflow-http-v2.service';
import { CheckoutTitlesV2Service } from '../services/checkout-titles-v2.service';
import { CheckoutWorkflowRouterV2Service } from '../services/checkout-workflow-router-v2.service';
import { CheckoutV2Actions } from './checkout-v2.actions';
import { CheckoutV2StateInterface } from './checkout-v2.state.interface';

import { LanguageService } from '@workflow-spa/shared/services/language.service';
import { UserService } from '@workflow-spa/shared/services/user.service';
import { SavePersonalInfoMethod } from '../methods/save-personal-info.method';
import { StartOrderMethod } from '../methods/start-order.method';
import { CheckoutFindOrderMethod } from '../methods/checkout-find-order.method';
import { QueryParamsHelpers } from '../../../../../../shared/helpers/query-params.helpers';
import { CommandErrorCode } from '@workflow-spa/shared/commands';
import { PrescriptionProviderEnum } from '../enums/prescription-provider.enum';
import { filter } from 'rxjs/operators';
import { CheckoutArguments } from '../guards/reinitialize-order-v2.guard';

@State<CheckoutV2StateInterface>({
  name: 'checkoutV2',
  defaults: {
    currentCommand: null,
    currentMethod: null,
    currentError: null,
    isLoading: false,
    isRedirecting: false,
    queryParams: {},
    sessionId: null,
    categoryId: null,
    profileDataIsPassed: false,
  },
})
@Injectable()
export class CheckoutV2State {
  constructor(
    private checkoutWorkflowHttpService: CheckoutWorkflowHttpV2Service,
    private checkoutWorkflowAuditService: CheckoutWorkflowAuditV2Service,
    private checkoutTitlesService: CheckoutTitlesV2Service,
    private checkoutWorkflowRouterService: CheckoutWorkflowRouterV2Service,
    private languageService: LanguageService,
    private userService: UserService,
  ) {
  }

  @Action(CheckoutV2Actions.ExecuteStartOrder)
  executeStartOrder(
    ctx: StateContext<CheckoutV2StateInterface>,
    action: CheckoutV2Actions.ExecuteStartOrder,
  ): Observable<void> {
    const method = new StartOrderMethod({
      locale: this.languageService.currentStore.language_id,
      ...action.options,
      queryParams: location.search,
      sourceUrl: location.href,
    });

    return ctx.dispatch(new CheckoutV2Actions.ExecuteMethod(method));
  }

  @Action(CheckoutV2Actions.ExecuteFindOrder)
  executeFindOrder(
    ctx: StateContext<CheckoutV2StateInterface>,
  ): Observable<void> {
    const queryParams = QueryParamsHelpers.normaliseCheckoutParams();
    const requestParams = {
      sessionId: queryParams.session_id,
      categoryId: queryParams.category_id,
      smartToken: queryParams.smart_token,
      dpg: queryParams.dpg,
      promo: queryParams.promo,
      intent: queryParams?.intent,
      queryParams: location.search,
    };

    const method = new CheckoutFindOrderMethod(requestParams);

    return ctx.dispatch(new CheckoutV2Actions.ExecuteMethod(method)).pipe(
      catchError((error) => {
        if (error.error.code === CommandErrorCode.OrderNotFound) {
          return ctx.dispatch(new CheckoutV2Actions.ExecuteStartOrder({
            ...requestParams,
            prescriptionProvider: (queryParams.prescription_provider as PrescriptionProviderEnum),
          }));
        }
        return throwError(() => error);
      }),
    );
  }

  @Action(CheckoutV2Actions.ExecuteRestartOrder)
  executeRestartOrder(
    ctx: StateContext<CheckoutV2StateInterface>,
    action: CheckoutV2Actions.ExecuteRestartOrder,
  ): Observable<void> {
    const state = ctx.getState();
    const queryParams = state.queryParams || QueryParamsHelpers.queryParams;
    const options = action.options;

    const productId =
      typeof options?.productId !== 'undefined'
        ? options?.productId
        : Number(options?.productId || queryParams.product_id) || null;

    const modificationId =
      typeof options?.modificationId !== 'undefined'
        ? (options as any).modificationId
        : Number(options?.modificationId || queryParams.modification_id) || null;

    const newOptions: CheckoutArguments = {
      prescriptionProvider:
        options?.prescriptionProvider || PrescriptionProviderEnum.online,
      sessionId: options?.sessionId || queryParams.session_id,
      categoryId: Number(options?.categoryId || queryParams.category_id),
      productId,
      modificationId,
      dpg: options?.dpg || queryParams.dpg || null,
      promo: options?.promo || queryParams.promo || null,
      smartToken: options?.smartToken,
    };

    return ctx.dispatch(new CheckoutV2Actions.ExecuteStartOrder(newOptions));
  }

  @Action(CheckoutV2Actions.ExecuteErrorRestartOrder)
  executeErrorRestartOrder(
    ctx: StateContext<CheckoutV2StateInterface>,
    { error, isSessionExpired }: CheckoutV2Actions.ExecuteErrorRestartOrder,
  ) {
    const currQueryParams = QueryParamsHelpers.queryParams;

    if (isSessionExpired && window.location.search.includes('rd_new')) {
      window.history.replaceState(
        {},
        document.title,
        QueryParamsHelpers.getLinkWithoutCertainQueryParams(window.location.href, ['rd_new']),
      );
    }

    return ctx.dispatch(new CheckoutV2Actions.ExecuteRestartOrder({
      smartToken:
        currQueryParams.auth_token &&
        decodeURIComponent(currQueryParams.auth_token),
      dpg:
        (currQueryParams.dpg && decodeURIComponent(currQueryParams.dpg)) ||
        error?.data?.prefilled_coupon,
      promo:
        (currQueryParams.promo && decodeURIComponent(currQueryParams.promo)) ||
        error?.data?.notification_coupon,
      prescriptionProvider:
        error?.data?.preferable_prescription_provider ||
        (currQueryParams.prescription_provider as PrescriptionProviderEnum),
      categoryId: error?.data?.category_id,
      productId: error?.data?.product_id,
    }));
  }

  @Action(CheckoutV2Actions.ExecuteMethod)
  executeMethod(
    ctx: StateContext<CheckoutV2StateInterface>,
    action: CheckoutV2Actions.ExecuteMethod,
  ): Observable<void> {
    ctx.patchState({ isLoading: true, currentMethod: action.method });

    return this.checkoutWorkflowHttpService
      .executeMethod(action.method)
      .pipe(
        tap((command) => {
          this.checkoutWorkflowAuditService.sendAnalytic(action.method, command?.data);
          this.checkoutWorkflowAuditService.logMethods(action.method, command);

          if (action.method instanceof SavePersonalInfoMethod) {
            this.userService.initPartialUserData(action.method.payload.profileInfo);
          }
        }),
        catchError((error) => {
          ctx.patchState({ currentError: error });
          return throwError(() => error);
        }),
        finalize(() => {
          ctx.patchState({ isLoading: false });
        }),
        switchMap((command) => ctx.dispatch(new CheckoutV2Actions.ProcessCommand(command, action.method))),
      );
  }

  @Action(CheckoutV2Actions.ProcessCommand)
  processCommand(
    ctx: StateContext<CheckoutV2StateInterface>,
    action: CheckoutV2Actions.ProcessCommand,
  ) {
    const { data: command, profileDataIsPassed } = action.response;
    const currentState = ctx.getState();

    // Single state update instead of multiple dispatches for better performance
    const stateUpdates: Partial<CheckoutV2StateInterface> = {
      profileDataIsPassed: !!profileDataIsPassed,
      currentError: null, // Clear error
    };

    // Update query parameters and session data in one go
    if (command?.sessionId || command?.categoryId) {
      const updatedQueryParams = { ...currentState.queryParams };

      if (command.sessionId) {
        updatedQueryParams.session_id = command.sessionId;
        stateUpdates.sessionId = command.sessionId;
      }

      if (command.categoryId) {
        updatedQueryParams.category_id = String(command.categoryId);
        stateUpdates.categoryId = command.categoryId;
      }

      stateUpdates.queryParams = updatedQueryParams;
    }

    // Apply all state updates at once
    ctx.patchState(stateUpdates);

    // Only dispatch navigation actions after state is updated
    if (command instanceof RedirectToUrlCommand) {
      ctx.dispatch(new CheckoutV2Actions.RedirectToUrl(command));
    } else if (command instanceof CheckoutRenderComponentCommand) {
      ctx.dispatch(new CheckoutV2Actions.NavigateToCommand(command));
    }
  }


  @Action(CheckoutV2Actions.SetQueryParams)
  setQueryParams(ctx: StateContext<CheckoutV2StateInterface>, action: CheckoutV2Actions.SetQueryParams) {
    const sanitizedParams = { ...action.queryParams };

    delete sanitizedParams['finish_consultation'];
    delete sanitizedParams['start_order'];
    delete sanitizedParams['smartToken'];
    delete sanitizedParams['auth_token'];
    delete sanitizedParams['preferredProducts'];
    delete sanitizedParams['prescription_provider'];
    delete sanitizedParams['is_landing'];
    delete sanitizedParams['openLoginSidebar'];
    delete sanitizedParams['oauthToken'];
    delete sanitizedParams['intent'];

    ctx.patchState({ queryParams: sanitizedParams });
  }

  @Action(CheckoutV2Actions.NavigateToCommand)
  navigateToCommand(ctx: StateContext<CheckoutV2StateInterface>, action: CheckoutV2Actions.NavigateToCommand) {
    const command = action.command;
    const state = ctx.getState();
    const currentCommand = state.currentCommand;

    ctx.patchState({ currentCommand: command });

    this.checkoutTitlesService.initTitles(command.component);

    // Delegate navigation to router service
    this.checkoutWorkflowRouterService.navigateToCommand(command, currentCommand);
  }

  @Action(CheckoutV2Actions.RedirectToUrl)
  redirectToUrl(ctx: StateContext<CheckoutV2StateInterface>, action: CheckoutV2Actions.RedirectToUrl) {
    ctx.patchState({ isRedirecting: true });

    // Get current method from state for redirect processing
    const state = ctx.getState();
    const currentMethod = state.currentMethod;

    // Delegate redirect processing to router service
    this.checkoutWorkflowRouterService.processRedirectCommand(action.command, currentMethod);
  }

  @Action(CheckoutV2Actions.LogWorkflowEvent)
  logWorkflowEvent(ctx: StateContext<CheckoutV2StateInterface>, action: CheckoutV2Actions.LogWorkflowEvent) {
    const state = ctx.getState();
    const category_id = state.currentCommand?.categoryId || state.queryParams.category_id;
    const context = {
      category_id,
      query_params: window.location.search,
      ...(action.context || {}),
    };

    sdkLogHelper(action.event, context);
  }

  @Action(CheckoutV2Actions.ResetState)
  resetState(ctx: StateContext<CheckoutV2StateInterface>) {
    ctx.setState({
      currentCommand: null,
      currentMethod: null,
      currentError: null,
      isLoading: false,
      isRedirecting: false,
      queryParams: {},
      sessionId: null,
      categoryId: null,
      profileDataIsPassed: false,
    });
  }
}
