import { HttpErrorResponse } from '@angular/common/http';
import { CheckoutRenderComponentCommand } from '../commands/checkout-render-component.command';
import { CheckoutMethodModel } from '../methods/checkout-method.model';

export interface CheckoutV2StateInterface {
  // Current workflow state
  currentCommand: CheckoutRenderComponentCommand | null;
  currentMethod: CheckoutMethodModel<any> | null;

  // Error handling
  currentError: HttpErrorResponse | null;

  // Loading states
  isLoading: boolean;
  isRedirecting: boolean;

  // Session data
  queryParams: { [key: string]: string };
  sessionId: string | null;
  categoryId: number | null;

  // User state
  profileDataIsPassed: boolean;
}
