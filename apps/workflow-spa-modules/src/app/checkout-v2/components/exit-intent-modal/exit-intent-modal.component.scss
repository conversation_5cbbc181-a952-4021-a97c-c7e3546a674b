@use 'scss/colors';
@use 'scss/mixins';

:host {
  min-width: 460px;

  @include mixins.small-tablet() {
    min-width: 343px;
  }
}

.exit_intent-modal {
  border-radius: 10px;
  padding: 32px 32px 16px 32px;
  background-image: url('../../../../assets/img/promo-modal_bg.jpg');
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  height: 440px;
  font-family: Roboto, sans-serif;
  color: colors.$white;
  position: relative;

  &::after {
    position: absolute;
    content: '';
    height: 100%;
    width: 100%;
    top: 0;
    left: 0;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0) 100%);
  }

  @include mixins.small-tablet {
    height: 448px;
    padding: 24px 16px 8px 16px;
  }

  &__inner {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  &__content {
    z-index: 2;

    &--title {
      text-align: center;
      font-size: 22px;
      font-weight: 700;
      line-height: 30px;
      margin: 0;
    }

    &--text {
      text-align: center;
      text-shadow: 0 15px 30px rgba(9, 81, 170, 0.10), 0 4px 15px rgba(0, 0, 0, 0.10);
      font-size: 16px;
      font-weight: 400;
      line-height: 24px;
      margin-top: 8px;
      margin-bottom: 0;
    }
  }

  &__buttons {
    &--button {
      height: 45px !important;
      width: 100%;
      margin: 0 auto;
      font-weight: 600 !important;
      font-size: 14px !important;
      padding: 0 !important;
    }

    &--trustedshops {
      margin-top: 8px;
      padding: 4px 0;
      display: flex;
      align-items: center;
      justify-content: center;
      color: inherit;
      text-decoration: none;
      font-weight: 200;
      flex-wrap: wrap;

      .trustedshops-hp {
        margin-right: 8px;
      }

      .trustedshops-stars {
        position: relative;
        margin-right: 8px;

        &__full {
          position: absolute;
          overflow: hidden;

          img {
            height: 20px;
            margin-right: 8px;
            max-width: unset;
          }
        }
      }

      .trustedshops-review {
        font-family: Roboto, sans-serif;
        font-size: 14px;
        font-weight: 400;
        line-height: 20px;
      }
    }
  }
}

.list {
  list-style: disc;
  margin-left: 30px;
  text-align: left;
  line-height: 1.7em;
  margin-top: 5px;

  &__item-content {
    position: relative;
    left: -5px;
  }
}

::ng-deep {
  .promocode-modal-wrap {
    width: 100%;

    .mat-mdc-dialog-surface {
      padding: 0;
    }

    .mat-mdc-dialog-container {
      border-radius: 8px !important;
    }
  }
}
