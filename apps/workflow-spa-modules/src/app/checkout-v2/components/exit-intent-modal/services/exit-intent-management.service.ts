import { Injectable, signal } from '@angular/core';
import { fromEvent, Observable, Subject, Subscription } from 'rxjs';
import { filter, first, map, pairwise, takeUntil, tap } from 'rxjs/operators';
import { Store } from '@ngxs/store';
import { sdkLogHelper } from '../../../../../../../../shared/helpers/sdk-log.helper';
import { TrackerEventsEnum } from '../../../../../../../../shared/models/tracker-events.enum';
import { TrustedShopsModel } from '../../../models/trusted-shops.model';
import { CheckPaymentMethod } from '../../../methods/check-payment.method';
import { TrustedShopsService } from '../../../services/trusted-shops.service';
import { CheckoutPageComponentEnum } from '../../../enums/checkout-path-component.enum';
import { CheckoutV2Selectors } from '../../../store';
import { CheckoutRenderComponentCommand } from '../../../commands/checkout-render-component.command';
import { CheckoutMethodModel } from '../../../methods/checkout-method.model';
import { DialogRef, DialogService } from '@apo/ui-kit';
import { ExitIntentModalComponent } from '../exit-intent-modal.component';

@Injectable({ providedIn: 'root' })
export class ExitIntentManagementService {
  readonly isModalActive = signal<boolean>(false);
  private mouseover$: Observable<Event>;
  private mouseoverSubscription: Subscription;
  private reInitMouseWatcherTimeout: any;
  private limitToShow = 1;
  private readonly showCounter = signal<number>(0);
  private readonly tsInfo = signal<TrustedShopsModel | null>(null);
  private destroy$ = new Subject();
  private readonly mobileViewWidth = 768;

  get isMobile() {
    return window.innerWidth <= this.mobileViewWidth;
  }

  constructor(
    private store: Store,
    private trustedShopService: TrustedShopsService,
    private dialogService: DialogService,
  ) {
    this.initTSInfo();
  }

  private get isLoading(): boolean {
    return this.store.selectSnapshot(CheckoutV2Selectors.isLoading) || false;
  }
  private get currentMethod(): CheckoutMethodModel | null {
    return this.store.selectSnapshot(CheckoutV2Selectors.currentMethod) || null;
  }
  private get currentCommand(): CheckoutRenderComponentCommand | null {
    return this.store.selectSnapshot(CheckoutV2Selectors.currentCommand) || null;
  }

  initMouseWatcher(): void {
    const header = document.getElementById('checkout-header');
    this.mouseover$ = fromEvent(header, 'mousemove');

    this.mouseoverSubscription = this.mouseover$
      .pipe(
        takeUntil(this.destroy$),
        map((v: MouseEvent) => v.clientY),
        pairwise(),
        map(([prev, curr]) => curr < prev),
        filter((mouseLeaving: boolean) => mouseLeaving),
      )
      .subscribe(() => this.handleMouseLeaving());
  }

  destroyExitIntentLogicSubscription(): void {
    this.destroy$.next(true);
    this.destroy$.complete();
    clearTimeout(this.reInitMouseWatcherTimeout);
  }

  showModal() {
    if (this.isModalActive()) {
      return true;
    }

    const modal = this.createModal();

    modal?.afterClosed()
      .pipe(tap((closedByCTAClick: boolean) => this.trackClosedEvent(closedByCTAClick)))
      .subscribe(() => this.isModalActive.set(false));
  }

  initTSInfo() {
    this.trustedShopService.getTrustedShopsInfo()
      .pipe(takeUntil(this.destroy$))
      .subscribe((model: TrustedShopsModel) => this.tsInfo.set(model));
  }

  private createModal(): DialogRef<any> | null {
    const isLoading = this.isLoading;
    const currentMethod = this.currentMethod;
    const currentCommand = this.currentCommand;

    if (isLoading &&
      !(currentMethod instanceof CheckPaymentMethod) &&
      currentCommand &&
      ![CheckoutPageComponentEnum.Payment].includes(currentCommand.component)
    ) {
      return null;
    }

    if (this.showCounter() >= this.limitToShow) {
      return null;
    }

    this.showCounter.update(v => v + 1);
    this.isModalActive.set(true);
    return this.dialogService.open(ExitIntentModalComponent, { data: this.tsInfo() as TrustedShopsModel });
  }

  private handleMouseLeaving(): void {
    const modal = this.createModal();

    modal?.afterClosed()
      .pipe(
        tap((closedByCTAClick: boolean) => this.trackClosedEvent(closedByCTAClick)),
        first(),
      )
      .subscribe(() => {
        this.isModalActive.set(false);
        this.mouseoverSubscription.unsubscribe();
        this.reInitMouseWatcherTimeout = setTimeout(() => this.initMouseWatcher(), 7000);
      });
  }

  private trackClosedEvent(closedByCTAClick: boolean) {
    sdkLogHelper(
      closedByCTAClick
        ? TrackerEventsEnum.exitIntentPopupClosedByCTAClick
        : TrackerEventsEnum.exitIntentPopupClosedByOutsideClick,
      { query_params: window.location.search },
    );
  }
}
