import { ChangeDetectionStrategy, Component, Inject, OnInit } from '@angular/core';
import { AbstractComponent } from '@workflow-spa/shared/helpers';
import { sdkLogHelper } from '../../../../../../../shared/helpers/sdk-log.helper';
import { TrackerEventsEnum } from '../../../../../../../shared/models/tracker-events.enum';
import { TrustedShopsModel } from '../../models/trusted-shops.model';
import { TranslatePipe } from '@ngx-translate/core';
import { DIALOG_DATA, DialogRef, ModalDefaultModule } from '@apo/ui-kit';

@Component({
  selector: 'apo-promocode-modal',
  templateUrl: './exit-intent-modal.component.html',
  styleUrls: ['./exit-intent-modal.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    TranslatePipe,
    ModalDefaultModule,
  ],
})
export class ExitIntentModalComponent extends AbstractComponent implements OnInit {
  constructor(
    private dialogRef: DialogRef<ExitIntentModalComponent>,
    @Inject(DIALOG_DATA) public tsInfo: TrustedShopsModel,
  ) {
    super();
  }

  close(closedByCTA: boolean): void {
    this.dialogRef.close(closedByCTA);
  }

  ngOnInit() {
    sdkLogHelper(
      TrackerEventsEnum.exitIntentPopupShown,
      { query_params: window.location.search },
    );
  }
}
