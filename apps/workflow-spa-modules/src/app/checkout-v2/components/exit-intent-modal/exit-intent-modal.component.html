<kit-modal-default-content>
  <div class="exit_intent-modal" data-test="exit-intent-modal-main-wrapper">
    <div class="exit_intent-modal__inner">
      <div class="exit_intent-modal__content">
        <h3 class="exit_intent-modal__content--title" data-test="exit-intent-modal-title">
          {{ 'checkoutV2ExitIntentModal.main.title' | translate }}
        </h3>
        <p class="exit_intent-modal__content--text" data-test="exit-intent-modal-text">
          {{ 'checkoutV2ExitIntentModal.main.text' | translate }}
        </p>
      </div>

      <div class="exit_intent-modal__buttons">
        <button
          data-test="exit-intent-modal-cta"
          class="exit_intent-modal__buttons--button"
          (click)="close(true)">
          {{ 'checkoutV2ExitIntentModal.main.cta' | translate }}
        </button>

        <div class="exit_intent-modal__buttons--trustedshops">
          <img
            class="trustedshops-hp"
            src="/img/trusted-shops-gmbh-logo-vector.svg"
            alt="trustedshops logo"
            loading="lazy"
            width="58"
            height="24"
          />
          <div class="trustedshops-stars">
            <div [style.width]="tsInfo?.width + '%'" class="trustedshops-stars__full">
              <img
                src="/img/ts-stars-group-orange.svg"
                alt="ts-stars-group-yellow"
                loading="lazy"
                width="80"
                height="13"
              />
            </div>
            <div>
              <img
                src="/img/ts-stars-group-yellow-empty.svg"
                alt="ts-stars-group-yellow"
                width="80"
                height="13"
                loading="lazy"
              />
            </div>
          </div>
          <span class="trustedshops-review"
                data-test="exit-intent-modal-trustedshops-review-count">( {{ tsInfo?.currentLangReviewCount }} )</span>
        </div>
      </div>
    </div>
  </div>
</kit-modal-default-content>
