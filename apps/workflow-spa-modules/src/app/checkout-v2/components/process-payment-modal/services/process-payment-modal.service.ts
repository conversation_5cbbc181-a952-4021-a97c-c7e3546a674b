import { Injectable, OnDestroy } from '@angular/core';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { Store } from '@ngxs/store';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { CheckoutEventsService } from '../../../../../../core/services/checkout-events.service';
import { ProcessPaymentModalComponent } from '../process-payment-modal.component';
import { PaymentDetailsState } from '../../../store/payment-details.state';
import { LanguageService } from '@workflow-spa/shared/services';

@Injectable()
export class ProcessPaymentModalService implements OnDestroy {
  private openedModal: MatDialogRef<ProcessPaymentModalComponent>;

  private paymentLeavingIsBlocked: boolean;

  private ngUnsubscribe$ = new Subject();

  constructor(
    private store: Store,
    private dialog: MatDialog,
    private checkoutEventsService: CheckoutEventsService,
    private languageService: LanguageService,
  ) {}

  init() {
    this.initInitialData();
    this.initLogoClickHandler();
    this.initRedirectHandler();
  }

  openIfPaymentIsProcessing(): Observable<void> | void {
    if (!this.paymentLeavingIsBlocked) {
      this.close();
      return;
    }

    if (this.openedModal) {
      return this.openedModal.afterClosed();
    }

    this.openedModal = this.dialog.open(
      ProcessPaymentModalComponent,
      {
        width: '656px',
        maxHeight: '100vh',
        backdropClass: 'blue-backdrop',
        panelClass: ['modern-panel', 'modern-panel--large'],
        autoFocus: false,
        data: {},
      },
    );

    this.openedModal.afterClosed()
      .pipe(takeUntil(this.ngUnsubscribe$))
      .subscribe(() => this.openedModal = null);

    return this.openedModal.afterClosed();
  }

  close() {
    this.openedModal?.close();
  }

  private initInitialData() {
    this.store
      .select(PaymentDetailsState.paymentLeavingIsBlocked)
      .pipe(takeUntil(this.ngUnsubscribe$))
      .subscribe(x => this.paymentLeavingIsBlocked = x);
  }

  private initLogoClickHandler() {
    this.checkoutEventsService.logoClick$
      .pipe(takeUntil(this.ngUnsubscribe$))
      .subscribe(() => {
        const modalRef = this.openIfPaymentIsProcessing();

        if (modalRef) {
          this.checkoutEventsService.preventRedirect();
        }
      });
  }

  private initRedirectHandler() {
    this.checkoutEventsService.redirectReject$
      .pipe(takeUntil(this.ngUnsubscribe$))
      .subscribe(() => this.openIfPaymentIsProcessing());
  }

  ngOnDestroy() {
    this.ngUnsubscribe$.next(true);
    this.ngUnsubscribe$.complete();
    this.close();
  }
}
