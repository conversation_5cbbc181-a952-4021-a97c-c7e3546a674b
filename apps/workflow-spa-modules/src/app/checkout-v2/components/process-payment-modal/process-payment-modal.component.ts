import { ChangeDetectionStrategy, Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { AddressInfoModel } from '@workflow-spa/shared/models';
import { TranslatePipe } from '@ngx-translate/core';
import { ProcessPaymentLoaderComponent } from './components/process-payment-loader/process-payment-loader.component';
import { FormModule } from '../../../shared/modules/form/form.module';

@Component({
    selector: 'apo-process-payment-modal',
    templateUrl: 'process-payment-modal.component.html',
    styleUrls: ['process-payment-modal.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
      TranslatePipe,
      ProcessPaymentLoaderComponent,
      FormModule,
    ],
})
export class ProcessPaymentModalComponent {
  constructor(
    private dialogRef: MatDialogRef<AddressInfoModel>,
    @Inject(MAT_DIALOG_DATA) public data,
  ) {}


  onCloseClick() {
    this.dialogRef.close();
  }

  onContinueClick() {
    this.dialogRef.close();
  }
}
