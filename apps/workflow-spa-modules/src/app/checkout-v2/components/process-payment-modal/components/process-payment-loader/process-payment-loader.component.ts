import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { TranslatePipe } from '@ngx-translate/core';

@Component({
    selector: 'apo-process-payment-loader',
    templateUrl: 'process-payment-loader.component.html',
    styleUrls: ['process-payment-loader.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [TranslatePipe],
})
export class ProcessPaymentLoaderComponent {
}
