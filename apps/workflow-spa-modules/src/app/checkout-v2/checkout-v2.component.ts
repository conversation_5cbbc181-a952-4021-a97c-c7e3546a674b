import { ChangeDetectionStrategy, Component } from '@angular/core';
import { AbstractComponent } from '@workflow-spa/shared/helpers';
import { RouterOutlet } from '@angular/router';
import { CheckoutHeaderComponent } from './layout/checkout-header/checkout-header.component';
import { CheckoutProgressComponent } from './layout/checkout-progress/checkout-progress.component';
import { CheckoutFooterComponent } from './layout/checkout-footer/checkout-footer.component';

@Component({
  selector: 'apo-checkout-v2',
  templateUrl: 'checkout-v2.component.html',
  styleUrls: ['./checkout-v2.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    RouterOutlet,
    CheckoutHeaderComponent,
    CheckoutProgressComponent,
    CheckoutFooterComponent,
  ],
})
export class CheckoutV2Component extends AbstractComponent {}
