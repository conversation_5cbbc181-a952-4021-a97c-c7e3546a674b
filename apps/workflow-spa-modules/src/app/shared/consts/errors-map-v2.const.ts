export const requestPhoneV2ModalErrorsMap = {
  'SIGN_UP_FORM.VALIDATION.PHONE.INVALID': 'successPageV2.main.requestPhoneModal.requestPhoneFormErrors.invalid',
  'SIGN_UP_FORM.VALIDATION.PHONE.MIN_LENGTH': 'successPageV2.main.requestPhoneModal.requestPhoneFormErrors.minLength',
  'SIGN_UP_FORM.VALIDATION.PHONE.MAX_LENGTH': 'successPageV2.main.requestPhoneModal.requestPhoneFormErrors.maxLength',
}

export const promoCodeV2ErrorsMap = (key: 'paymentPage' | 'promocode') => {
  return {
    'VALIDATION.PROMO_CODE.INVALID': `${key}.main.errors.promoCodeErrors.main.invalid`,
    'VALIDATION.PROMO_CODE.EXPIRED': `${key}.main.errors.promoCodeErrors.main.expired`,
    'VALIDATION.PROMO_CODE.MIN_AMOUNT': `${key}.main.errors.promoCodeErrors.main.minOrderAmount`,
    'VALIDATION.PROMO_CODE.SUBSCRIPTION_ONLY': `${key}.main.errors.promoCodeErrors.main.forSubscriptions`,
    'VALIDATION.PROMO_CODE.WRONG_CATEGORY': `${key}.main.errors.promoCodeErrors.main.wrongCategory`,
    'VALIDATION.PROMO_CODE.WRONG_PRODUCT': `${key}.main.errors.promoCodeErrors.main.wrongProduct`,
    'VALIDATION.PROMO_CODE.WRONG_MODIFICATION': `${key}.main.errors.promoCodeErrors.main.wrongModification`,
    'VALIDATION.PROMO_CODE.LIMIT_PER_CUSTOMER_USAGE': `${key}.main.errors.promoCodeErrors.main.limitPerCustomerUsage`,
    'VALIDATION.PROMO_CODE.LIMIT_ACTIVATION': `${key}.main.errors.promoCodeErrors.main.limitActivation`,
    'VALIDATION.PROMO_CODE.REGULAR_ORDER_ONLY': `${key}.main.errors.promoCodeErrors.main.regularOrderOnly`,
  };
};
