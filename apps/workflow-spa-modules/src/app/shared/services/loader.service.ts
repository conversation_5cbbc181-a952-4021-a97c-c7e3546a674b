import { Injectable } from '@angular/core';
import { LoaderModel } from '@workflow-spa/shared/models/loader.model';

@Injectable({ providedIn: 'root' })
export class LoaderService {
  loadersQuery: LoaderModel[] = [];
  isShowing = false;

  addLoader() {
    this.loadersQuery.push(new LoaderModel());
    this.isShowing = true;
  }

  removeLoader() {
    this.loadersQuery.shift();

    if (this.isLoaderQueryEmpty()) {
      this.isShowing = false;
    }
  }

  isLoaderQueryEmpty() {
    return this.loadersQuery.length === 0;
  }
}
