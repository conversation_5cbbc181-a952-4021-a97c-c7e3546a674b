version: '3.8'
services:
  cache:
    image: redis:6.2-alpine
    restart: always
    ports:
      - '6379:6379'
    command: redis-server --save 20 1 --loglevel warning --requirepass hunter2
    volumes:
      - cache:/data
  application:
    container_name: website
    build: .
    image: website
    env_file:
      - ./.env
      - ./.env.messengers
      - ./.env.stores
      - ./.env.view-settings
      - ./.env.google-maps
    depends_on:
      - cache
    ports:
      - 80:80
    links:
      - cache
    volumes:
      - ./:/src
volumes:
  cache:
    driver: local
