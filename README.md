# Apomeds Front Store project
## Development
Application starts on 3000 port with live reload
1. `git clone https://git.apomeds.digital/apomeds/apomeds-front-store.git`
2. `cd apomeds-front-store`
3.  `cp .env.stage .env`
3. `npm i`
4. `npm run start`
## Build and run with docker
<PERSON><PERSON><PERSON> uses 80 port
1. `cd apomeds-front-store`
2. `docker-compose up --build`

### Documentation of Dynamic Form - controls, configs and etc.
https://git.apomeds.digital/apomeds/apomeds-crm/-/blob/master/apps/backoffice/src/app/health-and-go-cms/README.md
