import { ConfigInterface } from '../config.interface';
import { BlogArticlePageDefaultSchema } from './articles/default';
import { BlogArticlePageENSchema } from './articles/en';
import { BlogArticlePageDESchema } from './articles/de';
import { BlogArticlePageCHSchema } from './articles/ch';
import { BlogArticlePagePTSchema } from './articles/pt';
import { BlogArticlePageDKSchema } from './articles/dk';
import { BlogArticlePageSESchema } from './articles/se';

export const blogArticle: ConfigInterface[] = [
  {
    id: 'blog-article-page',
    enabled: true,
    name: '[COMMON] Blog article page template',
    templates: [
      {
        locale: 'default',
        schema: BlogArticlePageDefaultSchema
      },
      {
        locale: 'en-GB',
        schema: BlogArticlePageENSchema
      },
      {
        locale: 'de-DE',
        schema: BlogArticlePageDESchema
      },
      {
        locale: 'de-CH',
        schema: BlogArticlePageCHSchema
      },
      {
        locale: 'pt-PT',
        schema: BlogArticlePagePTSchema
      },
      {
        locale: 'da-DK',
        schema: BlogArticlePageDKSchema
      },
      {
        locale: 'sv-SE',
        schema: BlogArticlePageSESchema
      },
      {
        locale: 'nl-NL',
        schema: BlogArticlePageDefaultSchema
      }
    ]
  }
];
