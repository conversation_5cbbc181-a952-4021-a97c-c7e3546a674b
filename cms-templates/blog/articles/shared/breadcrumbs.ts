import { TemplatesTabItemInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';

export const breadcrumbsSchema: TemplatesTabItemInterface = {
  'type': 'breadcrumbs',
  'field': 'breadcrumbs',
  'title': 'Parent page. (for breadcrumbs)',
  'validations': {
    'isRequired': true
  },
  "defaultValue": {
    "id": 190,
    "type": "relation",
    "entity": "breadcrumbs"
  }
}
