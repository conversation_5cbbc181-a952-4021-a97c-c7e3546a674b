import { TemplatesViewConfigInterface } from '../../../apps/website/src/app/api/v1/templates/templates.interface';
import { seoTitleSchema } from './shared/seoTitle';
import { seoDescriptionSchema } from './shared/seoDescription';
import { otherSocialSchema } from './shared/otherSocial';
import { otherContactsSchema } from './shared/otherContacts';
import { headerSchema } from './shared/header';
import { breadcrumbsSchema } from './shared/breadcrumbs';
import { titleSchema } from './shared/title';
import { slugSchema } from './shared/slug';
import { postImageSchema } from './shared/postImage';
import { footerSchema } from './shared/footer';
import { otherWebcomponents } from './shared/otherWebcomponents';
import { ChangefreqEnum, PriorityEnum, seoSitemapSchema } from '../../shared/seo-sitemap-schema';
import { isDeletedSchema } from './shared/isDeleted';

export const BlogArticlePageDKSchema: TemplatesViewConfigInterface = {
  'main': [
    headerSchema,
    breadcrumbsSchema,
    titleSchema,
    slugSchema,
    isDeletedSchema,
    postImageSchema,
    {
      'type': 'group',
      'field': 'experts',
      'items': [
        {
          'type': 'group',
          'field': 'author',
          'items': [
            {
              'type': 'string',
              'field': 'authorLabel',
              'title': 'Author label text',
              'defaultValue': 'Forfatter'
            },
            {
              'type': 'expertsList',
              'field': 'authorEntity',
              'title': 'Authors'
            }
          ],
          'title': 'Author'
        },
        {
          'type': 'group',
          'field': 'reviewer',
          'items': [
            {
              'type': 'string',
              'field': 'reviewerLabel',
              'title': 'Reviewer label text',
              'defaultValue': 'Revideret'
            },
            {
              'type': 'expert',
              'field': 'reviewerEntity',
              'title': 'Reviewer'
            },
            {
              'type': 'datepicker',
              'field': 'reviewAt',
              'title': 'Reviewed at'
            }
          ],
          'title': 'Reviewer'
        }
      ],
      'title': 'Author and Reviewer'
    },
    {
      'type': 'group',
      'field': 'content',
      'items': [
        {
          'type': 'string',
          'field': 'backButtonLabel',
          'title': 'Back Button Label (mobile view)',
          'defaultValue': 'Nyttige artikler'
        },
        {
          'type': 'string',
          'field': 'title',
          'title': 'Title'
        },
        {
          'type': 'code',
          'field': 'description',
          'title': 'Teaser'
        },
        {
          'type': 'code',
          'field': 'mainContent',
          'title': 'Main Content'
        },
        {
          'type': 'string',
          'field': 'createdAtLabel',
          'title': 'Created At Label',
          'defaultValue': 'Udgivelsesdato'
        },
        {
          'type': 'string',
          'field': 'updatedAtLabel',
          'title': 'Updated At Label',
          'defaultValue': 'Sidst ændret'
        },
        {
          'type': 'string',
          'field': 'reviewAtLabel',
          'title': 'Review At Label',
          'defaultValue': 'Sidst revideret'
        },
        {
          'type': 'string',
          'field': 'ctaLabel',
          'title': 'CTA Label',
          'defaultValue': 'Anmod om recept'
        },
        {
          'type': 'string',
          'field': 'ctaUrl',
          'title': 'CTA URL',
          'defaultValue': '/dk/consultation?category_id=30&start_order=true&prescription_provider=online'
        }
      ],
      'title': 'Content'
    },
    footerSchema
  ],
  'seo': [
    seoTitleSchema,
    seoDescriptionSchema,
    seoSitemapSchema(ChangefreqEnum.daily, PriorityEnum.zeroPointEight)
  ],
  "other": [
    otherSocialSchema,
    otherContactsSchema,
    otherWebcomponents,
  ]
};
