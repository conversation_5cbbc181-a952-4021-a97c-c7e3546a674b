import { ConfigInterface } from '../config.interface';
import { DisclaimerDefaultSchema } from './disclaimer/default';
import { OrderStatusDefaultSchema } from './order-status/default';
import { SubscriptionStatusDefaultSchema } from './subscription-status/default';
import { SafeAndSecureFeatureDefault } from './safe-and-secure/default';
import { HowItWorksDefaultSchema } from './how-it-works/default';
import { treatmentsDefaultSchema } from './treatments/default';
import { MainFooterDefaultSchema } from './main-footer/default';
import { MainHeaderDefaultSchema } from './main-header/default';
import { MainTreatmentSidebarDefaultSchema } from './treatment-sidebar/default';
import { ContactsDefaultSchema } from './contacts/default';
import { SocialConfigDefaultSchema } from './social-links/default';
import { PromotionBannerDefaultSchema } from './promotion-banner/default';
import { webcomponentsDefaultSchema } from './webcomponents/default';
import { webcomponentWelcomeModalDefaultSchema } from './webcomponent-welcome-popup/default';
import { webcomponentOtcPromocodeNotificationDefaultSchema } from './webcomponent-otc-promocode-notification/default';
import { CookiePolicyDefaultSchema } from './cookie-policy/default';
import { webcomponentVerificationModalDefaultSchema } from './webcomponent-verification-modal/default';
import { webcomponentLoginSidebarDefaultSchema } from "./webcomponent-login-sidebar/default";
import { webcomponentQuickProductSearchDefaultSchema } from "./webcomponent-quick-product-search/default";
import { webcomponentListSliderDefaultSchema } from './webcomponent-list-slider/default';
import { InvalidAddressModalDefaultSchema } from './invalid-address-modal/default';
import { SuggestAddressModalDefaultSchema } from './suggest-address-modal/default';
import { webcomponentImgSliderDefaultSchema } from './webcomponent-img-slider/default';
import { webcomponentCantOrderByGenderDefaultSchema } from './webcomponent-cant-order-by-gender/default';
import { suggestedPromoDefaultSchema } from './suggested-promo/default';
import { PageSummaryDefaultSchema } from './page-summary/default';
import { OfflineFlowPopupDefaultSchema } from './offline-flow-popup/default';
import { DeliveryInfoModalDefaultSchema } from './delivery-info-modal/default';
import { webcomponentHeaderPromoDefaultSchema } from './webcomponent-header-promo/default';
import { webComponentBackToStockModalDefaultSchema } from './webcomponent-back-to-stock-modal/default';

export const feature: ConfigInterface[] = [
  {
    id: 'disclaimer-template',
    enabled: true,
    name: '[CATALOG] Disclaimer template',
    templates: [
      {
        locale: 'default',
        schema: DisclaimerDefaultSchema
      }
    ]
  },
  {
    id: 'order-status',
    enabled: true,
    name: '[PROFILE] Order status template',
    templates: [
      {
        locale: 'default',
        schema: OrderStatusDefaultSchema
      }
    ]
  },
  {
    id: 'subscription-status',
    enabled: true,
    name: '[PROFILE] Subscription status template',
    templates: [
      {
        locale: 'default',
        schema: SubscriptionStatusDefaultSchema
      }
    ]
  },
  {
    id: 'safe-and-secure',
    enabled: true,
    name: '[CATALOG] Safe and Secure template',
    templates: [
      {
        locale: 'default',
        schema: SafeAndSecureFeatureDefault
      }
    ]
  },
  {
    id: 'how-it-works',
    enabled: true,
    name: '[SHARED] How It Works template',
    templates: [
      {
        locale: 'default',
        schema: HowItWorksDefaultSchema,
      }
    ]
  },
  {
    id: 'treatments',
    enabled: true,
    name: '[WEBSITE] Treatments template',
    templates: [
      {
        locale: 'default',
        schema: treatmentsDefaultSchema
      }
    ]
  },
  {
    id: 'footer-template',
    enabled: true,
    name: '[FOOTER] Main footer template',
    templates: [
      {
        locale: 'default',
        schema: MainFooterDefaultSchema
      }
    ]
  },
  {
    id: 'header-template',
    enabled: true,
    name: '[HEADER] Main header template',
    templates: [
      {
        locale: 'default',
        schema: MainHeaderDefaultSchema
      }
    ]
  },
  {
    id: 'treatment-sidebar',
    enabled: true,
    name: '[TREATMENT SIDEBAR] Treatment sidebar template',
    templates: [
      {
        locale: 'default',
        schema: MainTreatmentSidebarDefaultSchema
      }
    ]
  },
  {
    id: 'contacts',
    enabled: true,
    name: '[CONTACTS] Main contacts template',
    templates: [
      {
        locale: 'default',
        schema: ContactsDefaultSchema
      }
    ]
  },
  {
    id: 'social-links',
    enabled: true,
    name: '[SOCIAL LINKS] Social links template',
    templates: [
      {
        locale: 'default',
        schema: SocialConfigDefaultSchema
      }
    ]
  },
  {
    id: 'promotion-banner',
    enabled: true,
    name: '[FEATURE] Promotion banner',
    templates: [
      {
        locale: 'default',
        schema: PromotionBannerDefaultSchema
      }
    ]
  },
  {
    id: 'webcomponents',
    enabled: true,
    name: '[Webcomponents] Webcomponents main component',
    templates: [
      {
        locale: 'default',
        schema: webcomponentsDefaultSchema
      }
    ]
  },
  {
    id: 'webcomponents-quick-product-search',
    enabled: true,
    name: '[Webcomponents] Webcomponents quick product search',
    templates: [
      {
        locale: 'default',
        schema: webcomponentQuickProductSearchDefaultSchema
      }
    ]
  },
  {
    id: 'webcomponentWelcomeModal',
    enabled: true,
    name: '[Webcomponents] Webcomponents welcome modal',
    templates: [
      {
        locale: 'default',
        schema: webcomponentWelcomeModalDefaultSchema
      }
    ]
  },
  {
    id: 'webcomponentVerificationModal',
    enabled: true,
    name: '[Webcomponents] Verification Modal',
    templates: [
      {
        locale: 'default',
        schema: webcomponentVerificationModalDefaultSchema
      }
    ]
  },
  {
    id: 'webcomponents-login-sidebar',
    enabled: true,
    name: '[Webcomponents] Webcomponents login sidebar',
    templates: [
      {
        locale: 'default',
        schema: webcomponentLoginSidebarDefaultSchema
      }
    ]
  },
  {
    id: 'webcomponentOtcPromocodeNotification',
    enabled: true,
    name: '[Webcomponents] OTC Promocode Notification',
    templates: [
      {
        locale: 'default',
        schema: webcomponentOtcPromocodeNotificationDefaultSchema
      }
    ]
  },
  {
    id: 'webcomponents-cookie-policy',
    enabled: true,
    name: '[Webcomponents] Cookie policy',
    templates: [
      {
        locale: 'default',
        schema: CookiePolicyDefaultSchema
      }
    ]
  },
  {
    id: 'webcomponentsListSlider',
    enabled: true,
    name: '[Webcomponents] Webcomponents list slider',
    templates: [
      {
        locale: 'default',
        schema: webcomponentListSliderDefaultSchema
      }
    ]
  },
  {
    id: 'invalidAddressModal',
    enabled: true,
    name: '[SHARED] Invalid Address Modal',
    templates: [
      {
        locale: 'default',
        schema: InvalidAddressModalDefaultSchema
      }
    ]
  },
  {
    id: 'deliveryInfoModal',
    enabled: true,
    name: '[SHARED] Delivery Info Modal',
    templates: [
      {
        locale: 'default',
        schema: DeliveryInfoModalDefaultSchema,
      }
    ]
  },
  {
    id: 'webcomponentImgSlider',
    enabled: true,
    name: '[Webcomponents] Img Slider',
    templates: [
      {
        locale: 'default',
        schema: webcomponentImgSliderDefaultSchema
      }
    ]
  },
  {
    id: 'suggestAddressModal',
    enabled: true,
    name: '[SHARED] Suggest Address Modal',
    templates: [
      {
        locale: 'default',
        schema: SuggestAddressModalDefaultSchema
      }
    ]
  },
  {
    id: 'webcomponentBackToStockModal',
    enabled: true,
    name: '[Webcomponents] Back to stock modal',
    templates: [
      {
        locale: 'default',
        schema: webComponentBackToStockModalDefaultSchema,
      },
    ],
  },
  {
    id: 'webcomponentCantOrderByGenderDefaultSchema',
    enabled: true,
    name: '[Webcomponents] Cant order by gender',
    templates: [
      {
        locale: 'default',
        schema: webcomponentCantOrderByGenderDefaultSchema,
      },
    ],
  },
  {
    id: 'webcomponentHeaderPromoDefaultSchema',
    enabled: true,
    name: '[Webcomponents] Header Promo',
    templates: [
      {
        locale: 'default',
        schema: webcomponentHeaderPromoDefaultSchema,
      },
    ],
  },
  {
    id: 'suggestedPromoDefaultSchema',
    enabled: true,
    name: '[WEBSITE] Suggested promo',
    templates: [
      {
        locale: 'default',
        schema: suggestedPromoDefaultSchema,
      },
    ],
  },
  {
    id: 'pageSummary',
    enabled: true,
    name: '[FEATURE] Page summary',
    templates: [
      {
        locale: 'default',
        schema: PageSummaryDefaultSchema
      }
    ]
  },
  {
    id: 'offlineFlowPopup',
    enabled: true,
    name: '[FEATURE] Offline flow popup',
    templates: [
      {
        locale: 'default',
        schema: OfflineFlowPopupDefaultSchema
      }
    ]
  },
];
