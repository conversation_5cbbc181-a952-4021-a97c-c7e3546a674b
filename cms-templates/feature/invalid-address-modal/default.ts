import { TemplatesViewConfigInterface } from '../../../apps/website/src/app/api/v1/templates/templates.interface';

export const InvalidAddressModalDefaultSchema: TemplatesViewConfigInterface = {
  main: [
    {
      'type': 'string',
      'field': 'title',
      'title': 'Title'
    },
    {
      'type': 'string',
      'field': 'description',
      'title': 'Description',
    },
    {
      'type': 'string',
      'field': 'correctAddressButton',
      'title': 'Correct Address" button',
    },
    {
      'type': 'string',
      'field': 'editAddressButton',
      'title': 'Edit Address" button',
    },
  ]
}




