import { TemplatesViewConfigInterface } from '../../../apps/website/src/app/api/v1/templates/templates.interface';

export const webcomponentCantOrderByGenderDefaultSchema: TemplatesViewConfigInterface = {
  main: [
    {
      'type': 'string',
      'field': 'title',
      'title': 'Title',
    },
    {
      'type': 'string',
      'field': 'subtitle',
      'title': 'Subtitle',
    },
    {
      'type': 'string',
      'field': 'text',
      'title': 'Text',
    },
    {
      'type': 'string',
      'field': 'newCustomer',
      'title': 'New customer',
    },
    {
      'type': 'string',
      'field': 'anotherAccount',
      'title': 'Another account',
    },
    {
      'type': 'string',
      'field': 'male',
      'title': 'Male',
    },
    {
      'type': 'string',
      'field': 'female',
      'title': 'Female',
    },
  ]
}
