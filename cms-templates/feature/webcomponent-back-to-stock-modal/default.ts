import { TemplatesViewConfigInterface } from '../../../apps/website/src/app/api/v1/templates/templates.interface';

export const webComponentBackToStockModalDefaultSchema: TemplatesViewConfigInterface = {
  main: [
    {
      'type': 'string',
      'field': 'productBtnTitle',
      'title': 'Product button title',
    },
    {
      'type': 'string',
      'field': 'productInfoBtnTitle',
      'title': 'Product info button title',
    },
    {
      'type': 'string',
      'field': 'modalHeader',
      'title': 'Modal Header Title',
    },
    {
      'type': 'string',
      'field': 'contentProduct',
      'title': 'We will notify you as soon as {{product}} is back in stock',
    },
    {
      'type': 'string',
      'field': 'contentProductInfo',
      'title': 'Let us know your email address and we will notify you when {{product}} is available again',
    },
    {
      'type': 'string',
      'field': 'modalSubmit',
      'title': 'Modal submit title',
    },
    {
      "type": "group",
      "field": "form",
      "title": "Form Modal",
      "items": [
        {
          "type": "string",
          "field": "placeholder",
          "title": "Input placeholder"
        },
        {
          "type": "string",
          "field": "label",
          "title": "Input label"
        },
        {
          "type": "string",
          "field": "agreement",
          "title": "Agreement text"
        }
      ]
    },
    {
      "type": "group",
      "field": "validationsErrors",
      "title": "Validations Errors",
      "items": [
        {
          "type": "string",
          "field": "baseMessage",
          "title": "Base Message"
        },
        {
          "type": "string",
          "field": "invalidEmail",
          "title": "Invalid Email"
        },
      ]
    }
  ]
}
