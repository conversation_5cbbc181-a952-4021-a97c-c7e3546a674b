import { TemplatesViewConfigInterface } from '../../../apps/website/src/app/api/v1/templates/templates.interface';

export const webcomponentQuickProductSearchDefaultSchema: TemplatesViewConfigInterface = {
  main: [
    {
      "type": "text",
      "field": "searchButton",
      "title": "Search Button"
    },{
      "type": "text",
      "field": "inputPlaceholder",
      "title": "Input Placeholder"
    },{
      "type": "text",
      "field": "noResults",
      "title": "No Results"
    },{
      "type": "text",
      "field": "prescription",
      "title": "Prescription Label"
    },{
      "type": "text",
      "field": "delivery",
      "title": "Delivery Label"
    },{
      "type": "text",
      "field": "viewTreatment",
      "title": "View Treatment Link"
    },{
      "type": "text",
      "field": "treatments",
      "title": "Treatments Title"
    },{
      "type": "text",
      "field": "previous",
      "title": "Previous Button"
    },{
      "type": "text",
      "field": "next",
      "title": "Next Button"
    }
  ]
}
