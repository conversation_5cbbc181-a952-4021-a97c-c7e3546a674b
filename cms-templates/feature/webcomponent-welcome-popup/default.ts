import { TemplatesViewConfigInterface } from '../../../apps/website/src/app/api/v1/templates/templates.interface';

export const webcomponentWelcomeModalDefaultSchema: TemplatesViewConfigInterface = {
  main: [
    {
      "type": "group",
      "field": "welcomeInfoModal",
      "title": "Welcome Info Modal",
      "items" : [
        {
          "type": "string",
          "field": "discountLabel",
          "title": "Discount Label"
        },
        {
          "type": "text",
          "field": "modalText",
          "title": "Modal Text"
        },
        {
          "type": "string",
          "field": "emailPlaceholder",
          "title": "Email Placeholder"
        },
        {
          "type": "string",
          "field": "checkboxMessage",
          "title": "Checkbox Message"
        },
        {
          "type": "string",
          "field": "confirmButton",
          "title": "Confirm Button"
        },
        {
          "type": "group",
          "field": "emailErrors",
          "title": "Email Errors",
          "items": [
            {
              "type": "string",
              "field": "emailRequired",
              "title": "Email Required"
            },
            {
              "type": "string",
              "field": "emailInvalid",
              "title": "Email Invalid"
            },
            {
              "type": "string",
              "field": "accountAlreadyExists",
              "title": "Account Already Exists"
            },
          ]
        },
      ]
    },
    {
      "type": "group",
      "field": "welcomeSuccessModal",
      "title": "Welcome Success Modal",
      "items" : [
        {
          "type": "string",
          "field": "successTitle",
          "title": "Success Title"
        },
        {
          "type": "string",
          "field": "successInfo",
          "title": "Success Info"
        },
      ]
    },
  ]
}
