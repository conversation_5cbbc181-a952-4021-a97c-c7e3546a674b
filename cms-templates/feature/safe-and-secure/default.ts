import { TemplatesViewConfigInterface } from '../../../apps/website/src/app/api/v1/templates/templates.interface';

export const SafeAndSecureFeatureDefault: TemplatesViewConfigInterface = {
  'main': [
    {
      'type': 'string',
      'field': 'title',
      'title': 'Title',
    },
    {
      'type': 'text',
      'field': 'subtitle',
      'title': 'Subtitle',
    },
    {
      'type': 'repeater',
      'field': 'items',
      'title': 'Items',
      'items': [
        {
          'type': 'image',
          'field': 'image',
          'title': 'Image',
        },
        {
          'type': 'string',
          'field': 'title',
          'title': 'Title',
        },
        {
          'type': 'text',
          'field': 'subtitle',
          'title': 'Subtitle',
        },
        {
          'type': 'string',
          'field': 'link',
          'title': 'Link',
        },
      ],
    },
    {
      "type": "repeater",
      "field": "services",
      "title": "Safe Services",
      "items": [
        {
          "type": "image",
          "field": "image",
          "title": "image"
        }
      ]
    }
  ],
}
