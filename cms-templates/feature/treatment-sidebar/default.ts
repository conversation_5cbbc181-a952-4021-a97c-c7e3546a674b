import { TemplatesViewConfigInterface } from '../../../apps/website/src/app/api/v1/templates/templates.interface';

export const MainTreatmentSidebarDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      "type": "string",
      "field": "title",
      "title": "Title"
    },
    {
      "type": "string",
      "field": "subTitle",
      "title": "Sub Title"
    },
    {
      "type": "group",
      "field": "bottom",
      "title": "Bottom",
      "items": [
        {
          "type": "string",
          "field": "title",
          "title": "Title"
        },
        {
          "type": "string",
          "field": "subTitle",
          "title": "Sub Title"
        },
        {
          "type": "string",
          "field": "localLabel",
          "title": "Local Label"
        },
        {
          "type": "string",
          "field": "emailLabel",
          "title": "Email Label"
        },
      ]
    },
  ]
}
