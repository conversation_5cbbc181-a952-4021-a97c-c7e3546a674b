import { TemplatesViewConfigInterface } from '../../../apps/website/src/app/api/v1/templates/templates.interface';

export const HowItWorksDefaultSchema: TemplatesViewConfigInterface = {
  main: [
    {
      'type': 'string',
      'field': 'title',
      'title': 'Title',
    },
    {
      'type': 'image',
      'field': 'image',
      'title': 'Image',
    },
    {
      'type': 'repeater',
      'field': 'items',
      'title': 'Items',
      'items': [
        {
          'type': 'image',
          'field': 'image',
          'title': 'Image',
        },
        {
          'type': 'string',
          'field': 'title',
          'title': 'Title',
        },
        {
          'type': 'text',
          'field': 'subtitle',
          'title': 'Subtitle',
        },
      ],
    },
  ]
}




