import { TemplatesViewConfigInterface } from '../../../apps/website/src/app/api/v1/templates/templates.interface';

export const PageSummaryDefaultSchema: TemplatesViewConfigInterface = {
  main: [
    {
      type: 'string',
      field: 'author',
      title: 'Author'
    },
    {
      type: 'string',
      field: 'datePublished',
      title: 'Date published'
    },
    {
      type: 'string',
      field: 'lastUpdate',
      title: 'Last update'
    },
    {
      type: 'string',
      field: 'dateLastReview',
      title: 'Date last review'
    },
    {
      type: 'string',
      field: 'by',
      title: 'By'
    },
  ]
}
