import { TemplatesViewConfigInterface } from '../../../apps/website/src/app/api/v1/templates/templates.interface';

export const MainFooterDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      "type": "group",
      "field": "top",
      "title": "Top",
      "items": [
        {
          "type": "repeater",
          "field": "brandsIcons",
          "title": "Brands Icons",
          "items": [
            {
              "type": "image",
              "field": "image",
              "title": "image"
            }
          ]
        }
      ]
    },
    {
      "type": "group",
      "field": "col_1",
      "items": [
        {
          "type": "string",
          "field": "logoAlt",
          "title": "Logo Alt"
        },
        {
          "type": "string",
          "field": "socialLabel",
          "title": "Social Label"
        },
        {
          "type": "string",
          "field": "otherCountryLabel",
          "title": "Other Country Label"
        },
      ],
      "title": "Footer info column"
    },
    {
      "type": "group",
      "field": "col_2",
      "items": [
        {
          "type": "string",
          "field": "title",
          "title": "Column title"
        },
        {
          "type": "menu",
          "field": "items",
          "title": "Column items menu"
        }
      ],
      "title": "Footer popular treatments"
    },
    {
      "type": "group",
      "field": "col_3",
      "items": [
        {
          "type": "string",
          "field": "title",
          "title": "Column title"
        },
        {
          "type": "menu",
          "field": "items",
          "title": "Column items menu"
        }
      ],
      "title": "Footer legal column"
    },
    {
      "type": "group",
      "field": "col_4",
      "items": [
        {
          "type": "string",
          "field": "title",
          "title": "Column title"
        },
        {
          "type": "menu",
          "field": "items",
          "title": "Column items menu"
        }
      ],
      "title": "Footer useful links"
    },
    {
      "type": "group",
      "field": "bottom",
      "items": [
        {
          "type": "repeater",
          "field": "certs",
          "title": "Certificates",
          "items": [
            {
              "type": "image",
              "field": "image",
              "title": "image"
            },
            {
              "type": "link",
              "field": "link",
              "title": "Link"
            }
          ]
        },
        {
          "type": "string",
          "field": "copyright",
          "title": "Copyright text"
        }
      ],
      "title": "Bottom"
    }
  ]
}
