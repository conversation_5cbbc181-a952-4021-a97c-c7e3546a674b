import { TemplatesViewConfigInterface } from '../../../apps/website/src/app/api/v1/templates/templates.interface';

export const webcomponentsDefaultSchema: TemplatesViewConfigInterface = {
  main: [
    {
      "type": "feature",
      "field": "welcomeModal",
      "title": "Welcome Modal"
    },
    {
      "type": "feature",
      "field": "verificationModal",
      "title": "Verification Modal"
    },
    {
      "type": "feature",
      "field": "loginSidebar",
      "title": "Login Sidebar",
    },
    {
      "type": "feature",
      "field": "quickProductSearch",
      "title": "Quick Product Search",
    },
    {
      "type": "feature",
      "field": "cookiePolicy",
      "title": "Cookie Policy Modal",
    },
    {
      "type": "feature",
      "field": "listSlider",
      "title": "List Slider",
    },
    {
      "type": "feature",
      "field": "webcomponentImgSlider",
      "title": "Image Slider",
    },
    {
      "type": "feature",
      "field": "cantOrderByGender",
      "title": "Cant oreder by gender"
    },
    {
      "type": "feature",
      "field": "offlineFlowPopup",
      "title": "Offline flow popup"
    },
    {
      "type": "feature",
      "field": "headerPromo",
      "title": "Header Promo"
    },
    {
      "type": "feature",
      "field": "webcomponentBackToStockModal",
      "title": "Back To Stock Modal"
    },
  ]
}
