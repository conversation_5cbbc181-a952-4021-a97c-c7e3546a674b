import { TemplatesViewConfigInterface } from '../../../apps/website/src/app/api/v1/templates/templates.interface';

export const CookiePolicyDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      "type": "group",
      "field": "widget",
      "title": "Widget",
      "items": [
        {
          "type": "string",
          "field": "title",
          "title": "Title"
        },
        {
          "type": "string",
          "field": "infoText",
          "title": "Info Text"
        },
        {
          "type": "string",
          "field": "description",
          "title": "Description"
        },
        {
          "type": "string",
          "field": "bottomText",
          "title": "Bottom Text"
        },
        {
          "type": "link",
          "field": "cookieTableLink",
          "title": "Cookie Table Link"
        },
        {
          "type": "link",
          "field": "policyModalLink",
          "title": "Policy Modal Link"
        },
        {
          "type": "string",
          "field": "acceptCookieButton",
          "title": "Accept <PERSON><PERSON>"
        },
        {
          "type": "string",
          "field": "settingButton",
          "title": "Setting Button"
        },
      ]
    },
    {
      "type": "group",
      "field": "settings",
      "title": "Settings Block",
      "items": [
        {
          "type": "string",
          "field": "title",
          "title": "Title"
        },
        {
          "type": "string",
          "field": "necessaryCookieTitle",
          "title": "Necessary Cookie Title"
        },
        {
          "type": "string",
          "field": "necessaryCookieCounter",
          "title": "Necessary Cookie Counter"
        },
        {
          "type": "string",
          "field": "necessaryCookieText",
          "title": "Necessary Cookie Text"
        },
        {
          "type": "string",
          "field": "statisticCookieTitle",
          "title": "Statistic Cookie Title"
        },
        {
          "type": "string",
          "field": "statisticCookieCounter",
          "title": "Statistic Cookie Counter"
        },
        {
          "type": "string",
          "field": "statisticCookieText",
          "title": "Statistic Cookie Text"
        },
        {
          "type": "string",
          "field": "marketingCookieTitle",
          "title": "Marketing Cookie Title"
        },
        {
          "type": "string",
          "field": "marketingCookieCounter",
          "title": "Marketing Cookie Counter"
        },
        {
          "type": "string",
          "field": "marketingCookieText",
          "title": "Marketing Cookie Text"
        },
        {
          "type": "string",
          "field": "crossDomainCookieTitle",
          "title": "Cross Domain Title"
        },
        {
          "type": "string",
          "field": "crossDomainCookieCounter",
          "title": "Cross Domain Cookie Counter"
        },
        {
          "type": "string",
          "field": "crossDomainCookieText",
          "title": "Cross Domain Text"
        },
        {
          "type": "string",
          "field": "crossDomainListText",
          "title": "Cross Domain List Text"
        },
        {
          "type": "repeater",
          "field": "domains",
          "title": "Domains",
          "items": [
            {
              "type": "string",
              "field": "domain",
              "title": "Domain"
            }
          ]
        },
        {
          "type": "string",
          "field": "allowEveryoneButton",
          "title": "Allow Everyone Button"
        },
        {
          "type": "string",
          "field": "allowSelectionButton",
          "title": "Allow Selection Button"
        },
        {
          "type": "string",
          "field": "learnMore",
          "title": "Learn More"
        },
        {
          "type": "string",
          "field": "goBackButton",
          "title": "Go Back Button"
        },
        {
          "type": "string",
          "field": "validationText",
          "title": "Validation Text"
        },
        {
          "type": "string",
          "field": "buttonYes",
          "title": "Button Yes"
        },
        {
          "type": "string",
          "field": "buttonNo",
          "title": "Button No"
        },
      ]
    },
  ]
}
