import { TemplatesViewConfigInterface } from '../../../apps/website/src/app/api/v1/templates/templates.interface';

export const OrderStatusDefaultSchema: TemplatesViewConfigInterface = {
  main: [
    {
      type: 'string',
      field: 'paid',
      title: 'Paid'
    },
    {
      type: 'string',
      field: 'processing',
      title: 'Processing'
    },
    {
      type: 'string',
      field: 'shipped',
      title: 'Shipped'
    },
    {
      type: 'string',
      field: 'completed',
      title: 'Completed'
    },
    {
      type: 'string',
      field: 'pendingPayment',
      title: 'Pending Payment'
    },
    {
      type: 'string',
      field: 'pendingShipment',
      title: 'Pending Shipment'
    },
    {
      type: 'string',
      field: 'pendingRx',
      title: 'Pending Rx'
    },
    {
      type: 'string',
      field: 'aborted',
      title: 'Aborted'
    },
    {
      type: 'string',
      field: 'cancelled',
      title: 'Cancelled'
    },
    {
      type: 'string',
      field: 'declined',
      title: 'Declined'
    },
    {
      type: 'string',
      field: 'returned',
      title: 'Returned'
    },
    {
      type: 'string',
      field: 'reshipped',
      title: 'Reshipped'
    },
    {
      type: 'string',
      field: 'waitingForPrescription',
      title: 'Waiting for prescription'
    }
  ]
}
