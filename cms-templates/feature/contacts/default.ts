import { TemplatesViewConfigInterface } from '../../../apps/website/src/app/api/v1/templates/templates.interface';

export const ContactsDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      "type": "string",
      "field": "legalName",
      "title": "Legal Name (Used in organization schema)"
    },
    {
      "type": "string",
      "field": "organizationName",
      "title": "Organization Name (Used in organization schema)"
    },
    {
      "type": "string",
      "field": "websiteName",
      "title": "Website Name (Used in organization schema)"
    },
    {
      "type": "string",
      "field": "phoneLabel",
      "title": "Phone Label"
    },
    {
      "type": "string",
      "field": "phone",
      "title": "Phone"
    },
    {
      "type": "string",
      "field": "internationalLabel",
      "title": "International Label"
    },
    {
      "type": "string",
      "field": "international",
      "title": "International Phone"
    },
    {
      "type": "string",
      "field": "emailLabel",
      "title": "Email Label"
    },
    {
      "type": "string",
      "field": "email",
      "title": "Email"
    },
  ]
}
