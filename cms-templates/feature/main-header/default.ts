import { TemplatesViewConfigInterface } from '../../../apps/website/src/app/api/v1/templates/templates.interface';

export const MainHeaderDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      'type': 'feature',
      'field': 'promotionBanner',
      'title': 'Promotion banner feature',
    },
    {
      'type': 'feature',
      'field': 'suggestedPromo',
      'title': 'Suggested promo feature',
    },
    {
      "type": "group",
      "field": "topHeader",
      "items": [
        {
          "type": "menu",
          "field": "items",
          "title": "Header top menu"
        },
        {
          "type": "string",
          "field": "liveChatLabel",
          "title": "Live Chat Label"
        }
      ],
      "title": "Top Header"
    },
    {
      "type": "group",
      "field": "subHeader",
      "items": [
        {
          "type": "string",
          "field": "menuToggle",
          "title": "Menu Toggle"
        },
        {
          "type": "string",
          "field": "freshChatLabel",
          "title": "Fresh Chat Label"
        },
        {
          "type": "string",
          "field": "loginLabel",
          "title": "Login on mobile Label"
        },
      ],
      "title": "Sub Header"
    },
    {
      "type": "group",
      "field": "mobileHeader",
      "items": [
        {
          "type": "string",
          "field": "categories",
          "title": "Categories"
        },
        {
          "type": "string",
          "field": "backMainMenu",
          "title": "Back to main menu"
        },
        {
          "type": "string",
          "field": "company",
          "title": "Company"
        },
        {
          "type": "string",
          "field": "myAccount",
          "title": "My Account"
        },
        {
          "type": "link",
          "field": "faq",
          "title": "FAQ Label"
        },
        {
          "type": "string",
          "field": "usefulLinksLabel",
          "title": "Useful Links Label"
        },
        {
          "type": "menu",
          "field": "items",
          "title": "Useful links menu"
        },
        {
          "type": "string",
          "field": "legalLabel",
          "title": "Legal Label"
        },
        {
          "type": "string",
          "field": "otherCountry",
          "title": "Other Country"
        },
      ],
      "title": "Mobile Header"
    },
    {
      "type": "group",
      "field": "routes",
      "title": "Account Menu",
      "items": [
        {
          "type": "link",
          "field": "product",
          "title": "Product Route"
        },
        {
          "type": "link",
          "field": "accountDetails",
          "title": "Account Details"
        },
        {
          "type": "link",
          "field": "orderHistory",
          "title": "Order History"
        },
        {
          "type": "link",
          "field": "subscriptionHistory",
          "title": "Subscription History"
        },
      ]
    },
    {
      "type": "group",
      "field": "profile",
      "items": [
        {
          "type": "string",
          "field": "logOut",
          "title": "Log Out"
        },
        {
          "type": "string",
          "field": "signIn",
          "title": "Sign in"
        },
        {
          "type": "string",
          "field": "selectYourTreatment",
          "title": "Select your treatment"
        },
        {
          "type": "string",
          "field": "startTreatment",
          "title": "Start Treatment"
        }
      ],
      "title": "Profile"
    },
  ]
}
