import { TemplatesViewConfigInterface } from '../../../apps/website/src/app/api/v1/templates/templates.interface';

export const webcomponentLoginSidebarDefaultSchema: TemplatesViewConfigInterface = {
  main: [
    {
      "type": "group",
      "field": "otpForm",
      "title": "OTP Form",
      "items": [
        {
          "type": "string",
          "field": "title",
          "title": "Title"
        },{
          "type": "string",
          "field": "subtitle",
          "title": "Subtitle"
        },{
          "type": "string",
          "field": "emailPlaceholder",
          "title": "Email Placeholder"
        },{
          "type": "string",
          "field": "submitButton",
          "title": "Submit Button"
        },{
          "type": "string",
          "field": "resendCode",
          "title": "Resend Code"
        },{
          "type": "string",
          "field": "toLogin",
          "title": "Switch to login form"
        },{
          "type": "group",
          "field": "errors",
          "title": "Errors",
          "items": [
            {
              "type": "string",
              "field": "invalidEmail",
              "title": "Invalid Email"
            }
          ]
        },{
          "type": "group",
          "field": "enterOTP",
          "title": "Enter OTP Section",
          "items": [
            {
              "type": "string",
              "field": "title",
              "title": "Title"
            },{
              "type": "string",
              "field": "text",
              "title": "Text"
            },{
              "type": "string",
              "field": "text2",
              "title": "Text 2"
            },{
              "type": "string",
              "field": "editEmailLink",
              "title": "edit Email Link"
            },{
              "type": "string",
              "field": "spam",
              "title": "Spam"
            },{
              "type": "string",
              "field": "invalid",
              "title": "Incorrect Code"
            }
          ]
        }
      ]
    },
    {
      "type": "group",
      "field": "loginForm",
      "title": "Login Form",
      "items": [
        {
          "type": "string",
          "field": "title",
          "title": "Title"
        },{
          "type": "string",
          "field": "email",
          "title": "Email"
        },{
          "type": "string",
          "field": "password",
          "title": "Password"
        },{
          "type": "string",
          "field": "submitButton",
          "title": "Submit Button"
        },{
          "type": "string",
          "field": "forgotPassword",
          "title": "Forgot Password"
        },{
          "type": "string",
          "field": "otp",
          "title": "OTP"
        },{
          "type": "group",
          "field": "errors",
          "title": "Errors",
          "items": [
            {
              "type": "string",
              "field": "incorrectCredentials",
              "title": "Incorrect Credentials"
            },
            {
              "type": "string",
              "field": "customerAccountDisabled",
              "title": "Customer Account Disabled"
            }
          ]
        },
        {
          "type": "string",
          "field": "or",
          "title": "Or"
        },
      ]
    },
    {
      "type": "group",
      "field": "resetPasswordForm",
      "title": "Reset Password Form",
      "items": [
        {
          "type": "string",
          "field": "title",
          "title": "Title"
        },{
          "type": "string",
          "field": "enterEmailText",
          "title": "Enter Email Text"
        },{
          "type": "string",
          "field": "backToLogin",
          "title": "Back to login"
        },{
          "type": "string",
          "field": "resetButton",
          "title": "Reset Button"
        },{
          "type": "group",
          "field": "errors",
          "title": "Errors",
          "items": [
            {
              "type": "string",
              "field": "invalidEmail",
              "title": "Invalid Email"
            },{
              "type": "string",
              "field": "maxAttempts",
              "title": "Max Attempts"
            }
          ]
        },
        {
          "type": "group",
          "field": "successSection",
          "title": "Success Section",
          "items": [
            {
              "type": "string",
              "field": "title",
              "title": "Title"
            },{
              "type": "string",
              "field": "text",
              "title": "Text"
            },{
              "type": "string",
              "field": "text2",
              "title": "Text 2"
            }
          ]
        }
      ]
    },
    {
      "type": "group",
      "field": "googleOauthButton",
      "title": "Google Oauth Button",
      "items": [
        {
          "type": "string",
          "field": "title",
          "title": "Title"
        },
      ]
    },
    {
      "type": "string",
      "field": "liveChat",
      "title": "Live Chat"
    },
    {
      "type": "string",
      "field": "ContactUs",
      "title": "Contact us"
    },
    {
      "type": "string",
      "field": "PasswordTab",
      "title": "Password tab"
    },
    {
      "type": "string",
      "field": "OTPTab",
      "title": "OTP tab"
    }
  ]
}
