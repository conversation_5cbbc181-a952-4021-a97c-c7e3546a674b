import { TemplatesViewConfigInterface } from '../../../apps/website/src/app/api/v1/templates/templates.interface';

export const webcomponentHeaderPromoDefaultSchema: TemplatesViewConfigInterface = {
  main: [
    {
      'type': 'boolean',
      'field': 'isEnabled',
      'title': 'Is Enabled',
    },
    {
      'type': 'string',
      'field': 'titleLeft',
      'title': 'Title left',
    },
    {
      'type': 'string',
      'field': 'titleCenter',
      'title': 'Title center',
    },
    {
      'type': 'string',
      'field': 'titleRight',
      'title': 'Title right',
    },
  ]
}
