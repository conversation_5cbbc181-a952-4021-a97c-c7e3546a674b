import { TemplatesViewConfigInterface } from '../../../apps/website/src/app/api/v1/templates/templates.interface';

export const SuggestAddressModalDefaultSchema: TemplatesViewConfigInterface = {
  main: [
    {
      'type': 'string',
      'field': 'title',
      'title': 'Title'
    },
    {
      'type': 'string',
      'field': 'description',
      'title': 'Description',
    },
    {
      'type': 'string',
      'field': 'enteredAddress',
      'title': 'Entered Address',
    },
    {
      'type': 'string',
      'field': 'suggestedAddress',
      'title': 'Suggested Address',
    },
    {
      'type': 'string',
      'field': 'correctAddressButton',
      'title': 'Confirm Address" button',
    },
    {
      'type': 'string',
      'field': 'editAddressButton',
      'title': 'Edit Address" button',
    },
  ]
}




