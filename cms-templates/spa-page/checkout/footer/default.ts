import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';

export const footerDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      'type': 'string',
      'field': 'phoneFreeMessage',
      'title': 'Phone Free Message'
    },
    {
      'type': 'string',
      'field': 'international',
      'title': 'International',
    },
    {
      'type': 'string',
      'field': 'liveChat',
      'title': 'Live Chat',
    },
    {
      'type': 'group',
      'field': 'iconsGroup',
      'title': 'Icons',
      'items': [
        {
          'type': 'repeater',
          'field': 'icons',
          'items': [
            {
              "type": "string",
              "field": "link",
              "title": "Link"
            },
            {
              'type': 'group',
              'field': 'externalImage',
              'title': 'External Image',
              'items': [
                {
                  'type': 'string',
                  'field': 'src',
                  'title': 'Src'
                },
                {
                  'type': 'string',
                  'field': 'alt',
                  'title': 'Alt'
                },
                {
                  'type': 'string',
                  'field': 'width',
                  'title': 'Width'
                },
                {
                  'type': 'string',
                  'field': 'height',
                  'title': 'Height'
                },
              ]
            },
            {
              'type': 'image',
              'field': 'internalImage',
              'title': 'Internal Image'
            }
          ]
        }
      ]
    },
    {
      'type': 'code',
      'field': 'copyrightText',
      'title': 'Copyright Text',
    },
    {
      'type': 'group',
      'field': 'regulationDisclaimer',
      'title': 'Regulation Disclaimer',
      'items': [
        {
          'type': 'string',
          'field': 'registration',
          'title': 'Registration',
        },
        {
          'type': 'string',
          'field': 'chooseTreatment',
          'title': 'Choose Treatment',
        },
        {
          'type': 'string',
          'field': 'chooseTreatmentFullPriceAbTest',
          'title': 'Full Price Ab-Test',
        },
      ]
    },
    {
      "type": "feature",
      "field": "contacts",
      "title": "Contacts"
    },
  ]
}
