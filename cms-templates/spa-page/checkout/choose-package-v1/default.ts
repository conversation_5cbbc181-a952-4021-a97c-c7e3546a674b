import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';

export const choosePackageV1DefaultSchema: TemplatesViewConfigInterface = {
  'main': [
    {
      'type': 'string',
      'field': 'choosePlan',
      'title': 'Choose Plan'
    },
    {
      'type': 'string',
      'field': 'selectDosage',
      'title': 'Select dosage'
    },
    {
      'type': 'string',
      'field': 'selectDosageProductSkipped',
      'title': 'Select dosage (Choose product step skipped)'
    },
    {
      'type': 'string',
      'field': 'selectPackage',
      'title': 'Select Package'
    },
    {
      'type': 'string',
      'field': 'selectPackageProductSkipped',
      'title': 'Select Package (Choose product step skipped)'
    },
    {
      'type': 'string',
      'field': 'modificationBestValue',
      'title': 'Modification Best Value'
    },
    {
      'type': 'string',
      'field': 'treatmentFeeIncludedText',
      'title': 'Treatment Fee Included Text'
    },
    {
      'type': 'group',
      'field': 'treatmentFeeIncludedPopup',
      'title': 'Treatment Fee Included popup',
      'items': [
        {
          'type': 'string',
          'field': 'triggerText',
          'title': 'Trigger text'
        },
        {
          'type': 'string',
          'field': 'title',
          'title': 'Title'
        },
        {
          'type': 'string',
          'field': 'desc',
          'title': 'Description'
        },
      ]
    },
    {
      'type': 'string',
      'field': 'treatmentFeeIncluded',
      'title': 'Treatment Fee Included'
    },
    {
      'type': 'string',
      'field': 'productOneUnit',
      'title': 'Product One Unit'
    },
    {
      'type': 'string',
      'field': 'productSavedPrice',
      'title': 'Product Saved Price'
    },
    {
      'type': 'string',
      'field': 'morePackageSizes',
      'title': 'More Package Sizes'
    },
    {
      'type': 'string',
      'field': 'switchAndSave',
      'title': 'Switch And Save'
    },
    {
      'type': 'group',
      'field': 'buyModeSelector',
      'title': 'Buy Mode Selector',
      'items': [
        {
          'type': 'string',
          'field': 'topDiscountChoise',
          'title': 'Top Discount Choice'
        },
        {
          'type': 'string',
          'field': 'topDiscountChoiseV2',
          'title': 'Top Discount Choice V2'
        },
        {
          'type': 'string',
          'field': 'topDiscountChoiceSingleProductMode',
          'title': 'Top Discount Choice(Single product mode)'
        },
        {
          'type': 'string',
          'field': 'subscriptionDisabled',
          'title': 'Subscription Disabled'
        },
        {
          'type': 'string',
          'field': 'title',
          'title': 'Title'
        },
        {
          'type': 'string',
          'field': 'titleSingleProductMode',
          'title': 'Title(Single product mode)'
        },
        {
          'type': 'string',
          'field': 'titleSup',
          'title': 'Title Sup'
        },
        {
          'type': 'string',
          'field': 'discount',
          'title': 'Discount'
        },
        {
          'type': 'string',
          'field': 'cancelAnyTime',
          'title': 'Cancel Any Time'
        },
        {
          'type': 'string',
          'field': 'singlePurchase',
          'title': 'Single purchase'
        },
        {
          'type': 'string',
          'field': 'repeatServiceDesc',
          'title': 'Repeat service(Single product mode)'
        },
        {
          'type': 'string',
          'field': 'oneTimePurchase',
          'title': 'One Time Purchase'
        },
        {
          'type': 'string',
          'field': 'oneTimePurchaseSingleProductMode',
          'title': 'One Time Purchase(Single product mode)'
        },
        {
          'type': 'string',
          'field': 'oneTimePurchaseDesc',
          'title': 'One Time Purchase Description'
        },
        {
          'type': 'string',
          'field': 'oneTimePurchaseDescSingleProductMode',
          'title': 'One Time Purchase Description(Single product mode)'
        },
        {
          'type': 'repeater',
          'field': 'subscriptionBenefits',
          'title': 'Subscription Benefits',
          'items': [
            {
              'type': 'string',
              'field': 'benefitText',
              'title': 'Benefit Text'
            }
          ]
        },
        {
          "type": "group",
          "field": "regularity",
          "title": "Regularity",
          "items": [
            {
              "type": "string",
              "field": "1_month",
              "title": "One month"
            },
            {
              "type": "string",
              "field": "3_month",
              "title": "Three months"
            },
            {
              "type": "string",
              "field": "6_month",
              "title": "Six months"
            },
          ]
        },
        {
          "type": "group",
          "field": "regularityAbo",
          "title": "Regularity Abomeds",
          "items": [
            {
              "type": "code",
              "field": "1_month",
              "title": "One month"
            },
            {
              "type": "code",
              "field": "3_month",
              "title": "Three months"
            },
            {
              "type": "code",
              "field": "6_month",
              "title": "Six months"
            },
          ]
        },
      ]
    },
    {
      'type': 'group',
      'field': 'disclaimer',
      'title': 'Disclaimer',
      'items': [
        {
          'type': 'string',
          'field': 'content',
          'title': 'Content'
        },
        {
          'type': 'string',
          'field': 'showMore',
          'title': 'Show more'
        },
        {
          'type': 'string',
          'field': 'showLess',
          'title': 'Show less'
        },
      ]
    }, {
      'type': 'group',
      'field': 'suggestModal',
      'title': 'Do not miss modal',
      'items': [
        {
          'type': 'string',
          'field': 'title',
          'title': 'Title'
        },
        {
          'type': 'string',
          'field': 'titleSingleProductMode',
          'title': 'Title(Single product mode)'
        },
        {
          'type': 'repeater',
          'field': 'repeatModalBenefits',
          'title': 'Repeat Modal Benefits',
          'items': [
            {
              'type': 'string',
              'field': 'text',
              'title': 'Benefit Text'
            },
            {
              'type': 'image',
              'field': 'img',
              'title': 'Image'
            },
          ]
        },
        {
          'type': 'repeater',
          'field': 'repeatModalBenefitsSingleProductMode',
          'title': 'Repeat Modal Benefits(Single product mode)',
          'items': [
            {
              'type': 'string',
              'field': 'text',
              'title': 'Benefit Text'
            },
            {
              'type': 'image',
              'field': 'img',
              'title': 'Image'
            },
          ]
        },
        {
          'type': 'string',
          'field': 'CTA',
          'title': 'CTA'
        },
        {
          'type': 'string',
          'field': 'CTASingleProductMode',
          'title': 'CTA(Single product mode)'
        },
        {
          'type': 'string',
          'field': 'link',
          'title': 'Link'
        },
        {
          'type': 'string',
          'field': 'linkSingleProductMode',
          'title': 'Link(Single product mode)'
        },
      ]
    },
    {
      'type': 'group',
      'field': 'subscriptionHowToModal',
      'title': 'Repeat service How To modal',
      'items': [
        {
          'type': 'string',
          'field': 'title',
          'title': 'Title'
        },
        {
          'type': 'string',
          'field': 'desc',
          'title': 'Description'
        },
        {
          'type': 'string',
          'field': 'desc1',
          'title': 'Description second row'
        },
        {
          'type': 'string',
          'field': 'howItWorksTitle',
          'title': 'How it Works'
        },
        {
          'type': 'repeater',
          'field': 'howItWorksSteps',
          'title': 'How it works steps',
          'items': [
            {
              'type': 'string',
              'field': 'title',
              'title': 'Title'
            },
            {
              'type': 'string',
              'field': 'desc',
              'title': 'Desc'
            },
          ]
        },
      ],
    },
    {
      'type': 'string',
      'field': 'continueButtonLabel',
      'title': 'Continue Button Label'
    },
    {
      'type': 'string',
      'field': 'vatIncludedText',
      'title': 'Vat Included Text'
    },
    {
      'type': 'string',
      'field': 'preferDifferentMedication',
      'title': 'Prefer different medication?'
    },
    {
      'type': 'string',
      'field': 'chooseDosage',
      'title': 'Choose dosage'
    },
    {
      'type': 'string',
      'field': 'dosageNotification',
      'title': 'Dosage Notification '
    },
    {
      'type': 'string',
      'field': 'seeAllOptions',
      'title': 'See all options'
    },
    {
      'type': 'group',
      'field': 'warningsAndNotifications',
      'title': 'Warnings and Notifications',
      'items': [
        {
          'type': 'string',
          'field': 'applyingCouponNotification',
          'title': 'Applying Coupon Notification'
        },
        {
          'type': 'string',
          'field': 'discountAddedToCart',
          'title': 'Discount added to cart'
        },
        {
          'type': 'string',
          'field': 'preselectedModificationNotification',
          'title': 'Preselected Modification Notification'
        },
        {
          'type': 'string',
          'field': 'preselectedModificationNotificationProductSkipped',
          'title': 'Preselected Modification Notification (Choose product step skipped)'
        },
        {
          'type': 'string',
          'field': 'preselectedModificationNotificationViagra',
          'title': 'Preselected Modification Notification (Product step skipped. Previous order was Viagra)'
        },
        {
          'type': 'string',
          'field': 'preselectedModificationNotificationSildenafil',
          'title': 'Preselected Modification Notification (Product step skipped. Previous order was Sildenafil)'
        },
        {
          'type': 'text',
          'field': 'cumulativeRestrictionsWarning',
          'title': 'Cumulative Restrictions Warning'
        },
        {
          'type': 'string',
          'field': 'dosageMedicallyRestricted',
          'title': 'Dosage Medically Restricted'
        },
      ]
    },
    {
      'type': 'group',
      'field': 'alternativeModificationModal',
      'title': 'Alternative Modification Modal',
      'items': [
        {
          'type': 'string',
          'field': 'perOneUnit',
          'title': 'Per One Unit'
        },
        {
          'type': 'string',
          'field': 'switchAndSaveButtonLabel',
          'title': 'Switch And Save Button Label'
        },
        {
          'type': 'string',
          'field': 'upgradeAndPayLess',
          'title': 'Upgrade And Pay Less'
        },
        {
          'type': 'string',
          'field': 'chooseMoreTablets',
          'title': 'Choose More Tablets'
        },
        {
          'type': 'string',
          'field': 'doNotSwitchButtonLabel',
          'title': 'Do Not Switch Button Label'
        },
        {
          'type': 'string',
          'field': 'repeatModalHeadTitle',
          'title': 'Repeat Modal Head Title'
        },
        {
          'type': 'string',
          'field': 'repeatModalLabelNew',
          'title': 'Repeat Modal Label New'
        },
        {
          'type': 'string',
          'field': 'repeatModalTitle',
          'title': 'Repeat Modal Title'
        },
        {
          'type': 'repeater',
          'field': 'repeatModalBenefits',
          'title': 'Repeat Modal Benefits',
          'items': [
            {
              'type': 'string',
              'field': 'benefitText',
              'title': 'Benefit Text'
            },
          ]
        },
        {
          'type': 'string',
          'field': 'repeatModalCta',
          'title': 'Repeat Modal Cta'
        },
        {
          'type': 'string',
          'field': 'repeatModalOneTimeButton',
          'title': 'Repeat Modal One Time Button'
        },
        {
          'type': 'string',
          'field': 'policyLink',
          'title': 'Policy Link'
        }
      ]
    },
    {
      'type': 'group',
      'field': 'alternativeModificationV2Modal',
      'title': 'Alternative Modification V2 Modal',
      'items': [
        {
          'type': 'string',
          'field': 'oneTimeTitle',
          'title': 'One Time Title'
        },
        {
          'type': 'string',
          'field': 'subscriptionTitle',
          'title': 'Subscription Title'
        },
        {
          'type': 'string',
          'field': 'oneTimeDescription',
          'title': 'One Time Description'
        },
        {
          'type': 'string',
          'field': 'subscriptionDescription',
          'title': 'Subscription Description'
        },
        {
          'type': 'string',
          'field': 'perOneUnit',
          'title': 'Per One Unit'
        },
        {
          'type': 'string',
          'field': 'total',
          'title': 'Total'
        },
        {
          'type': 'string',
          'field': 'saved',
          'title': 'Saved'
        },
        {
          'type': 'string',
          'field': 'buyButtonText',
          'title': 'Buy Button Text'
        },
        {
          'type': 'string',
          'field': 'skipButtonText',
          'title': 'Skip Button Text'
        },
        {
          "type": "group",
          "field": "regularity",
          "title": "Delivery Regularity",
          "items": [
            {
              "type": "string",
              "field": "1_month",
              "title": "One month"
            },
            {
              "type": "string",
              "field": "3_month",
              "title": "Three months"
            },
            {
              "type": "string",
              "field": "6_month",
              "title": "Six months"
            }
          ]
        },
      ]
    },
    {
      'type': 'group',
      'field': 'crossSaleOffer',
      'title': 'Cross Sale Offer',
      'items': [
        {
          'type': 'string',
          'field': 'title',
          'title': 'Title'
        },
        {
          'type': 'string',
          'field': 'saved',
          'title': 'Saved'
        },
        {
          'type': 'string',
          'field': 'sameIngredient',
          'title': 'Same Ingredient'
        },
        {
          'type': 'string',
          'field': 'subscriptionDelivery',
          'title': 'Subscription Delivery'
        },
        {
          'type': 'string',
          'field': 'oneTimeDelivery',
          'title': 'One Time Delivery'
        },
        {
          'type': 'string',
          'field': 'ctaText',
          'title': 'Cta Text'
        }
      ]
    },
    {
      'type': 'group',
      'field': 'alternativeModificationV3Modal',
      'title': 'Alternative Modification V3 Modal',
      'items': [
        {
          'type': 'string',
          'field': 'title',
          'title': 'Title'
        },
        {
          'type': 'string',
          'field': 'description',
          'title': 'Description'
        },
        {
          'type': 'string',
          'field': 'dosageFormUnitName',
          'title': 'Unit Form Name'
        },
        {
          'type': 'string',
          'field': 'identical',
          'title': 'Identical'
        },
        {
          'type': 'string',
          'field': 'double',
          'title': 'Double'
        },
        {
          'type': 'string',
          'field': 'trusted',
          'title': 'Trusted'
        },
        {
          'type': 'string',
          'field': 'saved',
          'title': 'Saved'
        },
        {
          'type': 'string',
          'field': 'buyButtonText',
          'title': 'Buy Button Text'
        },
        {
          'type': 'string',
          'field': 'skipButtonText',
          'title': 'Skip Button Text'
        },
      ]
    },
    {
      'type': 'group',
      'field': 'subscriptionPolicyModal',
      'title': 'Subscription Policy Modal',
      'items': [
        {
          'type': 'string',
          'field': 'title',
          'title': 'Title'
        },
        {
          'type': 'text',
          'field': 'text',
          'title': 'Text'
        }
      ]
    },
    {
      'type': 'group',
      'field': 'dosageWarnings',
      'title': 'Dosage Warnings',
      'items': [
        {
          'type': 'string',
          'field': 'alphaAdrenergic',
          'title': 'Alpha Adrenergic'
        },
        {
          'type': 'string',
          'field': 'cyp3a4',
          'title': 'CYP 3A4'
        },
        {
          'type': 'string',
          'field': 'antiRetroViral',
          'title': 'Anti Retro Viral'
        },
      ]
    },
    {
      'type': 'code',
      'field': 'switchingProductWarning1',
      'title': 'Switch to another product warning (1 day)'
    },
    {
      'type': 'code',
      'field': 'switchingProductWarning7',
      'title': 'Switch to another product warning (7 days)'
    },
    {
      'type': 'group',
      'field': 'stickyOfferProduct',
      'title': 'Sticky offer product',
      'items': [
        {
          'type': 'string',
          'field': 'productName',
          'title': 'Product name'
        },
        {
          'type': 'string',
          'field': 'discountProduct',
          'title': 'Discount product'
        },
        {
          'type': 'string',
          'field': 'description',
          'title': 'Description'
        },
        {
          'type': 'string',
          'field': 'buttonText',
          'title': 'Button text'
        },
      ]
    },
  ],
  'other' : [
    {
      'type': 'string',
      'field': 'pageTitle',
      'title': 'Page Title',
    },
    {
      'type': 'string',
      'field': 'progressTitle',
      'title': 'Progress Bar Title',
    }
  ]
}
