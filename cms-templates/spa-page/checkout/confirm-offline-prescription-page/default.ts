import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';

export const confirmOfflinePrescriptionPageDefaultSchema: TemplatesViewConfigInterface = {
  'main': [
    {
      'type': 'group',
      'field': 'notification',
      'title': 'Notifications',
      'items': [
        {
          'type': 'string',
          'field': 'suggestedCoupon',
          'title': 'Suggested Coupon',
        },{
          'type': 'string',
          'field': 'prefilledCoupon',
          'title': 'Prefilled Coupon',
        }
      ]
    },
    {
      'type': 'group',
      'field': 'region',
      'title': 'Change Region Notification',
      'items': [
        {
          'type': 'string',
          'field': 'textBeforeLink',
          'title': 'Text before link',
        },{
          'type': 'string',
          'field': 'link',
          'title': 'Link',
        },{
          'type': 'string',
          'field': 'textAfterLink',
          'title': 'Text after link',
        },
      ]
    },
    {
      "type": "string",
      "field": "contentTitle",
      "title": "Content title"
    },{
      "type": "code",
      "field": "mainPrescriptionInfo",
      "title": "Main prescription info"
    },{
      "type": "code",
      "field": "note",
      "title": "Important Note"
    },
    {
      "type": "string",
      "field": "scrollToRead",
      "title": "Scroll To Read"
    },
    {
      "type": "string",
      "field": "continue",
      "title": "Continue"
    },
    {
      "type": "string",
      "field": "onlinePrescription",
      "title": "Online Prescription"
    },
    {
      "type": "string",
      "field": "footerDisclaimer",
      "title": "Footer Disclaimer"
    }
  ],
  'other' : [
    {
      'type': 'string',
      'field': 'pageTitle',
      'title': 'Page Title',
    }
  ]
}
