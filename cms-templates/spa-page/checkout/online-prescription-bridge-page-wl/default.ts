import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';

export const onlinePrescriptionBridgePageWlDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      'type': 'string',
      'field': 'title',
      'title': 'Title',
    },
    {
      'type': 'code',
      'field': 'description',
      'title': 'Description',
    },
    {
      'type': 'string',
      'field': 'submit',
      'title': 'Submit',
    },
    {
      'type': 'group',
      'field': 'question',
      'title': 'Question',
      'items': [
        {
          'type': 'string',
          'field': 'title',
          'title': 'Title',
        },
        {
          'type': 'string',
          'field': 'description',
          'title': 'Description',
        },
        {
          'type': 'string',
          'field': 'goToOfflineButton',
          'title': 'Go to offline button',
        },
        {
          'type': 'string',
          'field': 'continueOnlineFlowButton',
          'title': 'Continue online flow button',
        },
      ]
    },
  ],
  "other" : [
    {
      'type': 'string',
      'field': 'pageTitle',
      'title': 'Page Title',
    }
  ]
}
