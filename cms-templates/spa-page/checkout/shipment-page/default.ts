import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';

export const shipmentPageDefaultSchema: TemplatesViewConfigInterface = {
  'main': [
    {
      "type": "feature",
      "field": "invalidAddressModal",
      "title": "Invalid Address Modal"
    },
    {
      "type": "feature",
      "field": "deliveryInfoModal",
      "title": "Delivery Info Modal"
    },
    {
      "type": "feature",
      "field": "suggestAddressModal",
      "title": "Suggest Address Modal"
    },
    {
      'type': 'string',
      'field': 'shipmentPageTitle',
      'title': 'Shipment Page Title',
    },
    {
      'type': 'string',
      'field': 'shipmentPageDescription',
      'title': 'Shipment Page Description',
    },
    {
      'type': 'string',
      'field': 'patientDetailsTitle',
      'title': 'Patient Details Title',
    },
    {
      'type': 'string',
      'field': 'deliveryDetailsTitle',
      'title': 'Delivery Details Title',
    },
    {
      'type': 'string',
      'field': 'editLabel',
      'title': 'Edit Label',
    },
    {
      'type': 'group',
      'field': 'patientInfo',
      'title': 'Patient Info',
      'items': [
        {
          'type': 'string',
          'field': 'firstNameLabel',
          'title': 'First Name Label',
        },
        {
          'type': 'string',
          'field': 'firstNameTooltip',
          'title': 'First Name Tooltip',
        },
        {
          'type': 'string',
          'field': 'lastNameLabel',
          'title': 'Last Name Label',
        },
        {
          'type': 'string',
          'field': 'fullNameLabel',
          'title': 'Full Name Label',
        },
        {
          'type': 'string',
          'field': 'email',
          'title': 'Email Label',
        },
        {
          'type': 'string',
          'field': 'bsnNumber',
          'title': 'BSN Number',
        },
        {
          'type': 'string',
          'field': 'bsnNumberTooltip',
          'title': 'BSN Number Tooltip',
        },
        {
          'type': 'string',
          'field': 'lspConsentText',
          'title': 'LSP Consent Accepted Text',
        },
        {
          'type': 'string',
          'field': 'lspConsentLink',
          'title': 'LSP Consent Link',
        },
        {
          'type': 'code',
          'field': 'lspConsentModal',
          'title': 'LSP Consent Modal',
        },
      ],
    },
    {
      'type': 'group',
      'field': 'checkoutAddress',
      'title': 'Checkout Address',
      'items': [
        {
          'type': 'string',
          'field': 'country',
          'title': 'Country',
        },
        {
          'type': 'string',
          'field': 'notYourCountry',
          'title': '"Not Your Country" label?',
        },
        {
          'type': 'string',
          'field': 'deliveryZip',
          'title': 'Delivery ZIP',
        },
        {
          'type': 'string',
          'field': 'deliveryStreet',
          'title': 'Delivery Street',
        },
        {
          'type': 'string',
          'field': 'deliveryHouseNumber',
          'title': 'Delivery House Number',
        },
        {
          'type': 'string',
          'field': 'deliveryBuildingCode',
          'title': 'Delivery Building Code',
        },
        {
          'type': 'string',
          'field': 'additionalInfo',
          'title': 'Additional Info',
        },
        {
          'type': 'string',
          'field': 'additionalAddress',
          'title': 'Additional Address',
        },
        {
          'type': 'string',
          'field': 'additionalAddressOptional',
          'title': 'Additional Address Optional',
        },
        {
          'type': 'string',
          'field': 'deliveryCity',
          'title': 'Delivery City',
        },
        {
          'type': 'string',
          'field': 'shipmentInfoCountryTooltip',
          'title': 'Shipment Info Country Tooltip',
        },
      ],
    },
    {
      'type': 'string',
      'field': 'shippingAndBillingTitle',
      'title': 'Shipping And Billing Title',
    },
    {
      'type': 'string',
      'field': 'shippingDetailsTitle',
      'title': 'Shipping Details Title',
    },
    {
      'type': 'string',
      'field': 'billingDetailsTitle',
      'title': 'Billing Details Title',
    },
    {
      'type': 'string',
      'field': 'differentAddressRemoveLink',
      'title': 'Different Address Remove Link',
    },
    {
      'type': 'string',
      'field': 'differentDeliveryAddressTitle',
      'title': 'Different Delivery Address Title',
    },
    {
      'type': 'string',
      'field': 'phoneNumberInfoLabel',
      'title': 'Phone Number Info Label',
    },
    {
      'type': 'string',
      'field': 'phoneNumberInfoDescription',
      'title': 'Phone Number Info Description',
    },
    {
      'type': 'group',
      'field': 'phoneNumber',
      'title': 'Phone Number',
      'items': [
        {
          'type': 'string',
          'field': 'phoneNumberLabel',
          'title': 'Phone Number Label',
        },
        {
          'type': 'string',
          'field': 'phoneNumberOptionalLabel',
          'title': 'Phone Number Optional Label',
        },
        {
          'type': 'string',
          'field': 'phoneNumberTooltip',
          'title': 'Phone Number Tooltip',
        },
      ]
    },
    {
      'type': 'group',
      'field': 'birthDate',
      'title': 'Birth Date',
      'items': [
        {
          'type': 'string',
          'field': 'birthDateLabel',
          'title': 'Birth Date Label',
        },
        {
          'type': 'string',
          'field': 'birthDateTooltipUneditable',
          'title': 'Birth Date Uneditable',
        },
        {
          'type': 'string',
          'field': 'birthDateTooltip',
          'title': 'Birth Date Tooltip',
        },
        {
          'type': 'string',
          'field': 'birthDateDayPlaceholder',
          'title': 'Birth Date Day placeholder',
        },
        {
          'type': 'string',
          'field': 'birthDateMonthPlaceholder',
          'title': 'Birth Date Month placeholder',
        },
        {
          'type': 'string',
          'field': 'birthDateYearPlaceholder',
          'title': 'Birth Date Year placeholder',
        },
      ]
    },
    {
      'type': 'string',
      'field': 'addressEquality',
      'title': 'Address Equality Text'
    },
    {
      'type': 'string',
      'field': 'shipmentAddressTitle',
      'title': 'Shipment Address Title'
    },
    {
      'type': 'string',
      'field': 'sameAddressPickupPoint',
      'title': 'Same Address Pickup Point',
    },
    {
      'type': 'string',
      'field': 'differentAddressPickupPoint',
      'title': 'Different Address Pickup Point',
    },
    {
      'type': 'string',
      'field': 'differentAddressPickupPointWL',
      'title': 'Different Address Pickup Point Weight Loss',
    },
    {
      'type': 'string',
      'field': 'deliveryPickupPoint',
      'title': 'Delivery Pickup Point',
    },
    {
      'type': 'string',
      'field': 'deliveryOtherAddress',
      'title': 'Delivery Other Address',
    },
    {
      'type': 'group',
      'field': 'packstation',
      'title': 'Packstation',
      'items': [
        {
          'type': 'string',
          'field': 'pickupPostNumberTooltip',
          'title': 'Pickup Point Post Number Tooltip',
        },
        {
          'type': 'string',
          'field': 'packstationBlockTitle',
          'title': 'Packstation Block Title',
        },
        {
          'type': 'string',
          'field': 'packstationDetailsTitle',
          'title': 'Packstation Details Title',
        },
        {
          'type': 'string',
          'field': 'postNumber',
          'title': 'Post Number',
        },
        {
          'type': 'string',
          'field': 'zip',
          'title': 'ZIP',
        },
        {
          'type': 'string',
          'field': 'city',
          'title': 'City',
        },
        {
          'type': 'string',
          'field': 'packstationNumber',
          'title': 'Packstation Number'
        },
        {
          'type': 'string',
          'field': 'noMatches',
          'title': 'No Matches'
        },
      ]
    },
    {
      'type': 'string',
      'field': 'deliveryMethod',
      'title': 'Delivery Method',
    },
    {
      'type': 'string',
      'field': 'deliveryMethodIsUnavailable',
      'title': 'Delivery Method Is Unavailable',
    },
    {
      'type': 'string',
      'field': 'deliveryTimeTypeStandart',
      'title': 'Delivery Time for Standart Type',
    },
    {
      'type': 'string',
      'field': 'deliveryTimeTypeExpress',
      'title': 'Delivery Time for Express Type',
    },
    {
      'type': 'string',
      'field': 'deliveryTimeTypeExpressTemperatureBoxRegular',
      'title': 'Delivery Time for Express Type (Temperature Box Regular)',
    },
    {
      'type': 'string',
      'field': 'deliveryTimeTypeStandardTemperatureBoxRegular',
      'title': 'Delivery Time for Standard Type (Temperature Box Regular)',
    },
    {
      'type': 'string',
      'field': 'deliveryTimeTypeExpressTemperatureBoxTemperatureController',
      'title': 'Delivery Time for Express Type (Temperature Box - Temperature Controlled)',
    },
    {
      'type': 'string',
      'field': 'deliveryTimeTypeStandardTemperatureBoxTemperatureController',
      'title': 'Delivery Time for Standard Type (Temperature Box - Temperature Controlled)',
    },
    {
      'type': 'string',
      'field': 'freeDeliveryText',
      'title': 'Free Delivery Text',
    },
    {
      'type': 'string',
      'field': 'expressDeliveryHint',
      'title': 'Express Delivery Hint',
    },
    {
      'type': 'string',
      'field': 'postponedDeliveryHintTitle',
      'title': 'Postponed Delivery Hint Title',
    },
    {
      'type': 'code',
      'field': 'postponedDeliveryHintDescription',
      'title': 'Postponed Delivery Hint Description',
    },
    {
      'type': 'string',
      'field': 'continueButtonLabel',
      'title': 'Continue Button Label',
    },
    {
      'type': 'group',
      'field': 'notYourCountryModal',
      'title': 'Not Your Country Modal',
      'items': [
        {
          'type': 'string',
          'field': 'youVisitCountryText',
          'title': 'You Visit Country Text'
        },
        {
          'type': 'string',
          'field': 'changeLocationForceWarning',
          'title': 'Change Location Force Warning Text',
        },
      ]
    },
    {
      'type': 'string',
      'field': 'livingAddressTitle',
      'title': 'Living Address Title',
    },
    {
      'type': 'string',
      'field': 'livingAddressSubtitle',
      'title': 'Living Address Subtitle',
    },
    {
      'type': 'string',
      'field': 'shippingAddressTitle',
      'title': 'Shipping Address Title',
    },
    {
      'type': 'string',
      'field': 'sameAsLivingAddress',
      'title': '"Same As Living Address" text',
    },
    {
      'type': 'string',
      'field': 'billingAddressTitle',
      'title': 'Billing Address Title',
    },
    {
      'type': 'string',
      'field': 'sameAsShippingAddress',
      'title': '"Same As Shipping Address" text',
    },
    {
      'type': 'group',
      'field': 'errors',
      'title': 'Errors',
      'items': [
        {
          'type': 'string',
          'field': 'firstNameRequired',
          'title': 'First Name Required',
        },
        {
          'type': 'string',
          'field': 'firstNameInvalid',
          'title': 'First Name Invalid',
        },
        {
          'type': 'string',
          'field': 'lastNameRequired',
          'title': 'Last Name Required',
        },
        {
          'type': 'string',
          'field': 'lastNameInvalid',
          'title': 'Last Name Invalid',
        },
        {
          'type': 'string',
          'field': 'emailNotExists',
          'title': 'Email Not Exists',
        },
        {
          'type': 'string',
          'field': 'emailWhitespaceInvalid',
          'title': 'Email Whitespace Invalid',
        },
        {
          'type': 'string',
          'field': 'emailSpecialCharactersInvalid',
          'title': 'Email Special Characters Invalid',
        },
        {
          'type': 'string',
          'field': 'emailRequired',
          'title': 'Email Required',
        },
        {
          'type': 'string',
          'field': 'emailInvalid',
          'title': 'Email Invalid',
        },
        {
          'type': 'string',
          'field': 'emailAlreadyExists',
          'title': 'Email Already Exists',
        },
        {
          'type': 'string',
          'field': 'zipNotFound',
          'title': 'Zip not found'
        },
        {
          'type': 'string',
          'field': 'deliveryStreetRequired',
          'title': 'Delivery street required'
        },
        {
          'type': 'string',
          'field': 'deliveryStreetInvalid',
          'title': 'Delivery street invalid'
        },
        {
          'type': 'string',
          'field': 'deliveryHouseRequired',
          'title': 'Delivery house required'
        },
        {
          'type': 'string',
          'field': 'deliveryHouseInvalid',
          'title': 'Delivery house invalid'
        },
        {
          'type': 'string',
          'field': 'deliveryCityRequired',
          'title': 'Delivery city required'
        },
        {
          'type': 'string',
          'field': 'deliveryCityInvalid',
          'title': 'Delivery city invalid'
        },
        {
          'type': 'string',
          'field': 'deliveryZipRequired',
          'title': 'Delivery zip required'
        },
        {
          'type': 'string',
          'field': 'deliveryZipInvalid',
          'title': 'Delivery zip invalid'
        },
        {
          'type': 'string',
          'field': 'deliveryZipOnly4',
          'title': 'Delivery zip only 4 numbers'
        },
        {
          'type': 'string',
          'field': 'deliveryZipOnly5',
          'title': 'Delivery zip only 5 numbers'
        },
        {
          'type': 'string',
          'field': 'billingStreetRequired',
          'title': 'Billing street required'
        },
        {
          'type': 'string',
          'field': 'billingStreetInvalid',
          'title': 'Billing street invalid'
        },
        {
          'type': 'string',
          'field': 'billingHouseRequired',
          'title': 'Billing house required'
        },
        {
          'type': 'string',
          'field': 'billingHouseInvalid',
          'title': 'Billing house invalid'
        },
        {
          'type': 'string',
          'field': 'billingCityRequired',
          'title': 'Billing city required'
        },
        {
          'type': 'string',
          'field': 'billingCityInvalid',
          'title': 'Billing city invalid'
        },
        {
          'type': 'string',
          'field': 'billingZipRequired',
          'title': 'Billing zip required'
        },
        {
          'type': 'string',
          'field': 'billingZipInvalid',
          'title': 'Billing zip invalid'
        },
        {
          'type': 'string',
          'field': 'billingZipOnly4',
          'title': 'Billing zip only 4 numbers'
        },
        {
          'type': 'string',
          'field': 'billingZipOnly5',
          'title': 'Billing zip only 5 numbers'
        },
        {
          'type': 'string',
          'field': 'birthDateRequired',
          'title': 'Birth date required'
        },
        {
          'type': 'string',
          'field': 'birthDateAdult',
          'title': 'Birth date adult'
        },
        {
          'type': 'string',
          'field': 'birthDateInvalid',
          'title': 'Birth date invalid'
        },
        {
          'type': 'string',
          'field': 'birthDateInvalidYear',
          'title': 'Birth date invalid year'
        },
        {
          'type': 'string',
          'field': 'packstationNumberInvalid',
          'title': 'Packstation number invalid'
        },
        {
          'type': 'string',
          'field': 'packstationNumberRequired',
          'title': 'Packstation number required'
        },
        {
          'type': 'string',
          'field': 'packstationPostnumberInvalid',
          'title': 'Packstation post number invalid'
        },
        {
          'type': 'string',
          'field': 'packstationPostnumberRequired',
          'title': 'Packstation post number required'
        },
        {
          'type': 'string',
          'field': 'packstationZipInvalid',
          'title': 'Packstation zip invalid'
        },
        {
          'type': 'string',
          'field': 'packstationZipRequired',
          'title': 'Packstation zip required'
        },
        {
          'type': 'string',
          'field': 'packstationZipOnly4',
          'title': 'Packstation zip only 4 numbers'
        },
        {
          'type': 'string',
          'field': 'packstationZipOnly5',
          'title': 'Packstation zip only 5 numbers'
        },
        {
          'type': 'string',
          'field': 'packstationCityInvalid',
          'title': 'Packstation city invalid'
        },
        {
          'type': 'string',
          'field': 'packstationCityRequired',
          'title': 'Packstation city required'
        },
        {
          'type': 'string',
          'field': 'phoneMin',
          'title': 'Phone min length'
        },
        {
          'type': 'string',
          'field': 'phoneMax',
          'title': 'Phone max length'
        },
        {
          'type': 'string',
          'field': 'phoneInvalid',
          'title': 'Phone invalid'
        },
        {
          'type': 'string',
          'field': 'bsnNumberRequired',
          'title': 'BSN Number Required'
        },
        {
          'type': 'string',
          'field': 'bsnNumberInvalid',
          'title': 'BSN Number Invalid'
        },
        {
          'type': 'string',
          'field': 'lspConsentRequired',
          'title': 'LSP Consent Required'
        },
        {
          'type': 'string',
          'field': 'additionalInfoLength',
          'title': 'Additional Info Length'
        }
      ],

    },
    {
      'type': 'group',
      'field': 'information',
      'title': 'Information Alert',
      'items': [
        {
          'type': 'string',
          'field': 'title',
          'title': 'Information Title'
        },
        {
          'type': 'string',
          'field': 'description',
          'title': 'Information Description'
        }
      ]
    },
    {
      'type': 'string',
      'field': 'subscribeToEmails',
      'title': 'Subscribe to emails',
    },
  ],
  'other' : [
    {
      'type': 'string',
      'field': 'pageTitle',
      'title': 'Page Title',
    },
    {
      'type': 'string',
      'field': 'progressTitle',
      'title': 'Progress Bar Title',
    }
  ]
}
