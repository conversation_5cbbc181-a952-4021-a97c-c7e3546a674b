import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';

export const onlinePrescriptionBridgePageDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      'type': 'group',
      'field': 'notification',
      'title': 'Notification',
      'items': [
        {
          'type': 'string',
          'field': 'regularCoupon',
          'title': 'Coupon notifications for regular orders only',
        },
        {
          'type': 'string',
          'field': 'subscriptionCoupon',
          'title': 'Coupon notifications for subscription orders only',
        },
        {
          'type': 'string',
          'field': 'allCoupon',
          'title': 'Coupon notifications for all order types',
        },
      ],
    },
    {
      'type': 'group',
      'field': 'region',
      'title': 'Change Region Notification',
      'items': [
        {
          'type': 'string',
          'field': 'textBeforeLink',
          'title': 'Text before link',
        },{
          'type': 'string',
          'field': 'link',
          'title': 'Link',
        },{
          'type': 'string',
          'field': 'textAfterLink',
          'title': 'Text after link',
        },
      ]
    },
    {
      'type': 'group',
      'field': 'content',
      'title': 'Content',
      'items': [
        {
          'type': 'string',
          'field': 'title',
          'title': 'Title',
        },
        {
          'type': 'string',
          'field': 'subtitle',
          'title': 'Subtitle',
        },
        {
          'type': 'text',
          'field': 'desc',
          'title': 'Description',
        },
        {
          'type': 'image',
          'field': 'advantageCheckIcon',
          'title': 'Advantage check icon',
        },
        {
          'type': 'repeater',
          'field': 'advantages',
          'items': [
            {
              'type': 'string',
              'field': 'value',
              'title': 'Advantage'
            }
          ]
        }
      ],
    },
    {
      'type': 'string',
      'field': 'scrollToRead',
      'title': 'Scroll To Read (CTA)',
    },
    {
      'type': 'string',
      'field': 'continue',
      'title': 'Continue',
    },
    {
      'type': 'string',
      'field': 'linkToRedeemPrescription',
      'title': 'Link To Redeem Prescription',
    },
    {
      'type': 'group',
      'field': 'redeemPrescription',
      'title': 'Redeem Prescription',
      'items': [
        {
          'type': 'string',
          'field': 'textBeforeLink',
          'title': 'Text before link',
        },{
          'type': 'string',
          'field': 'link',
          'title': 'Link',
        },{
          'type': 'string',
          'field': 'textAfterLink',
          'title': 'Text after link',
        },
      ]
    },
    {
      'type': 'string',
      'field': 'checkOutFAQ',
      'title': 'Check out FAQ',
    },
    {
      'type': 'group',
      'field': 'consent',
      'title': 'Consent',
      'items': [
        {
          'type': 'string',
          'field': 'content',
          'title': 'Content',
        },
        {
          'type': 'string',
          'field': 'showMore',
          'title': 'Show More',
        },
        {
          'type': 'string',
          'field': 'showLess',
          'title': 'Show Less',
        },
      ],
    },
    {
      'type': 'group',
      'field': 'switchToOfflinePrescriptionModal',
      'title': 'Switch to offline prescription modal',
      'items': [
        {
          'type': 'string',
          'field': 'title',
          'title': 'Title',
        },
        {
          'type': 'string',
          'field': 'next',
          'title': 'Next button',
        },
        {
          'type': 'string',
          'field': 'warning',
          'title': 'Warning',
        },
        {
          'type': 'string',
          'field': 'desc1',
          'title': 'Description part 1',
        },
        {
          'type': 'string',
          'field': 'link',
          'title': 'Link',
        },
        {
          'type': 'string',
          'field': 'desc2',
          'title': 'Description part 2',
        },
      ],
    }
  ],
  "other" : [
    {
      'type': 'string',
      'field': 'pageTitle',
      'title': 'Page Title',
    }
  ]
}
