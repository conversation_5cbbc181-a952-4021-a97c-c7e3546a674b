import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';

export const checkoutAuth: TemplatesViewConfigInterface = {
  "main": [
    {
      'type': 'group',
      'field': 'signUp',
      'title': 'Registration',
      'items': [
        {
          "type": "string",
          "field": "title",
          "title": "Title"
        },
        {
          "type": "string",
          "field": "offlineTitle",
          "title": "Offline title"
        },
        {
          "type": "string",
          "field": "subTitle",
          "title": "subtitle"
        },
        {
          "type": "string",
          "field": "haveAccount",
          "title": "Have account text"
        },
        {
          "type": "string",
          "field": "loginLink",
          "title": "Login link text"
        },
        {
          "type": "string",
          "field": "googleOAuthBtn",
          "title": "Google auth button text"
        },
        {
          "type": "string",
          "field": "or",
          "title": "Or"
        },
        {
          'type': 'group',
          'field': 'form',
          'title': 'Form',
          'items': [
            {
              'type': 'string',
              'field': 'firstname',
              'title': 'Firstname',
            },
            {
              'type': 'string',
              'field': 'lastname',
              'title': 'Lastname',
            },
            {
              'type': 'string',
              'field': 'email',
              'title': 'Email',
            },
            {
              'type': 'string',
              'field': 'phoneNumber',
              'title': 'Phone Number',
            },
          ]
        },
        {
          'type': 'group',
          'field': 'phoneFieldLabels',
          'title': 'Phone field labels',
          'items': [
            {
              'type': 'string',
              'field': 'titleOptional',
              'title': 'Title optional',
            },
            {
              'type': 'string',
              'field': 'titleRequired',
              'title': 'Title required',
            },
            {
              'type': 'string',
              'field': 'description',
              'title': 'Description',
            },
          ]
        },
        {
          "type": "string",
          "field": "policy",
          "title": "Policy"
        },
        {
          "type": "string",
          "field": "and",
          "title": "and"
        },
        {
          "type": "string",
          "field": "terms",
          "title": "Terms"
        },
        {
          'type': 'repeater',
          'field': 'links',
          'items': [
            {
              'type': 'link',
              'field': 'link',
              'title': 'Link'
            },
          ],
          'title': 'TNC and Privacy Policy links'
        },
        {
          "type": "string",
          "field": "CTA",
          "title": "Submit button"
        },
        {
          "type": "string",
          "field": "SSLDesc",
          "title": "SSL description"
        },
        {
          'type': 'group',
          'field': 'testContent',
          'title': 'Test content',
          'items': [
            {
              "type": "string",
              "field": "title",
              "title": "Title"
            },
            {
              "type": "string",
              "field": "subTitle",
              "title": "Subtitle"
            },
            {
              "type": "string",
              "field": "CTA",
              "title": "Submit button"
            },
          ]
        },
      ]
    },
    {
      'type': 'group',
      'field': 'signIn',
      'title': 'Login',
      'items': [
        {
          "type": "string",
          "field": "title",
          "title": "Title"
        },
        {
          "type": "string",
          "field": "googleAuthBtn",
          "title": "Google auth button"
        },
        {
          'type': 'group',
          'field': 'form',
          'title': 'Form',
          'items': [
            {
              "type": "string",
              "field": "email",
              "title": "Email"
            },
            {
              "type": "string",
              "field": "emailPlaceholder",
              "title": "Email placeholder"
            },
            {
              "type": "string",
              "field": "password",
              "title": "Password"
            },
            {
              "type": "string",
              "field": "passwordPlaceholder",
              "title": "Password placeholder"
            },
          ],
        },
        {
          "type": "string",
          "field": "CTA",
          "title": "CTA button"
        },
        {
          "type": "string",
          "field": "forgotPassword",
          "title": "Forgot password link text"
        },
      ],
    },
    {
      'type': 'group',
      'field': 'resetPassword',
      'title': 'Reset password',
      'items': [
        {
          "type": "string",
          "field": "title",
          "title": "Title"
        },
        {
          "type": "string",
          "field": "enterEmailAddress",
          "title": "Enter email address"
        },
        {
          'type': 'group',
          'field': 'form',
          'title': 'Form',
          'items': [
            {
              "type": "string",
              "field": "email",
              "title": "Email"
            },
          ],
        },
        {
          "type": "string",
          "field": "CTA",
          "title": "CTA button"
        },
        {
          "type": "string",
          "field": "weSentResetInstructions",
          "title": "We sent reset instructions"
        },
        {
          "type": "string",
          "field": "checkYourEmail",
          "title": "Check your email"
        },
        {
          "type": "string",
          "field": "checkYourSpamFolder",
          "title": "Check your spam folder"
        },
        {
          "type": "string",
          "field": "backToLogin",
          "title": "Back to login link"
        },
      ],
    },
    {
      'type': 'group',
      'field': 'OTP',
      'title': 'OTP',
      'items': [
        {
          "type": "string",
          "field": "title",
          "title": "Title"
        },
        {
          "type": "string",
          "field": "desc",
          "title": "Description"
        },
        {
          "type": "image",
          "field": "spin",
          "title": "spin image"
        },
        {
          "type": "string",
          "field": "userPasswordInstead",
          "title": "User password instead"
        },
        {
          'type': 'group',
          'field': 'form',
          'title': 'Form',
          'items': [
            {
              "type": "string",
              "field": "email",
              "title": "Email"
            }
          ],
        },
        {
          "type": "string",
          "field": "sendBtn",
          "title": "Send email button"
        },
        {
          "type": "string",
          "field": "firstTimeHere",
          "title": "First time here - text"
        },
        {
          "type": "string",
          "field": "toRegistration",
          "title": "Back to registration link"
        },
        {
          "type": "string",
          "field": "sentTitle",
          "title": "Email sent title"
        },
        {
          "type": "string",
          "field": "sentDesc",
          "title": "Email sent desc"
        },
        {
          "type": "string",
          "field": "changeEmailLink",
          "title": "Change email link"
        },
        {
          "type": "string",
          "field": "errorHint",
          "title": "Error hint"
        },
        {
          "type": "string",
          "field": "submitCodeBtn",
          "title": "Submit OTP button"
        },
        {
          "type": "string",
          "field": "resendEmailBtn",
          "title": "Resend email button"
        },
        {
          "type": "string",
          "field": "openGmail",
          "title": "Open Gmail"
        },
        {
          "type": "string",
          "field": "openOutlook",
          "title": "Open Outlook"
        }
      ],
    },
    {
      'type': 'group',
      'field': 'secureLogin',
      'title': 'Secure Login',
      'items': [
        {
          "type": "string",
          "field": "secureLoginTitle",
          "title": "Secure Login Title"
        },
        {
          'type': 'group',
          'field': 'form',
          'title': 'Form',
          'items': [
            {
              'type': 'string',
              'field': 'email',
              'title': 'email',
            },
          ]
        },
      ]
    },
    {
      "type": "string",
      "field": "sideBarTabPassword",
      "title": "Sidebar tab - password"
    },
    {
      "type": "string",
      "field": "sideBarTabOTP",
      "title": "Sidebar tab - otp"
    },
    {
      "type": "string",
      "field": "firstnameRequired",
      "title": "Firstname field is required"
    },
    {
      "type": "string",
      "field": "alreadyHaveAnAccountText",
      "title": "Already Have An Account?"
    },
    {
      "type": "string",
      "field": "alreadyHaveAnAccountLinkText",
      "title": "Sign up here"
    },
    {
      'type': 'group',
      'field': 'error',
      'title': 'Validation errors',
      'items': [
        {
          "type": "string",
          "field": "firstnameRequired",
          "title": "Firstname field is required"
        },
        {
          "type": "string",
          "field": "firstnameInvalid",
          "title": "Firstname field is invalid"
        },
        {
          "type": "string",
          "field": "lastnameRequired",
          "title": "Lastname field is required"
        },
        {
          "type": "string",
          "field": "lastnameInvalid",
          "title": "Lastname field is invalid"
        },
        {
          "type": "string",
          "field": "TOCRequired",
          "title": "TOC agree is required"
        },
        {
          "type": "string",
          "field": "emailNotExists",
          "title": "Email doesnt exist"
        },
        {
          "type": "string",
          "field": "emailRequired",
          "title": "Email is required"
        },
        {
          "type": "string",
          "field": "emailInvalid",
          "title": "Email is invalid"
        },
        {
          "type": "string",
          "field": "emailSpecialCharactersInvalid",
          "title": "Email special characters error"
        },
        {
          "type": "string",
          "field": "emailWhitespaceInvalid",
          "title": "Email whitespace invalid"
        },
        {
          "type": "string",
          "field": "emailAlreadyExists",
          "title": "Email already exists"
        },
        {
          "type": "string",
          "field": "accountAlreadyExists",
          "title": "Account already exists"
        },
        {
          "type": "string",
          "field": "paidOrders",
          "title": "Paid orders"
        },
        {
          "type": "string",
          "field": "unpaidBankwireOrder",
          "title": "Unpaid bankwire order"
        },
        {
          "type": "string",
          "field": "passwordInvalid",
          "title": "Password is Invalid"
        },
        {
          "type": "string",
          "field": "passwordRequired",
          "title": "Password is required"
        },
        {
          "type": "string",
          "field": "passwordMinLength",
          "title": "Password min length error"
        },
        {
          "type": "string",
          "field": "passwordMaxLength",
          "title": "Password max length error"
        },
        {
          "type": "string",
          "field": "passwordEmpty",
          "title": "Password is empty"
        },
        {
          "type": "string",
          "field": "incorrectCredentials",
          "title": "Incorrect credentials"
        },
        {
          "type": "string",
          "field": "customerAccountDisabled",
          "title": "Customer Account Disabled"
        },
        {
          "type": "string",
          "field": "oldPasswordRequired",
          "title": "Old password is required"
        },
        {
          "type": "string",
          "field": "oldPasswordIncorrect",
          "title": "Old password is incorrect"
        },
        {
          "type": "string",
          "field": "confirmPasswordRequired",
          "title": "Confirm password is required"
        },
        {
          "type": "string",
          "field": "confirmPasswordNotMatch",
          "title": "Confirm password not match"
        },
        {
          "type": "string",
          "field": "emailAlreadySent",
          "title": "Email already sent"
        },
        {
          "type": "string",
          "field": "tokenInvalid",
          "title": "Invalid token"
        },
        {
          "type": "string",
          "field": "OTPTooManyRequests",
          "title": "Too many requests for OTP"
        },
        {
          "type": "string",
          "field": "OTPCodeEmpty",
          "title": "OTP code is empty"
        },
        {
          "type": "string",
          "field": "errorOTPCodeInvalid",
          "title": "OTP code is invalid"
        },
        {
          "type": "string",
          "field": "errorOTPCodeExpired",
          "title": "OTP code is expired"
        },
        {
          "type": "string",
          "field": "incorrectCredentialsActiveCampaign",
          "title": "Incorrect Credentials By ActiveCampaign"
        },
        {
          "type": "string",
          "field": "requiredPhoneNumber",
          "title": "Required Phone Number"
        },
        {
          "type": "string",
          "field": "incorrectPhoneNumber",
          "title": "Incorrect Phone Number"
        },
      ],
    },
  ],
  "other" : [
    {
      'type': 'string',
      'field': 'pageTitle',
      'title': 'Page Title',
    },
    {
      'type': 'string',
      'field': 'progressTitle',
      'title': 'Progress Bar Title',
    }
  ]
}
