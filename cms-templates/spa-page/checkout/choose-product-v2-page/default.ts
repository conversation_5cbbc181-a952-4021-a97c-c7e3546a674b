import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';

export const chooseProductV2PageDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      "type": "group",
      "field": "restrictionNotifications",
      "title": "Restriction Notifications",
      "items": [
        {
          "type": "string",
          "field": "productIsRestricted",
          "title": "Product Is Restricted"
        },
        {
          "type": "string",
          "field": "hasRestrictedProduct",
          "title": "Has Restricted Product"
        },
      ],
    },
    {
      "type": "string",
      "field": "discountNotification",
      "title": "Discount Notification"
    },
    {
      "type": "string",
      "field": "searchTitle",
      "title": "Search Title"
    },
  ],
  "other" : [
    {
      'type': 'string',
      'field': 'pageTitle',
      'title': 'Page Title',
    },
    {
      'type': 'string',
      'field': 'progressTitle',
      'title': 'Progress Bar Title',
    }
  ]
}
