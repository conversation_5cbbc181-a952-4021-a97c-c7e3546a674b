import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';

export const onlinePrescriptionBridgePageWlChatDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      'type': 'string',
      'field': 'title',
      'title': 'Title',
    },
    {
      'type': 'string',
      'field': 'subtitle',
      'title': 'Subtitle',
    },
    {
      'type': 'string',
      'field': 'label',
      'title': 'Label',
    },
    {
      'type': 'string',
      'field': 'shouldNotBeBlank',
      'title': 'Should not be blank',
    },
    {
      'type': 'string',
      'field': 'invalidRequest',
      'title': 'Invalid request',
    },
    {
      'type': 'string',
      'field': 'submit',
      'title': 'Submit',
    },
  ],
  "other" : [
    {
      'type': 'string',
      'field': 'pageTitle',
      'title': 'Page Title',
    }
  ]
}
