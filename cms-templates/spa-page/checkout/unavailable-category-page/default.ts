import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';

export const unavailableCategoryPageDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      "type": "string",
      "field": "title",
      "title": "Title"
    },
    {
      "type": "string",
      "field": "desc",
      "title": "Description"
    },
    {
      "type": "string",
      "field": "CTA",
      "title": "CTA button"
    }
  ],
  'other' : [
    {
      'type': 'string',
      'field': 'pageTitle',
      'title': 'Page Title',
    }
  ]
}
