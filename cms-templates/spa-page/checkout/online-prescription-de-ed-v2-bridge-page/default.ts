import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';

export const onlinePrescriptionDeEdV2BridgePageDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      'type': 'group',
      'field': 'content',
      'title': 'Content',
      'items': [
        {
          'type': 'string',
          'field': 'headerText',
          'title': 'Header text',
        },
        {
          'type': 'group',
          'field': 'region',
          'title': 'Region',
          'items': [
            {
              'type': 'string',
              'field': 'textBeforeLink',
              'title': 'Text before link',
            },
            {
              'type': 'string',
              'field': 'link',
              'title': 'Link',
            },
            {
              'type': 'string',
              'field': 'textAfterLink',
              'title': 'Text after link',
            },
          ]
        },
        {
          'type': 'string',
          'field': 'title',
          'title': 'Title',
        },
        {
          'type': 'string',
          'field': 'description',
          'title': 'Description',
        },
        {
          'type': 'string',
          'field': 'continueBtn',
          'title': 'Continue Button',
        },
        {
          'type': 'string',
          'field': 'orderMedication',
          'title': 'If you already have a prescription, order your medication here',
        },
        {
          'type': 'group',
          'field': 'footer',
          'title': 'Footer',
          'items': [
            {
              'type': 'repeater',
              'field': 'icons',
              'items': [
                {
                  "type": "string",
                  "field": "link",
                  "title": "Link"
                },
                {
                  'type': 'group',
                  'field': 'externalImage',
                  'title': 'External Image',
                  'items': [
                    {
                      'type': 'string',
                      'field': 'src',
                      'title': 'Src'
                    },
                    {
                      'type': 'string',
                      'field': 'alt',
                      'title': 'Alt'
                    },
                    {
                      'type': 'string',
                      'field': 'width',
                      'title': 'Width'
                    },
                    {
                      'type': 'string',
                      'field': 'height',
                      'title': 'Height'
                    },
                  ]
                },
                {
                  'type': 'image',
                  'field': 'internalImage',
                  'title': 'Internal Image'
                }
              ]
            },
            {
              'type': 'string',
              'field': 'text',
              'title': 'Text',
            },
          ]
        },
      ],
    },
  ],
  'other' : [
    {
      'type': 'string',
      'field': 'pageTitle',
      'title': 'Page Title',
    }
  ]
}
