import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';

export const stickyCartDefaultSchema: TemplatesViewConfigInterface = {
  'main': [
    {
      'type': 'string',
      'field': 'showCart',
      'title': 'Show Cart Label',
    },
    {
      'type': 'string',
      'field': 'hideCart',
      'title': 'Hide Cart Label',
    },
    {
      'type': 'string',
      'field': 'continueCheckout',
      'title': 'Continue to checkout',
    },
    {
      'type': 'string',
      'field': 'discountNotification',
      'title': 'Discount Notification',
    },
    {
      'type': 'string',
      'field': 'treatmentIncludedText',
      'title': 'Treatment Included Text',
    },
    {
      'type': 'string',
      'field': 'nextDayExpressDeliveryText',
      'title': 'Next Day Express Delivery Text',
    },
    {
      'type': 'string',
      'field': 'delivery',
      'title': 'Delivery',
    },
    {
      'type': 'string',
      'field': 'price',
      'title': 'Price',
    },
    {
      'type': 'string',
      'field': 'treatmentIncluded',
      'title': 'Treatment fee included',
    },
    {
      'type': 'string',
      'field': 'coupon',
      'title': 'Coupon',
    },
    {
      'type': 'string',
      'field': 'prescriptionDiscountLabel',
      'title': 'Prescription Discount Label',
    },
    {
      'type': 'string',
      'field': 'total',
      'title': 'Total',
    },
    {
      'type': 'string',
      'field': 'continueButtonLabel',
      'title': 'Continue Button Label',
    },
    {
      'type': 'string',
      'field': 'freeLabel',
      'title': 'Free Label',
    },
    {
      "type": "spa-feature",
      "field": "prescriptionIncludedHighlight",
      "title": "Prescription Included Highlight"
    },
    {
      "type": "string",
      "field": "headerToggleTitle",
      "title": "Header Toggle title"
    },
    {
      'type': 'group',
      'field': 'prescriptionIncludesModal',
      'title': 'Prescription Includes Modal',
      'items': [
        {
          'type': 'string',
          'field': 'title',
          'title': 'Title',
        },
        {
          'type': 'string',
          'field': 'text',
          'title': 'Text',
        },
      ]
    },
  ]
}
