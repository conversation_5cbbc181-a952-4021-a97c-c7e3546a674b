import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';

export const CheckoutCommonDefaultSchema: TemplatesViewConfigInterface = {
  'main': [
    {
      'type': 'feature',
      'field': 'promotionBanner',
      'title': 'Promotion banner feature',
    },
    {
      'type': 'feature',
      'field': 'cookiePolicy',
      'title': 'Cookie Policy',
    },
    {
      "type": "feature",
      "field": "listSlider",
      "title": "List Slider",
    },
    {
      'type': 'feature',
      'field': 'cantOrderByGender',
      'title': 'Cant order by gender',
    },
    {
      'type': 'feature',
      'field': 'headerPromo',
      'title': 'Header Promo',
    },
    {
      'type': 'string',
      'field': 'loading',
      'title': 'Loading',
    },
  ]
}
