import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';

export const chooseProductV1PageDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      'type': 'group',
      'field': 'title',
      'title': 'Title',
      'items': [
        {
          "type": "string",
          "field": "hairLoss",
          "title": "Hair Loss"
        },
        {
          "type": "string",
          "field": "weightLoss",
          "title": "Weight Loss"
        },
        {
          "type": "string",
          "field": "erectileDysfunction",
          "title": "Erectile Dysfunction"
        },
      ]
    },
    {
      'type': 'group',
      'field': 'subTitle',
      'title': 'Sub Title',
      'items': [
        {
          "type": "string",
          "field": "viagraGenericABTest",
          "title": "Viagra Generic AB Test"
        },
        {
          "type": "string",
          "field": "doctorProposal",
          "title": "Doctor Proposal"
        },
        {
          "type": "string",
          "field": "viagraGeneric",
          "title": "Viagra Generic"
        },
        {
          "type": "string",
          "field": "weightLoss",
          "title": "Weight Loss"
        },
      ]
    },
    {
      "type": "string",
      "field": "abTestDisclaimer",
      "title": "AB Test Disclaimer"
    },
    {
      "type": "string",
      "field": "abTestInfoText",
      "title": "AB Test Info Text"
    },
    {
      "type": "string",
      "field": "abTestInfoSubText",
      "title": "AB Test Info Sub Text"
    },
    {
      'type': 'group',
      'field': 'stickyBlock',
      'title': 'Sticky Block',
      'items': [

      ]
    },
    {
      "type": "string",
      "field": "vatIncludedText",
      "title": "Vat Included Text"
    },
    {
      "type": "string",
      "field": "viagraGenericABTestNotification",
      "title": "Viagra Generic AB Test Notification"
    },
    {
      "type": "string",
      "field": "priceFrom",
      "title": "Price From"
    },
    {
      "type": "string",
      "field": "pricePerPill",
      "title": "Price Per Pill"
    },
    {
      "type": "string",
      "field": "perPillDiscount",
      "title": "Per Pill Discount"
    },
    {
      "type": "group",
      "field": "restrictionReasons",
      "title": "Restriction Reasons",
      "items": [
        {
          "type": "string",
          "field": "medicalRestrictionHemodialysis",
          "title": "Hemodialysis"
        },
        {
          "type": "string",
          "field": "medicalRestrictionBosentan",
          "title": "Bosentan"
        },
        {
          "type": "string",
          "field": "medicalRestrictionGeneral",
          "title": "General"
        },
      ],
    },
    {
      "type": "string",
      "field": "preferDifferentMedication",
      "title": "Prefer Different Medication"
    },
    {
      "type": "string",
      "field": "seeAllOptions",
      "title": "See all options"
    },
    {
      "type": "string",
      "field": "seeAllOptionsWL",
      "title": "See all options Weight Loss"
    },
    {
      "type": "string",
      "field": "continueButton",
      "title": "Continue Button"
    },
    {
      "type": "spa-feature",
      "field": "prescriptionIncludedHighlight",
      "title": "Prescription Included Highlight"
    },
    {
      "type": "string",
      "field": "doctorRecommended",
      "title": "Doctor Recommended",
    }
  ],
  "other" : [
    {
      'type': 'string',
      'field': 'pageTitle',
      'title': 'Page Title',
    },
    {
      'type': 'string',
      'field': 'progressTitle',
      'title': 'Progress Bar Title',
    }
  ]
}
