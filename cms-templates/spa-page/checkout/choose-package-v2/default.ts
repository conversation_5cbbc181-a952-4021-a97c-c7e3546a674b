import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';

export const choosePackageV2DefaultSchema: TemplatesViewConfigInterface = {
  'main': [
    {
      'type': 'string',
      'field': 'applyingCouponNotification',
      'title': 'Applying Coupon Notification'
    },
    {
      'type': 'string',
      'field': 'chooseTreatmentPackageTitle',
      'title': 'Choose Treatment Package Title'
    },
    {
      'type': 'string',
      'field': 'productOneUnit',
      'title': 'Product One Unit'
    },
    {
      'type': 'string',
      'field': 'productSavedPrice',
      'title': 'Product Saved Price'
    },
    {
      'type': 'string',
      'field': 'continueButtonLabel',
      'title': 'Continue Button Label'
    },
    {
      'type': 'string',
      'field': 'vatIncludedText',
      'title': 'Vat Included Text'
    },
    {
      'type': 'string',
      'field': 'lookForDiffContraceptive',
      'title': 'Looking for different contraceptive?'
    },
    {
      'type': 'string',
      'field': 'clickHere',
      'title': '"Click here" text'
    },
  ],
  'other' : [
    {
      'type': 'string',
      'field': 'pageTitle',
      'title': 'Page Title',
    },
    {
      'type': 'string',
      'field': 'progressTitle',
      'title': 'Progress Bar Title',
    }
  ]
}
