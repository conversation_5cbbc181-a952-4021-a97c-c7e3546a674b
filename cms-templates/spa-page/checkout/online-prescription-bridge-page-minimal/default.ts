import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';

export const onlinePrescriptionBridgePageMinimalDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      'type': 'group',
      'field': 'content',
      'title': 'Content',
      'items': [
        {
          'type': 'string',
          'field': 'title',
          'title': 'Title',
        },
        {
          'type': 'string',
          'field': 'description',
          'title': 'Description',
        },
        {
          'type': 'string',
          'field': 'continueBtn',
          'title': 'Continue Button',
        },
        {
          'type': 'group',
          'field': 'offlineFlow',
          'title': 'Offline Flow',
          'items': [
            {
              'type': 'string',
              'field': 'textBeforeLink',
              'title': 'Text before link',
            },{
              'type': 'string',
              'field': 'textLink',
              'title': 'Text Link',
            }
          ]
        },
        {
          'type': 'string',
          'field': 'footerText',
          'title': 'Footer Text',
        },
      ],
    },
    {
      'type': 'group',
      'field': 'notification',
      'title': 'Notifications',
      'items': [
        {
          'type': 'string',
          'field': 'suggestedCoupon',
          'title': 'Suggested Coupon',
        }
      ]
    }
  ],
  'other' : [
    {
      'type': 'string',
      'field': 'pageTitle',
      'title': 'Page Title',
    },
  ]
}
