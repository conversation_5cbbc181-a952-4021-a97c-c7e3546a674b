import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';

export const paymentDetailsPageDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      'type': 'group',
      'field': 'paymentMethods',
      'title': 'Payment methods',
      'items': [
        {
          'type': 'string',
          'field': 'selectPaymentMethod',
          'title': 'Select payment method',
        },
        {
          'type': 'string',
          'field': 'morePaymentOptions',
          'title': 'More payment options',
        },
        {
          'type': 'string',
          'field': 'processingPayment',
          'title': 'Processing payment',
        },
        {
          'type': 'string',
          'field': 'processingMessageText',
          'title': 'Processing message text',
        },
        {
          'type': 'string',
          'field': 'cardPayment',
          'title': 'Card Payment',
        },
        {
          'type': 'group',
          'field': 'applePay',
          'title': 'Apple pay',
          'items': [
            {
              'type': 'string',
              'field': 'description',
              'title': 'Description',
            }
          ],
        },
        {
          'type': 'group',
          'field': 'bankwire',
          'title': 'Bankwire',
          'items': [
            {
              'type': 'string',
              'field': 'bankWireTransfer',
              'title': 'Bank Wire Transfer',
            },
            {
              'type': 'string',
              'field': 'advanceBankTransfer',
              'title': 'Advance bank transfer',
            },
            {
              'type': 'text',
              'field': 'wireTransfersPlaceholder',
              'title': 'Wire transfers placeholder',
            },
            {
              'type': 'string',
              'field': 'wireTransfersTooltip',
              'title': 'Wire transfers tooltip',
            },
            {
              'type': 'string',
              'field': 'payBtn',
              'title': 'Pay button',
            }
          ],
        },
        {
          'type': 'group',
          'field': 'braintreeCc',
          'title': 'Braintree cc',
          'items': [
            {
              'type': 'string',
              'field': 'description',
              'title': 'Description',
            },
            {
              'type': 'string',
              'field': 'chargeWillAppear',
              'title': 'The charge will appear on your bill as',
            },
            {
              'type': 'string',
              'field': 'addNewCard',
              'title': 'Add new card',
            },
            {
              'type': 'string',
              'field': 'expirationDate',
              'title': 'Expiration date',
            },
            {
              'type': 'string',
              'field': 'cvv',
              'title': 'CVV',
            },
            {
              'type': 'string',
              'field': 'cvvCodeExplanation',
              'title': 'CVV2/CVC2 code is three-digit code on the back of your credit card',
            },
            {
              'type': 'string',
              'field': 'saveCard',
              'title': 'Save card',
            },
            {
              'type': 'string',
              'field': 'paySecurelyNow',
              'title': 'Pay securely now',
            },
            {
              'type': 'string',
              'field': 'cardNumber',
              'title': 'Card number',
            },
            {
              'type': 'string',
              'field': 'cardHolderName',
              'title': 'Card holder name',
            },
            {
              'type': 'string',
              'field': 'paymentCard',
              'title': 'Payment card',
            },
            {
              'type': 'string',
              'field': 'expires',
              'title': 'Expires',
            },
            {
              'type': 'string',
              'field': 'expired',
              'title': 'Expired',
            },
            {
              'type': 'string',
              'field': 'selectAnotherCard',
              'title': 'Select another card',
            },
            {
              'type': 'string',
              'field': 'selectCard',
              'title': 'Select card',
            },
          ],
        },
        {
          'type': 'group',
          'field': 'braintreePaypal',
          'title': 'Braintree paypal',
          'items': [
            {
              'type': 'string',
              'field': 'description',
              'title': 'Description',
            },
          ],
        },
        {
          'type': 'group',
          'field': 'fibonatix',
          'title': 'Fibonatix',
          'items': [
            {
              'type': 'string',
              'field': 'description',
              'title': 'Description',
            },
            {
              'type': 'string',
              'field': 'chargeAppearOnBill',
              'title': 'The charge will appear on your bill as',
            },
            {
              'type': 'string',
              'field': 'paySecurelyNow',
              'title': 'Pay securely now',
            },
            {
              'type': 'string',
              'field': 'addNewCard',
              'title': 'Add new card',
            },
            {
              'type': 'string',
              'field': 'cardNumber',
              'title': 'Card number',
            },
            {
              'type': 'string',
              'field': 'expirationDate',
              'title': 'Expiration date',
            },
            {
              'type': 'string',
              'field': 'cvv',
              'title': 'CVV',
            },
            {
              'type': 'string',
              'field': 'mm',
              'title': 'MM',
            },
            {
              'type': 'string',
              'field': 'yyyy',
              'title': 'YYYY',
            },
            {
              'type': 'string',
              'field': 'cardHolderName',
              'title': 'Card holder name',
            },
            {
              'type': 'string',
              'field': 'selectAnotherCard',
              'title': 'Select another card',
            },
            {
              'type': 'string',
              'field': 'selectCard',
              'title': 'Select card',
            },
            {
              'type': 'string',
              'field': 'paymentCard',
              'title': 'Payment card',
            },
            {
              'type': 'string',
              'field': 'expires',
              'title': 'Expires',
            },
            {
              'type': 'string',
              'field': 'expired',
              'title': 'Expired',
            }
          ],
        },
        {
          'type': 'group',
          'field': 'googlePay',
          'title': 'Google pay',
          'items': [
            {
              'type': 'string',
              'field': 'description',
              'title': 'Description',
            }
          ],
        },
        {
          'type': 'group',
          'field': 'klarna',
          'title': 'Klarna',
          'items': [
            {
              'type': 'string',
              'field': 'directDescription',
              'title': 'Direct description',
            },
            {
              'type': 'string',
              'field': 'installmentDescription',
              'title': 'Installment description',
            },
            {
              'type': 'string',
              'field': 'invoiceDescription',
              'title': 'Invoice description',
            },
            {
              'type': 'string',
              'field': 'transferDescription',
              'title': 'Transfer description',
            },
            {
              'type': 'string',
              'field': 'paySecurelyNow',
              'title': 'Pay securely now',
            }
          ],
        },
        {
          'type': 'group',
          'field': 'multibanco',
          'title': 'Multibanco',
          'items': [
            {
              'type': 'text',
              'field': 'advanceBankTransfer',
              'title': 'Advance bank transfer',
            },
            {
              'type': 'string',
              'field': 'payButton',
              'title': 'Pay button',
            }
          ],
        },
        {
          'type': 'group',
          'field': 'paypal',
          'title': 'Paypal',
          'items': [
            {
              'type': 'string',
              'field': 'description',
              'title': 'Description',
            }
          ],
        },
        {
          'type': 'group',
          'field': 'trustly',
          'title': 'Trustly',
          'items': [
            {
              'type': 'string',
              'field': 'instantBankTransfer',
              'title': 'Instant bank transfer',
            },
            {
              'type': 'string',
              'field': 'instantSecureBankTransfer',
              'title': 'Instant secure bank transfer',
            },
            {
              'type': 'string',
              'field': 'description',
              'title': 'Description',
            },
            {
              'type': 'string',
              'field': 'whatIsTrustly',
              'title': 'What is Trustly?',
            }
          ],
        },
        {
          'type': 'group',
          'field': 'braintreeIdeal',
          'title': 'Braintree iDEAL',
          'items': [
            {
              'type': 'string',
              'field': 'description',
              'title': 'Description',
            },
            {
              'type': 'string',
              'field': 'payButton',
              'title': 'Pay button',
            },
            {
              'type': 'string',
              'field': 'completePayment',
              'title': 'Complete Payment',
            }
          ],
        },
      ],
    },
    {
      'type': 'group',
      'field': 'crossalePopupButton',
      'title': 'Crossale popup button',
      'items': [
        {
          'type': 'string',
          'field': 'oftenBoughtTogether',
          'title': 'Often bought together',
        },
        {
          'type': 'string',
          'field': 'close',
          'title': 'Close',
        }
      ],
    },
    {
      'type': 'group',
      'field': 'deliveryRow',
      'title': 'Delivery row',
      'items': [
        {
          'type': 'string',
          'field': 'free',
          'title': 'Free',
        },
      ],
    },
    {
      'type': 'group',
      'field': 'modificationRows',
      'title': 'Modification rows',
      'items': [
        {
          'type': 'string',
          'field': 'medicalPrescription',
          'title': 'Medical Prescription',
        },
        {
          'type': 'string',
          'field': 'prescriptionIncluded',
          'title': 'Prescription included',
        },
        {
          'type': 'string',
          'field': 'treatmentFeeIncluded',
          'title': 'Traatment fee included',
        },
        {
          'type': 'string',
          'field': 'quantity',
          'title': 'Quantity',
        },
        {
          'type': 'string',
          'field': 'free',
          'title': 'Free',
        },
      ],
    },
    {
      'type': 'group',
      'field': 'prescriptionRow',
      'title': 'Prescription row',
      'items': [
        {
          'type': 'string',
          'field': 'medicalPrescription',
          'title': 'Medical prescription',
        },
        {
          'type': 'string',
          'field': 'free',
          'title': 'Free',
        }
      ],
    },
    {
      'type': 'group',
      'field': 'promocode',
      'title': 'Promocode',
      'items': [
        {
          'type': 'string',
          'field': 'enterYourCode',
          'title': 'Enter your code',
        },
        {
          'type': 'string',
          'field': 'havePromo',
          'title': 'Have promo?',
        },
        {
          'type': 'string',
          'field': 'havePromoQuestion',
          'title': 'Do you have a coupon code?',
        },
        {
          'type': 'string',
          'field': 'successfullyAppliedDiscount',
          'title': 'Your discount applied successfully',
        },
      ],
    },
    {
      'type': 'group',
      'field': 'subscriptionReservationPolicyModal',
      'title': 'Subscription reservation policy modal',
      'items': [
        {
          'type': 'string',
          'field': 'title',
          'title': 'Title',
        },
        {
          'type': 'string',
          'field': 'text',
          'title': 'Text',
        }
      ],
    },
    {
      'type': 'group',
      'field': 'subscriptionReservationPolicyModalWL',
      'title': 'Subscription reservation policy modal weight loss',
      'items': [
        {
          'type': 'string',
          'field': 'title',
          'title': 'Title',
        },
        {
          'type': 'string',
          'field': 'text',
          'title': 'Text',
        }
      ],
    },
    {
      'type': 'group',
      'field': 'upsaleBlock',
      'title': 'Upsale block',
      'items': [
        {
          'type': 'string',
          'field': 'changeBackTo',
          'title': 'Change back to',
        },
        {
          'type': 'string',
          'field': 'upgradeOrder',
          'title': 'Upgrade order',
        },
        {
          'type': 'string',
          'field': 'upgradeAndSave',
          'title': 'Upgrade and save',
        },
        {
          'type': 'string',
          'field': 'upgradeTo',
          'title': 'Upgrade to',
        },
        {
          'type': 'string',
          'field': 'andSave',
          'title': 'And save',
        },
        {
          'type': 'string',
          'field': 'instead',
          'title': 'Instead',
        },
      ],
    },
    {
      'type': 'group',
      'field': 'paymentsOrderInformation',
      'title': 'Payments order information',
      'items': [
        {
          'type': 'string',
          'field': 'disclaimer',
          'title': 'Disclaimer',
        },
        {
          'type': 'text',
          'field': 'hgTermsOfUse',
          'title': 'HG Terms of Use',
        },
        {
          'type': 'string',
          'field': 'discount',
          'title': 'Discount',
        },
        {
          'type': 'string',
          'field': 'discountSuccessfullyRedeemed',
          'title': 'Your discount has been successfully redeemed.',
        },
        {
          'type': 'string',
          'field': 'remove',
          'title': 'Remove',
        },
        {
          'type': 'string',
          'field': 'gift',
          'title': 'Gift',
        },
        {
          'type': 'string',
          'field': 'oftenBoughtTogether',
          'title': 'Often bought together',
        },
        {
          'type': 'string',
          'field': 'checkOtcProducts',
          'title': 'Check OTC products',
        },
        {
          'type': 'string',
          'field': 'seeMoreOtcProductsV2',
          'title': 'See more OTC products V2',
        },
        {
          'type': 'string',
          'field': 'seeMoreOtcProducts',
          'title': 'See more OTC products',
        },
        {
          'type': 'string',
          'field': 'seeMoreOtcProducts',
          'title': 'See more OTC products',
        },
        {
          'type': 'string',
          'field': 'changeFlowHint',
          'title': 'Change flow hint',
        },
        {
          'type': 'string',
          'field': 'changeFlowLink',
          'title': 'Change flow link',
        },
        {
          'type': 'group',
          'field': 'changeFromOneTime',
          'title': 'Change From One Time',
          'items': [
            {
              'type': 'string',
              'field': 'title',
              'title': 'Title',
            },
            {
              'type': 'string',
              'field': 'text',
              'title': 'Text',
            },
            {
              'type': 'string',
              'field': 'linkText',
              'title': 'Link Text',
            },
          ]
        },
        {
          'type': 'group',
          'field': 'changeFromSubscription',
          'title': 'Change From Subscription',
          'items': [
            {
              'type': 'string',
              'field': 'text',
              'title': 'Text',
            },
            {
              'type': 'string',
              'field': 'linkText',
              'title': 'Link Text',
            },
          ]
        },
        {
          'type': 'string',
          'field': 'youAre',
          'title': 'You are',
        },
        {
          'type': 'string',
          'field': 'awayFromFreeShipping',
          'title': 'Away from free shipping',
        },
        {
          'type': 'string',
          'field': 'total',
          'title': 'Total',
        },
        {
          'type': 'string',
          'field': 'vatIncludedText',
          'title': 'VAT included text',
        },
        {
          'type': 'string',
          'field': 'vatIncludedTooltip',
          'title': 'VAT included tooltip',
        },
        {
          'type': 'string',
          'field': 'repeatServiceReservationPolicyLink',
          'title': 'Repeat service reservation policy link',
        },
        {
          'type': 'string',
          'field': 'repeatServiceReservationPolicyLinkWL',
          'title': 'Repeat service reservation policy link weight loss',
        },
        {
          'type': 'link',
          'field': 'repeatServiceAgbLink',
          'title': 'Repeat service AGB link',
        },
        {
          'type': 'string',
          'field': 'promoCodeApplied',
          'title': 'Promo code applied',
        },
      ],
    },
    {
      'type': 'group',
      'field': 'appliedCouponNotification',
      'title': 'Applied coupon notification',
      'items': [
        {
          'type': 'string',
          'field': 'useCouponToRedeemDiscount',
          'title': 'Use this coupon code to redeem your discount',
        },
        {
          'type': 'string',
          'field': 'copiedToClipboard',
          'title': 'Copied to the clipboard',
        },
        {
          'type': 'string',
          'field': 'clipboard',
          'title': 'Clipboard',
        },
      ],
    },
    {
      'type': 'group',
      'field': 'orderReservationHint',
      'title': 'Order reservation hint',
      'items': [
        {
          'type': 'string',
          'field': 'reservationAndReturnPolicy',
          'title': 'Our reservation and return policy',
        },
        {
          'type': 'string',
          'field': 'hintTitle',
          'title': 'Hint title',
        },
        {
          'type': 'string',
          'field': 'hintNote',
          'title': 'Hint note',
        },
        {
          'type': 'string',
          'field': 'hintBody1',
          'title': 'Hint body 1',
        },
        {
          'type': 'string',
          'field': 'hintBody2',
          'title': 'Hint body 2',
        },
        {
          'type': 'string',
          'field': 'note',
          'title': 'Note',
        },
        {
          'type': 'string',
          'field': 'hintNoReturnBodyOnline1',
          'title': 'Hint no return body online 1',
        },
        {
          'type': 'string',
          'field': 'hintNoReturnBodyOffline1',
          'title': 'Hint no return body offline 1',
        },
        {
          'type': 'string',
          'field': 'hintNoReturnBodyOnline2',
          'title': 'Hint no return body online 2',
        },
        {
          'type': 'string',
          'field': 'hintNoReturnBodyOffline2',
          'title': 'Hint no return body offline2',
        }
      ],
    },
    {
      'type': 'group',
      'field': 'otcModal',
      'title': 'Otc modal',
      'items': [
        {
          'type': 'string',
          'field': 'back',
          'title': 'Back',
        },
        {
          'type': 'string',
          'field': 'title',
          'title': 'Title',
        },
        {
          'type': 'string',
          'field': 'closeButton',
          'title': 'Close button',
        },
        {
          'type': 'string',
          'field': 'cta1',
          'title': 'Cta 1',
        },
        {
          'type': 'string',
          'field': 'cta2',
          'title': 'Cta 2',
        },
        {
          'type': 'string',
          'field': 'selectProducts',
          'title': 'Select products',
        },
        {
          'type': 'string',
          'field': 'price',
          'title': 'Price',
        },
        {
          'type': 'string',
          'field': 'productInfo',
          'title': 'Product info',
        }
      ],
    },
    {
      'type': 'group',
      'field': 'processPaymentModal',
      'title': 'Process payment modal',
      'items': [
        {
          'type': 'image',
          'field': 'loader',
          'title': 'Loader',
        },
        {
          'type': 'string',
          'field': 'title',
          'title': 'Title',
        },
        {
          'type': 'string',
          'field': 'content',
          'title': 'Content',
        },
        {
          'type': 'string',
          'field': 'ok',
          'title': 'Ok',
        }
      ],
    },
    {
      'type': 'group',
      'field': 'chDisclaimer',
      'title': 'CH disclaimer',
      'items': [
        {
          'type': 'string',
          'field': 'title',
          'title': 'Title',
        },
        {
          'type': 'code',
          'field': 'text',
          'title': 'Text',
        },
      ],
    },
    {
      'type': 'group',
      'field': 'errors',
      'title': 'Errors',
      'items': [
        {
          "type": "spa-feature",
          "field": "promoCodeErrors",
          "title": "Promo code errors",
        },
        {
          'type': 'spa-feature',
          'field': 'paymentErrors',
          'title': 'Payment errors',
        },
      ],
    },
    {
      'type': 'group',
      'field': 'outOfStockModal',
      'title': 'Out Of Stock Modal',
      'items': [
        {
          "type": "string",
          "field": "title",
          "title": "Title",
        },
        {
          'type': 'string',
          'field': 'chooseAnotherProduct',
          'title': 'Choose Another Product',
        },
        {
          'type': 'string',
          'field': 'allDataIsSaved',
          'title': 'All Data is Saved',
        },
        {
          'type': 'string',
          'field': 'chooseOtherProduct',
          'title': 'Choose Other Product',
        },
      ],
    },
  ],
  'other' : [
    {
      'type': 'string',
      'field': 'pageTitle',
      'title': 'Page Title',
    },
    {
      'type': 'string',
      'field': 'progressTitle',
      'title': 'Progress Bar Title',
    }
  ]
}
