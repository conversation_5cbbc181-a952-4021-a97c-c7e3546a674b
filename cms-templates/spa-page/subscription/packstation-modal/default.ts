import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';

export const packstationModalDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      'type': 'string',
      'field': 'close',
      'title': 'Close',
    },
    {
      'type': 'string',
      'field': 'title',
      'title': 'Title',
    },
    {
      'type': 'string',
      'field': 'description',
      'title': 'Description',
    },
    {
      'type': 'string',
      'field': 'ctaButtonText',
      'title': 'CTA button text',
    },
    {
      'type': 'string',
      'field': 'warning',
      'title': 'Warning',
    }
  ]
}
