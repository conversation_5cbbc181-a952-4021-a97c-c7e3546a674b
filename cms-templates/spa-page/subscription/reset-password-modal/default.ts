import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';

export const resetPasswordModalDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      "type": "string",
      "field": "title",
      "title": "Title"
    },
    {
      "type": "string",
      "field": "currentPassword",
      "title": "Current password label"
    },
    {
      "type": "string",
      "field": "newPassword",
      "title": "New password label"
    },
    {
      "type": "string",
      "field": "repeatPassword",
      "title": "Repeat password label"
    },
    {
      "type": "string",
      "field": "repeatPassword",
      "title": "Repeat password label"
    },
    {
      "type": "string",
      "field": "submitButton",
      "title": "Submit button"
    }
  ]
}
