import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';

export const invalidAddressModalDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      "type": "string",
      "field": "title",
      "title": "Title"
    },
    {
      "type": "string",
      "field": "description",
      "title": "Description"
    },
    {
      "type": "string",
      "field": "enteredAddress",
      "title": "Entered Address Title"
    },
    {
      "type": "string",
      "field": "submitButton",
      "title": "Submit button"
    },
    {
      "type": "string",
      "field": "closeButton",
      "title": "Close button"
    }
  ]
}
