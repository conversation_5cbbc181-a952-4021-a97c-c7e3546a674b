import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';

export const HAGPaymentPageDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      "type": "string",
      "field": "paymentTitle",
      "title": "Payment title"
    },
    {
      "type": "string",
      "field": "disclaimer",
      "title": "Disclaimer"
    },
    {
      "type": "string",
      "field": "disclaimerDiscount",
      "title": "Disclaimer Discount"
    },
    {
      "type": "string",
      "field": "titleDiscount",
      "title": "Title Discount"
    },
    {
      "type": "string",
      "field": "free",
      "title": "Delivery Free"
    },
    {
      "type": "string",
      "field": "gift",
      "title": "Gift"
    },
    {
      "type": "string",
      "field": "totalPriceTitle",
      "title": "Total price title"
    },
    {
      "type": "string",
      "field": "paymentMethodsTitle",
      "title": "Payment Methods Title"
    },
    {
      "type": "link",
      "field": "agb",
      "title": "AGB Title"
    },
    {
      "type": "string",
      "field": "policy",
      "title": "Policy Title"
    },
    {
      "type": "group",
      "field": "promo",
      "title": "Promo Code",
      "items": [
        {
          "type": "string",
          "field": "title",
          "title": "Title"
        },
        {
          "type": "string",
          "field": "placeholder",
          "title": "Placeholder"
        },
        {
          "type": "string",
          "field": "label",
          "title": "Label"
        },
        {
          "type": "string",
          "field": "buttonText",
          "title": "Button Text"
        },
      ]
    },
    {
      "type": "group",
      "field": "methods",
      "title": "Payment methods",
      "items": [
        {
          "type": "string",
          "field": "fibonatix",
          "title": "Fibonatix"
        },
        {
          "type": "string",
          "field": "googlePay",
          "title": "GooglePay"
        },
        {
          "type": "string",
          "field": "applePay",
          "title": "Apple pay"
        },
        {
          "type": "string",
          "field": "braintreeCC",
          "title": "Credit Card Braintree"
        },
        {
          "type": "string",
          "field": "braintreePaypal",
          "title": "PayPal Braintree"
        },
        {
          "type": "string",
          "field": "braintreePaypalDescription",
          "title": "PayPal Braintree Description"
        },
        {
          "type": "string",
          "field": "klarnaPayLater",
          "title": "Klarna Rechnung"
        },
        {
          "type": "string",
          "field": "klarnaDirectDebit",
          "title": "Klarna Lastschrift"
        },
        {
          "type": "string",
          "field": "klarnaPayOverTime",
          "title": "Klarna over time"
        },
        {
          "type": "string",
          "field": "klarnaDirectBankTransfer",
          "title": "Klarna direct bank transfer"
        },
        {
          "type": "string",
          "field": "trustly",
          "title": "Trustly"
        },
        {
          "type": "string",
          "field": "offline",
          "title": "Bankwire"
        },
        {
          "type": "string",
          "field": "paypalMultibanco",
          "title": "Paypal Multibanco"
        },
      ]
    },
    {
      "type": "group",
      "field": "klarna",
      "title": "Klarna",
      "items": [
        {
          "type": "string",
          "field": "submitButton",
          "title": "Submit Button Title"
        },
      ]
    },
    {
      "type": "group",
      "field": "offline",
      "title": "Bankwire",
      "items": [
        {
          "type": "string",
          "field": "submitButton",
          "title": "Submit Button Title"
        },
      ]
    },
    {
      "type": "group",
      "field": "brainTree",
      "title": "BrainTree",
      "items": [
        {
          "type": "string",
          "field": "description",
          "title": "Card payment description"
        },
        {
          "type": "string",
          "field": "chargeOnYourBill",
          "title": "The charge will appear on your bill as"
        },
        {
          "type": "string",
          "field": "newCard",
          "title": "Add new card"
        },
        {
          "type": "string",
          "field": "expirationDate",
          "title": "Expiration date"
        },
        {
          "type": "string",
          "field": "CVVTooltip",
          "title": "CVV tooltip"
        },
        {
          "type": "string",
          "field": "CVV",
          "title": "CVV"
        },
        {
          "type": "string",
          "field": "paySecurelyNow",
          "title": "Pay securely now"
        },
        {
          "type": "string",
          "field": "selectCard",
          "title": "Select card"
        },
        {
          "type": "string",
          "field": "cardNumber",
          "title": "Card number"
        },
        {
          "type": "string",
          "field": "cardNumberTooltip",
          "title": "Card number tooltip"
        },
        {
          "type": "string",
          "field": "cardHolderName",
          "title": "Card holder name"
        },
        {
          "type": "string",
          "field": "cardHolderTooltip",
          "title": "Card holder tooltip"
        },
        {
          "type": "string",
          "field": "paymentCard",
          "title": "Payment card"
        },
        {
          "type": "string",
          "field": "expired",
          "title": "Expires"
        },
        {
          "type": "string",
          "field": "cardExpired",
          "title": "Card Expired"
        },
        {
          "type": "string",
          "field": "selectAnotherCard",
          "title": "Select another card"
        },
        {
          "type": "string",
          "field": "saveCard",
          "title": "Save card"
        }
      ]
    },
    {
      'type': 'group',
      'field': 'multibanco',
      'title': 'Multibanco',
      'items': [
        {
          'type': 'text',
          'field': 'advanceBankTransfer',
          'title': 'Advance bank transfer',
        },
        {
          'type': 'string',
          'field': 'payButton',
          'title': 'Pay button',
        }
      ],
    },
    {
      'type': 'group',
      'field': 'errors',
      'title': 'Errors',
      'items': [
        {
          "type": "spa-feature",
          "field": "paymentErrors",
          "title": "Payment errors",
        },
      ],
    },
    {
      "type": "feature",
      "field": "contacts",
      "title": "Contacts"
    }
  ]
}
