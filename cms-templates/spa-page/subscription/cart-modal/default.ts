import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';

export const cartModalDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      "type": "string",
      "field": "title",
      "title": "Title"
    },
    {
      "type": "string",
      "field": "get",
      "title": "Get"
    },
    {
      "type": "string",
      "field": "packs",
      "title": "Packs"
    },
    {
      "type": "string",
      "field": "every",
      "title": "Every"
    },
    {
      "type": "string",
      "field": "one_month",
      "title": "One month(subscription period)"
    },
    {
      "type": "string",
      "field": "months",
      "title": "Months(subscription period)"
    },
    {
      "type": "string",
      "field": "1months",
      "title": "1 month subscription name"
    },
    {
      "type": "string",
      "field": "3months",
      "title": "3 months subscription name"
    },
    {
      "type": "string",
      "field": "6months",
      "title": "6 months subscription name"
    },
    {
      "type": "string",
      "field": "total",
      "title": "Total"
    }
  ]
}
