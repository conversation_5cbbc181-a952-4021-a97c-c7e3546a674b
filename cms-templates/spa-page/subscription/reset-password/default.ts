import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';

export const ResetPasswordDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      "type": "string",
      "field": "mainTitle",
      "title": "Main title"
    },
    {
      "type": "string",
      "field": "mainDesc",
      "title": "Main Desc"
    },
    {
      "type": "string",
      "field": "emailLabel",
      "title": "Email input label"
    },
    {
      "type": "string",
      "field": "emailPlaceholder",
      "title": "Email input placeholder"
    },
    {
      "type": "string",
      "field": "requiredFieldsHint",
      "title": "Required fields hint"
    },
    {
      "type": "string",
      "field": "sendEmailBtn",
      "title": "Send email button"
    },
    {
      "type": "string",
      "field": "hint",
      "title": "Hint under CTA"
    },
    {
      "type": "string",
      "field": "mailSentText",
      "title": "Mail sent hint"
    },
    {
      "type": "string",
      "field": "backToLogin",
      "title": "Back to login"
    },
    {
      "type": "string",
      "field": "resetYourPasswordTitle",
      "title": "Reset your password title"
    },
    {
      "type": "string",
      "field": "newPassword",
      "title": "New password input label"
    },
    {
      "type": "string",
      "field": "newPasswordPlaceholder",
      "title": "New password input placeholder"
    },
    {
      "type": "string",
      "field": "repeatPassword",
      "title": "Repeat password input label"
    },
    {
      "type": "string",
      "field": "repeatPasswordPlaceholder",
      "title": "Repeat password input placeholder"
    },
    {
      "type": "string",
      "field": "resetPasswordBtn",
      "title": "Reset password button"
    },
    {
      "type": "string",
      "field": "passwordWasResetTitle",
      "title": "Password was reset title"
    },
    {
      "type": "string",
      "field": "continueFlowBtn",
      "title": "Continue flow button"
    },
    {
      "type": "group",
      "field": "errors",
      "title": "Errors",
      "items": [
        {
          "type": "string",
          "field": "passwordInvalid",
          "title": "New password is invalid"
        },
        {
          "type": "string",
          "field": "confirmPasswordNotMatch",
          "title": "Passwords not match"
        },
        {
          "type": "string",
          "field": "tokenInvalid",
          "title": "Token is invalid"
        },
        {
          "type": "string",
          "field": "emailInvalid",
          "title": "Email is invalid"
        },
        {
          "type": "string",
          "field": "emailAlreadySent",
          "title": "Email already sent"
        }
      ]
    }
  ]
};
