import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';

export const otcPaymentPageDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      "type": "string",
      "field": "choosePaymentTitle",
      "title": "Choose payment title"
    },
    {
      "type": "string",
      "field": "orderBillTitle",
      "title": "Order bill title"
    },
    {
      "type": "string",
      "field": "morePaymentOptions",
      "title": "More payment options"
    },
    {
      "type": "string",
      "field": "paymentProcessingTitle",
      "title": "Payment processing title"
    },
    {
      "type": "string",
      "field": "paymentProcessingDesc",
      "title": "Payment processing desc"
    },
    {
      "type": "string",
      "field": "submitButton",
      "title": "Submit button"
    },
    {
      "type": "string",
      "field": "get",
      "title": "Get"
    },
    {
      "type": "string",
      "field": "packs",
      "title": "Packs"
    },
    {
      "type": "string",
      "field": "every",
      "title": "Every"
    },
    {
      "type": "string",
      "field": "one_month",
      "title": "One month(subscription period)"
    },
    {
      "type": "string",
      "field": "months",
      "title": "Months(subscription period)"
    },
    {
      "type": "string",
      "field": "1months",
      "title": "1 month subscription name"
    },
    {
      "type": "string",
      "field": "3months",
      "title": "3 months subscription name"
    },
    {
      "type": "string",
      "field": "6months",
      "title": "6 months subscription name"
    },
    {
      "type": "string",
      "field": "productTotalPriceTitle",
      "title": "Product total price title"
    },
    {
      "type": "string",
      "field": "firstDeliveryTitle",
      "title": "First delivery title"
    },
    {
      "type": "string",
      "field": "deliveredBy",
      "title": "Delivered by"
    },
    {
      "type": "string",
      "field": "deliveredByOneTime",
      "title": "Delivered by One Time Buy"
    },
    {
      "type": "string",
      "field": "shippedTo",
      "title": "Shipped to"
    },
    {
      "type": "string",
      "field": "billingAddress",
      "title": "Billing address"
    },
    {
      "type": "string",
      "field": "editButon",
      "title": "Edit button"
    },
    {
      "type": "string",
      "field": "orderReservationTitle",
      "title": "Order reservation hint title"
    },
    {
      "type": "string",
      "field": "orderReservationDescBold",
      "title": "Order reservation bold description"
    },
    {
      "type": "text",
      "field": "orderReservationDesc",
      "title": "Order reservation description"
    },
    {
      "type": "string",
      "field": "shippingSameAsBilling",
      "title": "Shipping address same as billing"
    },
    {
      "type": "string",
      "field": "notion",
      "title": "Notion"
    },
    {
      "type": "group",
      "field": "methods",
      "title": "Payment methods",
      "items": [
        {
          "type": "string",
          "field": "fibonatix",
          "title": "Fibonatix"
        },
        {
          "type": "string",
          "field": "klarna_pay_later",
          "title": "Rechnung"
        },
        {
          "type": "string",
          "field": "google_pay",
          "title": "GooglePay"
        },
        {
          "type": "string",
          "field": "apple_pay",
          "title": "Apple pay"
        },
        {
          "type": "string",
          "field": "braintree_credit_card",
          "title": "Credit Card Braintree"
        },
        {
          "type": "string",
          "field": "braintree_paypal",
          "title": "PayPal Braintree"
        },
        {
          "type": "string",
          "field": "klarna_direct_debit",
          "title": "Lastschrift"
        },
        {
          "type": "string",
          "field": "klarna_pay_over_time",
          "title": "Klarna over time"
        },
        {
          "type": "string",
          "field": "klarna_direct_bank_transfer",
          "title": "Klarna direct bank transfer"
        },
        {
          "type": "string",
          "field": "trustly",
          "title": "Trustly"
        },
        {
          "type": "string",
          "field": "offline",
          "title": "Bankwire"
        },
        {
          "type": "string",
          "field": "paypal_multibanco",
          "title": "Paypal Multibanco"
        },
        {
          "type": "string",
          "field": "paypal_direct",
          "title": "Paypal Direct"
        },
      ]
    },
    {
      "type": "group",
      "field": "brainTree",
      "title": "BrainTree",
      "items": [
        {
          "type": "string",
          "field": "description",
          "title": "Card payment description"
        },
        {
          "type": "string",
          "field": "chargeOnYourBill",
          "title": "The charge will appear on your bill as"
        },
        {
          "type": "string",
          "field": "newCard",
          "title": "Add new card"
        },
        {
          "type": "string",
          "field": "expirationDate",
          "title": "Expiration date"
        },
        {
          "type": "string",
          "field": "expirationDateTooltip",
          "title": "Expiration date tooltip"
        },
        {
          "type": "string",
          "field": "CVVDesc",
          "title": "CVV2/CVC2 description"
        },
        {
          "type": "string",
          "field": "CVVTooltip",
          "title": "CVV tooltip"
        },
        {
          "type": "string",
          "field": "CVV",
          "title": "CVV"
        },
        {
          "type": "string",
          "field": "paySecurelyNow",
          "title": "Pay securely now"
        },
        {
          "type": "string",
          "field": "selectCard",
          "title": "Select card"
        },
        {
          "type": "string",
          "field": "cardNumber",
          "title": "Card number"
        },
        {
          "type": "string",
          "field": "cardNumberTooltip",
          "title": "Card number tooltip"
        },
        {
          "type": "string",
          "field": "cardHolderName",
          "title": "Card holder name"
        },
        {
          "type": "string",
          "field": "cardHolderTooltip",
          "title": "Card holder tooltip"
        },
        {
          "type": "string",
          "field": "paymentCard",
          "title": "Payment card"
        },
        {
          "type": "string",
          "field": "expired",
          "title": "Expires"
        },
        {
          "type": "string",
          "field": "selectAnotherCard",
          "title": "Select another card"
        },
        {
          "type": "string",
          "field": "saveCard",
          "title": "Save card for further purchases"
        },
        {
          "type": "string",
          "field": "paypalDescription",
          "title": "Paypal description"
        }
      ]
    },
    {
      'type': 'group',
      'field': 'multibanco',
      'title': 'Multibanco',
      'items': [
        {
          'type': 'text',
          'field': 'advanceBankTransfer',
          'title': 'Advance bank transfer',
        },
        {
          'type': 'string',
          'field': 'payButton',
          'title': 'Pay button',
        }
      ],
    },
    {
      'type': 'group',
      'field': 'errors',
      'title': 'Errors',
      'items': [
        {
          "type": "spa-feature",
          "field": "paymentErrors",
          "title": "Payment errors",
        },
      ],
    },
    {
      "type": "feature",
      "field": "contacts",
      "title": "Contacts"
    }
  ]
}
