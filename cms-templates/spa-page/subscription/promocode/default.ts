import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';

export const promocodeDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      "type": "string",
      "field": "title",
      "title": "Title"
    },
    {
      "type": "string",
      "field": "description",
      "title": "Description"
    },
    {
      "type": "string",
      "field": "submitButton",
      "title": "Submit button"
    },
    {
      "type": "group",
      "field": "errors",
      "title": "Errors",
      "items": [
        {
          "type": "spa-feature",
          "field": "promoCodeErrors",
          "title": "Promo code errors",
        }
      ]
    }
  ]
}
