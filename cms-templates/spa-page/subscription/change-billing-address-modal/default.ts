import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';

export const changeBillingAddressModalDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      "type": "string",
      "field": "title",
      "title": "Title"
    },
    {
      "type": "string",
      "field": "shippingSameAsBilling",
      "title": "Shipping same as billing address"
    },
    {
      "type": "string",
      "field": "countryLabel",
      "title": "Country label"
    },
    {
      "type": "string",
      "field": "countryHint",
      "title": "Country hint"
    },
    {
      "type": "string",
      "field": "zipLabel",
      "title": "Zip label"
    },
    {
      "type": "string",
      "field": "cityLabel",
      "title": "City label"
    },
    {
      "type": "string",
      "field": "streetLabel",
      "title": "Street label"
    },
    {
      "type": "string",
      "field": "houseNumberLabel",
      "title": "House number label"
    },
    {
      "type": "string",
      "field": "submitButton",
      "title": "Submit button"
    }
  ]
}
