import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';

export const verificationModalDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      "type": "string",
      "field": "title",
      "title": "Title"
    },
    {
      "type": "string",
      "field": "warning",
      "title": "Warning Text"
    },
    {
      'type': 'string',
      'field': 'rulesTitle',
      'title': 'Rules Title',
    },
    {
      'type': 'group',
      'field': 'rules',
      'title': 'Rules',
      'items': [
        {
          "type": "repeater",
          "field": "items",
          "title": "Items",
          "items": [
            {
              "type": "string",
              "field": "item",
              "title": "Item"
            },
          ]
        }
      ]
    },
    {
      'type': 'string',
      'field': 'reasonsTitle',
      'title': 'Reasons Title',
    },
    {
      'type': 'group',
      'field': 'reasons',
      'title': 'Reasons',
      'items': [
        {
          "type": "repeater",
          "field": "items",
          "title": "Items",
          "items": [
            {
              "type": "string",
              "field": "item",
              "title": "Item"
            },
          ]
        }
      ]
    },
    {
      'type': 'group',
      'field': 'certificatesIcons',
      'title': 'Certificates Icons',
      'items': [
        {
          "type": "repeater",
          "field": "items",
          "title": "Items",
          "items": [
            {
              "type": "image",
              "field": "item",
              "title": "Item"
            },
          ]
        }
      ]
    },
    {
      "type": "string",
      "field": "scanDesktop",
      "title": "Scan Desktop"
    },
    {
      "type": "string",
      "field": "scanMobile",
      "title": "Scan Mobile"
    },
    {
      "type": "string",
      "field": "startVerification",
      "title": "Start Verification"
    },
    {
      "type": "string",
      "field": "progressTitle",
      "title": "Progress Title"
    },
    {
      "type": "string",
      "field": "progressText",
      "title": "Progress Text"
    },
    {
      "type": "string",
      "field": "successTitle",
      "title": "Success Title"
    },
    {
      "type": "string",
      "field": "successText",
      "title": "Success Text"
    },
    {
      "type": "string",
      "field": "failedTitle",
      "title": "Failed Title"
    },
    {
      "type": "string",
      "field": "failedText",
      "title": "Failed Text"
    },
    {
      'type': 'string',
      'field': 'thankYou',
      'title': 'Thank you',
    },
    {
      'type': 'string',
      'field': 'thankYouTitle',
      'title': 'Thank you title',
    },
  ]
}
