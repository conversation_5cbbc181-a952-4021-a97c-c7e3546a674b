import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';

export const loginPageDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      "type": "text",
      "field": "tabPasswordTitle",
      "title": "Tab Password Title"
    },
    {
      "type": "text",
      "field": "tabOTPTitle",
      "title": "Tab OTP Title"
    },
    {
      "type": "group",
      "field": "password",
      "title": "Password Mode",
      "items": [
        {
          "type": "text",
          "field": "title",
          "title": "Title"
        },
        {
          "type": "string",
          "field": "subtitle",
          "title": "Subtitle"
        },
        {
          "type": "string",
          "field": "genderErrorTitle",
          "title": "Gender error title"
        },
        {
          "type": "string",
          "field": "genderErrorNotification",
          "title": "Gender error notification"
        },
        {
          "type": "group",
          "field": "googleOAuth",
          "title": "Google OAuth",
          "items": [
            {
              "type": "string",
              "field": "GoogleSignIn",
              "title": "Google SignIn"
            },
            {
              "type": "string",
              "field": "or",
              "title": "OR"
            },
          ]
        },
        {
          "type": "string",
          "field": "emailLabel",
          "title": "Email Label"
        },
        {
          "type": "string",
          "field": "EmailPlaceholder",
          "title": "Email placeholder"
        },
        {
          "type": "string",
          "field": "PasswordLabel",
          "title": "Password Label"
        },
        {
          "type": "string",
          "field": "PasswordPlaceholder",
          "title": "Password Placehoolder"
        },
        {
          "type": "string",
          "field": "requiredFieldsHint",
          "title": "Required Hint"
        },
        {
          "type": "string",
          "field": "submitButton",
          "title": "Submit button"
        },
        {
          "type": "string",
          "field": "RegistrationTextPart1",
          "title": "Registration Text Part 1"
        },
        {
          "type": "string",
          "field": "RegistrationTextPart2",
          "title": "Registration Text Part 2"
        },
        {
          "type": "string",
          "field": "RegistrationLink",
          "title": "Registration Link"
        },
        {
          "type": "string",
          "field": "newHaGAccount",
          "title": "Account will also be set up for H&G"
        },
        {
          "type": "string",
          "field": "resetPassword",
          "title": "Reset Password"
        },
      ]
    },
    {
      "type": "group",
      "field": "OTP",
      "title": "OTP Mode",
      "items": [
        {
          "type": "string",
          "field": "title",
          "title": "Title"
        },
        {
          "type": "string",
          "field": "desc",
          "title": "Description"
        },
        {
          "type": "image",
          "field": "spin",
          "title": "spin image"
        },
        {
          "type": "string",
          "field": "usePasswordInstead",
          "title": "Use password instead"
        },
        {
          'type': 'group',
          'field': 'form',
          'title': 'Form',
          'items': [
            {
              "type": "string",
              "field": "emailPlaceholder",
              "title": "Email Placeholder"
            },
            {
              "type": "string",
              "field": "email",
              "title": "Email"
            }
          ],
        },
        {
          "type": "string",
          "field": "sendBtn",
          "title": "Send email button"
        },
        {
          "type": "string",
          "field": "registrationTextPart1",
          "title": "Registration Text Part 1"
        },
        {
          "type": "string",
          "field": "registrationTextPart2",
          "title": "Registration Text Part 2"
        },
        {
          "type": "string",
          "field": "registrationLink",
          "title": "Registration Link"
        },
        {
          "type": "string",
          "field": "sentTitle",
          "title": "Email sent title"
        },
        {
          "type": "string",
          "field": "sentDesc",
          "title": "Email sent desc"
        },
        {
          "type": "string",
          "field": "changeEmailLink",
          "title": "Change email link"
        },
        {
          "type": "string",
          "field": "errorHint",
          "title": "Error hint"
        },
        {
          "type": "string",
          "field": "submitCodeBtn",
          "title": "Submit OTP button"
        },
        {
          "type": "string",
          "field": "resendEmailBtn",
          "title": "Resend email button"
        },
        {
          "type": "string",
          "field": "openGmail",
          "title": "Open Gmail"
        },
        {
          "type": "string",
          "field": "openOutlook",
          "title": "Open Outlook"
        }
      ]
    },

    {
      "type": "group",
      "field": "errors",
      "title": "Errors",
      "items": [
        {
          "type": "string",
          "field": "emailRequired",
          "title": "Email is required"
        },
        {
          "type": "string",
          "field": "emailInvalid",
          "title": "Email is invalid"
        },
        {
          "type": "string",
          "field": "passwordRequired",
          "title": "Password is required"
        },
        {
          "type": "string",
          "field": "passwordMinLength",
          "title": "Password min length"
        },
        {
          "type": "string",
          "field": "passwordMaxLength",
          "title": "Password max length"
        },
        {
          "type": "string",
          "field": "PasswordEmpty",
          "title": "Password is empty"
        },
        {
          "type": "string",
          "field": "incorrectCredentials",
          "title": "Incorrect Credentials"
        },
        {
          "type": "string",
          "field": "accountDisabled",
          "title": "Account disabled"
        },
        {
          "type": "string",
          "field": "OTPCodeEmpty",
          "title": "OTP code is empty"
        },
        {
          "type": "string",
          "field": "OTPCodeInvalid",
          "title": "OTP code is invalid"
        },
        {
          "type": "string",
          "field": "OTPCodeExpired",
          "title": "OTP code is expired"
        },
        {
          "type": "string",
          "field": "OTPTooManyRequests",
          "title": "Too many requests for OTP"
        },
      ]
    }
  ]
}
