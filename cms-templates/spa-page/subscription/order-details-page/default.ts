import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';

export const orderDetailsPageDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      "type": "string",
      "field": "title",
      "title": "Title"
    },
    {
      "type": "string",
      "field": "get",
      "title": "Get"
    },
    {
      "type": "string",
      "field": "packs",
      "title": "Packs"
    },
    {
      "type": "string",
      "field": "every",
      "title": "Every"
    },
    {
      "type": "string",
      "field": "one_month",
      "title": "One month(subscription period)"
    },
    {
      "type": "string",
      "field": "months",
      "title": "Months(subscription period)"
    },
    {
      "type": "string",
      "field": "1months",
      "title": "1 month subscription name"
    },
    {
      "type": "string",
      "field": "3months",
      "title": "3 months subscription name"
    },
    {
      "type": "string",
      "field": "6months",
      "title": "6 months subscription name"
    },
    {
      "type": "string",
      "field": "total",
      "title": "Total"
    },
    {
      "type": "string",
      "field": "submitButton",
      "title": "Submit button"
    },
    {
      "type": "string",
      "field": "deliveryTitle",
      "title": "Delivery title"
    },
    {
      "type": "string",
      "field": "deliveryDesc",
      "title": "Delivery desc"
    },
    {
      "type": "string",
      "field": "deliveryDescOneTime",
      "title": "Delivery desc for OneTime"
    },
    {
      "type": "string",
      "field": "orderCancelationTitle",
      "title": "Order cancelation title"
    },
    {
      "type": "string",
      "field": "orderCancelationDesc",
      "title": "Order cancelation desc"
    },
    {
      "type": "string",
      "field": "notion",
      "title": "Notion"
    }
  ]
}
