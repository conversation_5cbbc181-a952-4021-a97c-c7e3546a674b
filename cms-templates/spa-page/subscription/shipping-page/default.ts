import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';

export const shippingPageDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      "type": "string",
      "field": "title",
      "title": "Title"
    },
    {
      "type": "text",
      "field": "shippingPageWelcomeTitle",
      "title": "Shipping Page Welcome Title (HTML friendly)"
    },
    {
      "type": "string",
      "field": "shippingPageWelcomeSubtitle",
      "title": "Shipping Page Welcome Subtitle"
    },
    {
      "type": "string",
      "field": "parientDetailsFormTitle",
      "title": "Patient Details Form Title"
    },
    {
      "type": "string",
      "field": "deliveryMethodTitle",
      "title": "Delivery Method Title"
    },
    {
      "type": "string",
      "field": "shippingDetailsFormTitle",
      "title": "Shipping Details Form Title"
    },
    {
      "type": "string",
      "field": "billingAddressSameAsShipping",
      "title": "Billing Address Same as Shipping"
    },
    {
      "type": "string",
      "field": "deliveryDetailsTitle",
      "title": "Delivery Details Title"
    },
    {
      "type": "string",
      "field": "packstationDetailsTitle",
      "title": "Packstation Details Title"
    },
    {
      "type": "string",
      "field": "packStationListTitle",
      "title": "Packstation List Title"
    },
    {
      "type": "string",
      "field": "packStationFreeDeliveryText",
      "title": "PackStation Free Delivery Text"
    },
    {
      "type": "string",
      "field": "billingDetailsTitle",
      "title": "Billing Details Title"
    },
    {
      "type": "group",
      "field": "termsAndConditions",
      "title": "Terms And Conditions",
      "items": [
        {
          "type": "repeater",
          "field": "links",
          "title": "Links",
          "items": [
            {
              "type": "string",
              "field": "key",
              "title": "Link key",
            },
            {
              "type": "link",
              "field": "link",
              "title": "Link",
            }
          ]
        },
        {
          "type": "string",
          "field": "iAgreeTermsAndConditions",
          "title": "I Agree Terms and Conditions (HTML friendly)"
        },
      ]
    },
    {
      "type": "string",
      "field": "residentialTab",
      "title": "Residential Tab"
    },
    {
      "type": "string",
      "field": "dhlTab",
      "title": "DHL Tab"
    },
    {
      "type": "string",
      "field": "billingAddressTitle",
      "title": "Billing address title"
    },
    {
      "type": "string",
      "field": "packstationInfo",
      "title": "Packstation title"
    },
    {
      "type": "string",
      "field": "companyNameInfo",
      "title": "Company Name hidden field info"
    },
    {
      "type": "string",
      "field": "companyNameHint",
      "title": "Company Name field hint"
    },
    {
      "type": "string",
      "field": "submitButton",
      "title": "Submit button"
    },
    {
      "type": "group",
      "field": "deliveryTypes",
      "title": "Delivery Types",
      "items": [
        {
          "type": "string",
          "field": "freeDeliveryText",
          "title": "Free Delivery Text"
        },
        {
          "type": "string",
          "field": "deliveryTimeTypeStandart",
          "title": "Delivery Time Type Standart"
        },
        {
          "type": "string",
          "field": "deliveryTimeTypeExpress",
          "title": "Delivery Time Type Express"
        },
        {
          "type": "string",
          "field": "deliveryTimeTypeExpressTemperatureBoxRegular",
          "title": "Delivery Time Type Express Temperature Box Regular"
        },
        {
          "type": "string",
          "field": "deliveryTimeTypeStandardTemperatureBoxRegular",
          "title": "Delivery Time Type Standard Temperature Box Regular"
        },
        {
          "type": "string",
          "field": "deliveryTimeTypeExpressTemperatureBoxTemperatureController",
          "title": "Delivery Time Type Express Temperature Box Temperature Controller"
        },
        {
          "type": "string",
          "field": "deliveryTimeTypeStandardTemperatureBoxTemperatureController",
          "title": "Delivery Time Type Standard Temperature Box Temperature Controller"
        },
      ]
    },
    {
      "type": "group",
      "field": "delivery",
      "title": "Deliver To block",
      "items": [
        {
          "type": "string",
          "field": "title",
          "title": "Title"
        },
        {
          "type": "string",
          "field": "editButton",
          "title": "Edit button"
        },
        {
          "type": "string",
          "field": "doneButton",
          "title": "Done button"
        },
        {
          "type": "string",
          "field": "hint",
          "title": "Hint"
        }
      ]
    },
    {
      "type": "group",
      "field": "form",
      "title": "Form",
      "items": [
        {
          "type": "string",
          "field": "firstName",
          "title": "First name"
        },
        {
          "type": "string",
          "field": "lastName",
          "title": "Last name"
        },
        {
          "type": "string",
          "field": "email",
          "title": "Email"
        },
        {
          "type": "string",
          "field": "country",
          "title": "Country"
        },
        {
          "type": "string",
          "field": "countryHint",
          "title": "Country Hint"
        },
        {
          "type": "string",
          "field": "zipCode",
          "title": "Zip code"
        },
        {
          "type": "string",
          "field": "city",
          "title": "City"
        },
        {
          "type": "string",
          "field": "street",
          "title": "Street"
        },
        {
          "type": "string",
          "field": "houseNumber",
          "title": "House Number"
        },
        {
          "type": "string",
          "field": "additionalAddress",
          "title": "Additional address"
        },
        {
          "type": "string",
          "field": "postNumber",
          "title": "Post number"
        },
        {
          "type": "string",
          "field": "postNumberHint",
          "title": "Post number hint"
        },
        {
          "type": "string",
          "field": "phone",
          "title": "Phone"
        },
        {
          "type": "string",
          "field": "phoneHint",
          "title": "Phone Hint"
        },
        {
          "type": "string",
          "field": "birthDate",
          "title": "Birth date"
        },
        {
          "type": "string",
          "field": "birthDateHint",
          "title": "Birth date hint"
        },
        {
          "type": "string",
          "field": "bdPlaceholderDay",
          "title": "Birth date day placeholder"
        },
        {
          "type": "string",
          "field": "bdPlaceholderMonth",
          "title": "Birth date month placeholder"
        },
        {
          "type": "string",
          "field": "bdPlaceholderYear",
          "title": "Birth date year placeholder"
        }
      ]
    },
    {
      "type": "group",
      "field": "errors",
      "title": "Errors",
      "items": [
        {
          "type": "string",
          "field": "zipNotFound",
          "title": "Zip not found"
        },
        {
          "type": "string",
          "field": "deliveryCountryRequired",
          "title": "Delivery country required"
        },
        {
          "type": "string",
          "field": "deliveryCountryInvalid",
          "title": "Delivery country invalid"
        },
        {
          "type": "string",
          "field": "deliveryStreetRequired",
          "title": "Delivery street required"
        },
        {
          "type": "string",
          "field": "deliveryStreetInvalid",
          "title": "Delivery street invalid"
        },
        {
          "type": "string",
          "field": "deliveryHouseRequired",
          "title": "Deliveryh ouse required"
        },
        {
          "type": "string",
          "field": "deliveryHouseInvalid",
          "title": "Delivery house invalid"
        },
        {
          "type": "string",
          "field": "deliveryCityRequired",
          "title": "Delivery city required"
        },
        {
          "type": "string",
          "field": "deliveryCityInvalid",
          "title": "Delivery city invalid"
        },
        {
          "type": "string",
          "field": "deliveryZipRequired",
          "title": "Delivery zip required"
        },
        {
          "type": "string",
          "field": "deliveryZipInvalid",
          "title": "Delivery zip invalid"
        },
        {
          "type": "string",
          "field": "deliveryZipOnly4",
          "title": "Delivery zip only 4 numbers"
        },
        {
          "type": "string",
          "field": "deliveryZipOnly5",
          "title": "Delivery zip only 5 numbers"
        },
        {
          "type": "string",
          "field": "billingCountryRequired",
          "title": "Billing country required"
        },
        {
          "type": "string",
          "field": "billingCountryInvalid",
          "title": "Billing country invlid"
        },
        {
          "type": "string",
          "field": "billingStreetRequired",
          "title": "Billing street required"
        },
        {
          "type": "string",
          "field": "billingStreetInvalid",
          "title": "Billing street invlid"
        },
        {
          "type": "string",
          "field": "billingHouseRequired",
          "title": "Billing house required"
        },
        {
          "type": "string",
          "field": "billingHouseInvalid",
          "title": "Billing house invlid"
        },
        {
          "type": "string",
          "field": "billingCityRequired",
          "title": "Billing city required"
        },
        {
          "type": "string",
          "field": "billingCityInvalid",
          "title": "Billing city invlid"
        },
        {
          "type": "string",
          "field": "billingZipRequired",
          "title": "Billing zip required"
        },
        {
          "type": "string",
          "field": "billingZipInvalid",
          "title": "Billing zip invlid"
        },
        {
          "type": "string",
          "field": "billingZipOnly4",
          "title": "Billing zip only 4 numbers"
        },
        {
          "type": "string",
          "field": "billingZipOnly5",
          "title": "Billing zip only 5 numbers"
        },
        {
          "type": "string",
          "field": "birthDateRequired",
          "title": "Birth date required"
        },
        {
          "type": "string",
          "field": "birthDateAdult",
          "title": "Birth date adult"
        },
        {
          "type": "string",
          "field": "birthDateInvalid",
          "title": "Birth date invalid"
        },
        {
          "type": "string",
          "field": "birthDateInvalidYear",
          "title": "Birth date invalid year"
        },
        {
          "type": "string",
          "field": "packstationNumberInvalid",
          "title": "Packstation number invalid"
        },
        {
          "type": "string",
          "field": "packstationNumberRequired",
          "title": "Packstation number required"
        },
        {
          "type": "string",
          "field": "packstationPostnumberInvalid",
          "title": "Packstation post number invalid"
        },
        {
          "type": "string",
          "field": "packstationPostnumberRequired",
          "title": "Packstation post number required"
        },
        {
          "type": "string",
          "field": "packstationZipInvalid",
          "title": "Packstation zip invalid"
        },
        {
          "type": "string",
          "field": "packstationZipRequired",
          "title": "Packstation zip required"
        },
        {
          "type": "string",
          "field": "packstationZipOnly4",
          "title": "Packstation zip only 4 numbers"
        },
        {
          "type": "string",
          "field": "packstationZipOnly5",
          "title": "Packstation zip only 5 numbers"
        },
        {
          "type": "string",
          "field": "packstationCityInvalid",
          "title": "Packstation city invalid"
        },
        {
          "type": "string",
          "field": "packstationCityRequired",
          "title": "Packstation city required"
        },
        {
          "type": "string",
          "field": "isDifferentAddressConflict",
          "title": "Is different address cnflict"
        },
        {
          "type": "string",
          "field": "carrierRequired",
          "title": "Carrier required"
        },
        {
          "type": "string",
          "field": "deliveryTypeRequired",
          "title": "Delivery type required"
        },
        {
          "type": "string",
          "field": "phoneMin",
          "title": "Phone min length"
        },
        {
          "type": "string",
          "field": "phoneMax",
          "title": "Phone max length"
        },
        {
          "type": "string",
          "field": "phoneInvalid",
          "title": "Phone invalid"
        },
        {
          "type": "string",
          "field": "termsAndConditionsAgreementRequired",
          "title": "Terms and Conditions agreement required"
        },
      ]
    }
  ]
}
