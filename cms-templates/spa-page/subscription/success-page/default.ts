import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';

export const successPageDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      "type": "string",
      "field": "paymentReceived",
      "title": "Payment received"
    },
    {
      "type": "string",
      "field": "orderBeingProceed",
      "title": "Order is being Processed"
    },
    {
      "type": "string",
      "field": "title",
      "title": "Title"
    },
    {
      "type": "string",
      "field": "titleHaG",
      "title": "Title H&G"
    },
    {
      "type": "string",
      "field": "desc",
      "title": "Description"
    },
    {
      "type": "string",
      "field": "descHaG",
      "title": "DescriptionH&G"
    },
    {
      "type": "string",
      "field": "changeOrder",
      "title": "Need to change your order?"
    },
    {
      "type": "string",
      "field": "contactUsPart1",
      "title": "Contact us part 1"
    },
    {
      "type": "string",
      "field": "emailLink",
      "title": "Email link"
    },
    {
      "type": "string",
      "field": "email",
      "title": "EMAIL"
    },
    {
      "type": "string",
      "field": "contactUsPart2",
      "title": "Contact us part 2"
    },
    {
      "type": "string",
      "field": "manageSubscriptionText",
      "title": "Manage subscription text"
    },
    {
      "type": "string",
      "field": "manageSubscriptionLink",
      "title": "Manage subscription link"
    },
    {
      "type": "string",
      "field": "loginDetailsText",
      "title": "Login details text"
    },
    {
      "type": "string",
      "field": "accountCreated",
      "title": "Apomeds account has been created"
    },
    {
      "type": "string",
      "field": "passwordBelowToLogin",
      "title": "Password below to login"
    },
    {
      "type": "string",
      "field": "resetPassword",
      "title": "Reset your password"
    },
    {
      "type": "string",
      "field": "loginEmail",
      "title": "Login email"
    },
    {
      "type": "string",
      "field": "yourPassword",
      "title": "Your password"
    },
    {
      "type": "string",
      "field": "copy",
      "title": "Copy"
    },
    {
      "type": "string",
      "field": "reset",
      "title": "Reset"
    },
    {
      "type": "string",
      "field": "keepPasswordRecord",
      "title": "Keep a record of your password"
    },
    {
      "type": "string",
      "field": "returnToShop",
      "title": "Return to shop link"
    },
    {
      "type": "string",
      "field": "orderHistory",
      "title": "Order history link"
    },
    {
      "type": "string",
      "field": "similarProductsTitle",
      "title": "Similar products title"
    },
    {
      "type": "string",
      "field": "similarProductsPriceFrom",
      "title": "From"
    },
    {
      "type": "string",
      "field": "similarProductsPriceMonth",
      "title": "Per month"
    },
    {
      "type": "string",
      "field": "orderDetailsTitle",
      "title": "Order details title"
    },
    {
      "type": "string",
      "field": "order",
      "title": "Order"
    },
    {
      "type": "string",
      "field": "orderTotal",
      "title": "Order total"
    },
    {
      "type": "string",
      "field": "paymentMethods",
      "title": "Payment methods"
    },
    {
      "type": "string",
      "field": "shippedTo",
      "title": "Shipped to"
    },
    {
      "type": "string",
      "field": "firstShipmentExpectationText",
      "title": "First shipment expectation text"
    },
    {
      "type": "string",
      "field": "firstShipmentExpectationOneTimeText",
      "title": "First shipment expectation text for One Time Buy Flow"
    },
    {
      "type": "string",
      "field": "deliveredEvery",
      "title": "Delivered every text"
    },
    {
      "type": "string",
      "field": "deliveredEveryMonth",
      "title": "Month"
    },
    {
      "type": "string",
      "field": "one_month",
      "title": "One month delivery"
    },
    {
      "type": "string",
      "field": "nextPayment",
      "title": "Next payment"
    },
    {
      "type": "string",
      "field": "nextPaymentAt",
      "title": "At"
    },
    {
      "type": "string",
      "field": "prescriptionIncluded",
      "title": "Prescription included"
    },
    {
      "type": "string",
      "field": "showOrderDetails",
      "title": "Show order details"
    },
    {
      "type": "string",
      "field": "hideOrderDetails",
      "title": "Hide order details"
    },
    {
      "type": "string",
      "field": "product",
      "title": "Product"
    },
    {
      "type": "string",
      "field": "delivery",
      "title": "Delivery"
    },
    {
      "type": "string",
      "field": "free",
      "title": "Free"
    },
    {
      "type": "string",
      "field": "patientsName",
      "title": "Patient’s name"
    },
    {
      "type": "string",
      "field": "deliveryAddress",
      "title": "Delivery Address"
    },
    {
      "type": "string",
      "field": "continueShoppingButton",
      "title": "Continue Shopping Button"
    },
    {
      "type": "group",
      "field": "paymentMethod",
      "title": "Payment Method",
      "items": [
        {
          "type": "string",
          "field": "creditCard",
          "title": "Credit Card"
        },
        {
          "type": "string",
          "field": "payPal",
          "title": "Pay Pal"
        },
        {
          "type": "string",
          "field": "googlePay",
          "title": "Google Pay"
        },
        {
          "type": "string",
          "field": "applePay",
          "title": "Apple Pay"
        },
        {
          "type": "string",
          "field": "klarnaPayLater",
          "title": "Klarna Pay Later"
        },
        {
          "type": "string",
          "field": "klarnaDirectDebit",
          "title": "Klarna Direct Debit"
        },
      ]
    },
    {
      "type": "group",
      "field": "errors",
      "title": "Errors",
      "items": [
        {
          "type": "group",
          "field": "resetPassword",
          "title": "Reset password",
          "items": [
            {
              "type": "string",
              "field": "oldPassReqired",
              "title": "Old password reqired"
            },
            {
              "type": "string",
              "field": "oldPassIncorrect",
              "title": "Old password incorrect"
            },
            {
              "type": "string",
              "field": "passRequired",
              "title": "Password is required"
            },
            {
              "type": "string",
              "field": "passEmpty",
              "title": "Password is empty"
            },
            {
              "type": "string",
              "field": "passMinLength",
              "title": "Password min length"
            },
            {
              "type": "string",
              "field": "passMaxLength",
              "title": "Password max length"
            },
            {
              "type": "string",
              "field": "confirmPassRequired",
              "title": "Confirm password is required"
            },
            {
              "type": "string",
              "field": "confirmPassNotMatch",
              "title": "Confirm password is not match"
            }
          ]
        }
      ]
    }
  ]
}
