import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';

export const suggestAddressModalDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      'type': 'string',
      'field': 'title',
      'title': 'Title'
    },
    {
      "type": "string",
      "field": "description",
      "title": "Description"
    },
    {
      "type": "string",
      "field": "originalAddress",
      "title": "Original Address"
    },
    {
      "type": "string",
      "field": "suggestedAddress",
      "title": "Suggested Address"
    },
    {
      "type": "string",
      "field": "submitButton",
      "title": "Submit button"
    },
    {
      "type": "string",
      "field": "closeButton",
      "title": "Close button"
    }
  ]
}
