import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';

export const registrationPageDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      "type": "string",
      "field": "title",
      "title": "Title"
    },
    {
      "type": "string",
      "field": "your",
      "title": "Your"
    },
    {
      "type": "string",
      "field": "description1",
      "title": "Description part 1"
    },
    {
      "type": "string",
      "field": "description2",
      "title": "Description part 2"
    },
    {
      "type": "string",
      "field": "titleActiveCampaign",
      "title": "Title Active Campaign"
    },
    {
      "type": "group",
      "field": "googleOAuth",
      "title": "Google OAuth",
      "items": [
        {
          "type": "string",
          "field": "GoogleSignUp",
          "title": "Google SignUp"
        },
        {
          "type": "string",
          "field": "or",
          "title": "OR"
        },
      ]
    },
    {
      "type": "string",
      "field": "emailValidationIfActiveCampaign",
      "title": "Email Validation If ActiveCampaign"
    },
    {
      "type": "string",
      "field": "alreadyHaveAnAccountText",
      "title": "Already Have An Account?"
    },
    {
      "type": "string",
      "field": "alreadyHaveAnAccountLinkText",
      "title": "Sign up here"
    },
    {
      "type": "string",
      "field": "emailExistsErrorTitle",
      "title": "Email exists error title"
    },
    {
      "type": "string",
      "field": "emailExistsErrorTextPart1",
      "title": "Email exists error text part 1"
    },
    {
      "type": "string",
      "field": "emailExistsErrorTextPart2",
      "title": "Email exists error text part 2"
    },
    {
      "type": "string",
      "field": "emailExistsErrorLink",
      "title": "Email exists error link"
    },
    {
      "type": "string",
      "field": "firstNamePlaceholder",
      "title": "Firstname field placeholder"
    },
    {
      "type": "string",
      "field": "firstNameLabel",
      "title": "Firstname field label"
    },
    {
      "type": "string",
      "field": "lastNamePlaceholder",
      "title": "Lastname field placeholder"
    },
    {
      "type": "string",
      "field": "lastNameLabel",
      "title": "Lastname field label"
    },
    {
      "type": "string",
      "field": "emailPlaceholder",
      "title": "Email field placeholder"
    },
    {
      "type": "string",
      "field": "emailLabel",
      "title": "Email field label"
    },
    {
      "type": "string",
      "field": "requiredFieldsHint",
      "title": "Required fields hint"
    },
    {
      "type": "code",
      "field": "agreement",
      "title": "Agreement"
    },
    {
      "type": "repeater",
      "field": "agreementLinks",
      "title": "Agreement Links",
      "items": [
        {
          "type": "string",
          "field": "key",
          "title": "Link key",
        },
        {
          "type": "link",
          "field": "link",
          "title": "Link",
        }
      ]
    },
    {
      "type": "string",
      "field": "submitButton",
      "title": "Submit button"
    },
    {
      "type": "string",
      "field": "loginText",
      "title": "Login text"
    },
    {
      "type": "string",
      "field": "loginLink",
      "title": "Login link"
    },
    {
      "type": "string",
      "field": "newHaGAccount",
      "title": "Account will also be set up for H&G"
    },
    {
      "type": "group",
      "field": "errors",
      "title": "Errors",
      "items": [
        {
          "type": "string",
          "field": "firstNameRequired",
          "title": "First name is required"
        },
        {
          "type": "string",
          "field": "firstNameInvalid",
          "title": "First name is invalid"
        },
        {
          "type": "string",
          "field": "lastNameRequired",
          "title": "Last name is required"
        },
        {
          "type": "string",
          "field": "lastNameInvalid",
          "title": "Last name is invalid"
        },
        {
          "type": "string",
          "field": "tocRequired",
          "title": "TOC is reqired"
        },
        {
          "type": "string",
          "field": "emailRequired",
          "title": "Email is required "
        },
        {
          "type": "string",
          "field": "emailNotExists",
          "title": "Email not exists"
        },
        {
          "type": "string",
          "field": "emailInvalid",
          "title": "Email is invalid"
        },
        {
          "type": "string",
          "field": "emailSpecChars",
          "title": "Email spec characters invalid"
        },
        {
          "type": "string",
          "field": "emailWhitespaceInvalid",
          "title": "Email whitespace invalid"
        }
      ]
    }
  ]
}
