import { ConfigInterface } from '../../config.interface';
import { PrescriptionIncludedHighlightDefaultSchema } from './prescription-included-highlight/default';
import { choosePackageDefaultSchema } from "./choose-package/default";
import { paymentErrorsDefaultSchema } from './payment-errors/default';
import { promoCodeErrorsDefaultSchema } from './promocode-errors/default';

export const spaFeature: ConfigInterface[] = [
  {
    id: 'prescription-included-highlight',
    enabled: true,
    name: '[SPA FEATURE] Prescription included highlight',
    templates: [
      {
        locale: 'default',
        schema: PrescriptionIncludedHighlightDefaultSchema
      }
    ]
  },
  {
    id: 'choosePackage',
    enabled: true,
    name: '[SPA FEATURE] Choose Package',
    templates: [
      {
        locale: 'default',
        schema: choosePackageDefaultSchema
      }
    ]
  },
  {
    id: 'paymentErrors',
    enabled: true,
    name: '[SPA FEATURE] Payment Errors',
    templates: [
      {
        locale: 'default',
        schema: paymentErrorsDefaultSchema
      }
    ]
  },
  {
    id: 'promoCodeErrors',
    enabled: true,
    name: '[SPA FEATURE] Promo Code Errors',
    templates: [
      {
        locale: 'default',
        schema: promoCodeErrorsDefaultSchema
      }
    ]
  },
]
