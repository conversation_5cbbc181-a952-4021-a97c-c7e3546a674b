import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';

export const promoCodeErrorsDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      'type': 'string',
      'field': 'invalid',
      'title': 'Invalid',
    },
    {
      'type': 'string',
      'field': 'expired',
      'title': 'Expired',
    },
    {
      'type': 'string',
      'field': 'minOrderAmount',
      'title': 'Min order amount',
    },
    {
      'type': 'string',
      'field': 'forSubscriptions',
      'title': 'Only for subscriptions',
    },
    {
      'type': 'string',
      'field': 'wrongCategory',
      'title': 'Wrong category',
    },
    {
      'type': 'string',
      'field': 'wrongProduct',
      'title': 'Wrong product',
    },
    {
      'type': 'string',
      'field': 'wrongModification',
      'title': 'Wrong modification',
    },
    {
      'type': 'string',
      'field': 'limitPerCustomerUsage',
      'title': 'Limit per customer usage',
    },
    {
      'type': 'string',
      'field': 'limitActivation',
      'title': 'Limit activation',
    },
    {
      'type': 'string',
      'field': 'regularOrderOnly',
      'title': 'Regular order only',
    }
  ]
}
