import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';

export const paymentErrorsDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      'type': 'string',
      'field': 'invalidCreditCardNumber',
      'title': 'Invalid credit card number',
    },
    {
      'type': 'string',
      'field': 'invalidExpirationDate',
      'title': 'Invalid expiration date',
    },
    {
      'type': 'string',
      'field': 'cvvError',
      'title': 'CVV error',
    },
    {
      'type': 'string',
      'field': 'cardholderNameError',
      'title': 'Cardholder name error',
    },
    {
      'type': 'string',
      'field': 'cvvIsRequired',
      'title': 'CVV is required',
    },
    {
      'type': 'string',
      'field': 'cardExpiredError',
      'title': 'Card expired error',
    },
    {
      'type': 'string',
      'field': 'paymentDeclined',
      'title': 'Payment declined',
    },
    {
      'type': 'string',
      'field': 'cardWithout3ds',
      'title': 'Card without 3DS',
    },
    {
      'type': 'string',
      'field': 'paymentGeneralMessageError',
      'title': 'Payment general message error',
    },
    {
      'type': 'string',
      'field': 'doNotHonor',
      'title': 'Do not honor',
    },
    {
      'type': 'string',
      'field': 'insufficientFunds',
      'title': 'Insufficient funds',
    },
    {
      'type': 'string',
      'field': 'invalidCardNumber',
      'title': 'Invalid card number',
    },
    {
      'type': 'string',
      'field': 'invalidCvv',
      'title': 'Invalid CVV',
    },
    {
      'type': 'string',
      'field': 'cardIssuerDeclinedCvv',
      'title': 'Card Issuer Declined CVV',
    },
    {
      'type': 'string',
      'field': 'invalidExpiryDate',
      'title': 'Invalid expiry date',
    },
    {
      'type': 'string',
      'field': 'cardExpired',
      'title': 'Card expired',
    },
    {
      'type': 'string',
      'field': 'invalidCardType',
      'title': 'Invalid card type',
    },
    {
      'type': 'string',
      'field': 'failedDueToVelocityCheck',
      'title': 'Failed due to velocity check',
    },
    {
      'type': 'string',
      'field': 'failedDueToRiskManagementRules',
      'title': 'Failed due to risk management rules',
    },
    {
      'type': 'string',
      'field': 'transactionExceedsVolume',
      'title': 'The transaction exceeds volume cap per cardholder',
    },
    {
      'type': 'string',
      'field': 'transactionExceedsTransactionCount',
      'title': 'The transaction exceeds transaction count cap per cardholder',
    },
    {
      'type': 'string',
      'field': 'cardTypeNotSupported',
      'title': 'Card type not supported',
    },
    {
      'type': 'string',
      'field': 'wrongCardNumber',
      'title': 'Wrong card number',
    },
    {
      'type': 'string',
      'field': 'wrongCvv',
      'title': 'Wrong CVV',
    },
    {
      'type': 'string',
      'field': 'wrongExpiryDate',
      'title': 'Wrong expiry date',
    },
    {
      'type': 'string',
      'field': 'cardReportedAsStolen',
      'title': 'Card reported as stolen',
    },
    {
      'type': 'string',
      'field': 'cardReportedAsLost',
      'title': 'Card reported as lost',
    },
    {
      'type': 'string',
      'field': 'cardIsBlocked',
      'title': 'Card is blocked',
    },
    {
      'type': 'string',
      'field': 'wrongPersonalIdentificationNumber',
      'title': 'Wrong personal identification number',
    },
    {
      'type': 'string',
      'field': 'pickUpCard',
      'title': 'Pick up card',
    },
    {
      'type': 'string',
      'field': 'transactionIsNotPermittedOnCard',
      'title': 'Transaction is not permitted on card',
    },
    {
      'type': 'string',
      'field': 'cardIsRestricted',
      'title': 'Card is restricted',
    },
    {
      'type': 'string',
      'field': 'wrongCardholderName',
      'title': 'Wrong cardholder name',
    },
    {
      'type': 'string',
      'field': 'systemError',
      'title': 'System error',
    },
    {
      'type': 'string',
      'field': 'processorSystemCurrentlyDown',
      'title': 'Processor system currently down',
    },
    {
      'type': 'string',
      'field': 'generalError',
      'title': 'General error',
    },
    {
      'type': 'string',
      'field': 'declinedByValidation',
      'title': 'Declined by Validation',
    },
    {
      'type': 'string',
      'field': 'declinedByProcessor',
      'title': 'Declined by processor',
    },
    {
      'type': 'string',
      'field': 'declinedByIssuer',
      'title': 'Declined by issuer',
    },
    {
      'type': 'string',
      'field': 'unauthorizedMethod',
      'title': 'Unauthorized method',
    },
    {
      'type': 'string',
      'field': 'unauthorizedCurrency',
      'title': 'Unauthorized currency',
    },
    {
      'type': 'string',
      'field': 'markedAsPossibleFraud',
      'title': 'Transaction is marked as possible fraud by issuer',
    },
    {
      'type': 'string',
      'field': 'couldNotAuthenticateCardholder',
      'title': 'Could not authenticate cardholder',
    },
    {
      'type': 'string',
      'field': 'filteredByBlacklist',
      'title': 'Filtered by blacklist',
    },
    {
      'type': 'string',
      'field': 'creditCardBinBlacklisted',
      'title': 'Credit card bin blacklisted',
    },
    {
      'type': 'string',
      'field': 'cardBrandIsNotAllowed',
      'title': 'Card brand is not allowed',
    },
    {
      'type': 'string',
      'field': 'transactionWasNotAuthorized',
      'title': 'Transaction was not authorized',
    },
    {
      'type': 'string',
      'field': 'couldNotCompleteTheTransaction',
      'title': 'We could not complete the transaction',
    },
    {
      'type': 'string',
      'field': 'technicalError',
      'title': 'Technical error',
    },
    {
      'type': 'string',
      'field': 'monthlyCardLimitError',
      'title': 'Monthly card limit error',
    },
    {
      'type': 'string',
      'field': 'somethingWentWrong',
      'title': 'Something went wrong with your payment',
    }
  ]
}
