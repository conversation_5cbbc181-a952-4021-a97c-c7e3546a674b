import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';

export const choosePackageDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      "type": "string",
      "field": "modificationBestValue",
      "title": "Modification best value"
    },
    {
      "type": "string",
      "field": "productSavedPrice",
      "title": "Product saved price"
    },
    {
      "type": "string",
      "field": "productOneUnit",
      "title": "Product One Unit"
    },
    {
      "type": "string",
      "field": "currentPackage",
      "title": "Current package"
    },
    {
      "type": "string",
      "field": "morePackageSizes",
      "title": "More package sizes"
    },
    {
      "type": "string",
      "field": "switchAndSave",
      "title": "Switch and Save"
    }
  ]
}
