import { ConfigInterface } from '../config.interface';
import { ResetPasswordDefaultSchema } from './subscription/reset-password/default';
import { blockSuccessLeavingDefaultSchema } from './subscription/block-success-leaving/default';
import { cartModalDefaultSchema } from './subscription/cart-modal/default';
import { changeBillingAddressModalDefaultSchema } from './subscription/change-billing-address-modal/default';
import { deliveryFAQDefaultSchema } from './subscription/delivery-faq/default';
import { emptyBillingAddressModalDefaultSchema } from './subscription/empty-billing-address-modal/default';
import { freeDeliveryDefaultSchema } from './subscription/free-delivery/default';
import { genderErrorModalDefaultSchema } from './subscription/gender-error-modal/default';
import { HAGdeliveryFAQDefaultSchema } from './subscription/hag-delivery-faq/default';
import { headerDefaultSchema } from './subscription/header/default';
import { invalidAddressModalDefaultSchema } from './subscription/invalid-address-modal/default';
import { loginPageDefaultSchema } from './subscription/login-page/default';
import { orderDetailsPageDefaultSchema } from './subscription/order-details-page/default';
import { otcPaymentPageDefaultSchema } from './subscription/otc-payment-page/default';
import { paymentInProgressModalDefaultSchema } from './subscription/paymentIn-progress-modal/default';
import { promocodeDefaultSchema } from './subscription/promocode/default';
import { registrationPageDefaultSchema } from './subscription/registration-page/default';
import { resetPasswordModalDefaultSchema } from './subscription/reset-password-modal/default';
import { sessionExpiredModalDefaultSchema } from './shared/session-expired-modal/default';
import { shippingPageDefaultSchema } from './subscription/shipping-page/default';
import { successPageDefaultSchema } from './subscription/success-page/default';
import { suggestAddressModalDefaultSchema } from './subscription/suggest-address-modal/default';
import { SPA_CATEGORIES } from '../../shared/enums/spa-categories.enum';
import { verificationModalDefaultSchema } from './subscription/verification-modal/default';
import { privacyPolicyModalDefaultSchema } from './subscription/privacy-policy-modal/default';
import { termsAndConditionsModalDefaultSchema } from './subscription/terms-and-conditions-modal/default';
import { cookiePolicyModalDefaultSchema } from './subscription/cookie-policy-modal/default';
import { accountDetailsDefaultSchema } from './account/account-details/default';
import { orderHistoryDefaultSchema } from './account/order-history/default';
import { breadcrumbsDefaultSchema } from './account/breadcrumbs/default';
import { orderDetailsDefaultSchema } from './account/order-details/default';
import { subscriptionDetailsDefaultSchema } from './account/subscription-details/default';
import { subscriptionHistoryDefaultSchema } from './account/subscription-history/default';
import { subscriptionDeliveryLogModalDefaultSchema } from './account/subscription-delivery-log-modal/default';
import { accountDefaultSchema } from './account/account/default';
import { shipmentPageDefaultSchema } from './checkout/shipment-page/default';
import { stickyCartDefaultSchema } from './checkout/sticky-cart/default';
import { onlinePrescriptionBridgePageDefaultSchema } from './checkout/online-prescription-bridge-page/default';
import { confirmOfflinePrescriptionPageDefaultSchema } from './checkout/confirm-offline-prescription-page/default';
import { thankYouPageDefaultSchema } from './checkout/thank-you-page/default';
import { paymentDetailsPageDefaultSchema } from './checkout/payment-details-page/default';
import { exitIntentModalDefaultSchema } from './checkout/exit-intent-modal/default';
import { choosePackageV1DefaultSchema } from './checkout/choose-package-v1/default';
import { choosePackageV2DefaultSchema } from './checkout/choose-package-v2/default';
import { onlinePrescriptionBridgePageMinimalDefaultSchema } from './checkout/online-prescription-bridge-page-minimal/default';
import { chooseProductV2PageDefaultSchema } from './checkout/choose-product-v2-page/default';
import { findProductsDefaultSchema } from './checkout/find-products/default';
import { productMoreInfoDialogDefaultSchema } from './checkout/product-more-info-dialog/default';
import { chooseProductV1PageDefaultSchema } from './checkout/choose-product-v1-page/default';
import { footerDefaultSchema } from './checkout/footer/default';
import { unavailableProductPageDefaultSchema } from './checkout/unavailable-product-page/default';
import { unavailableCategoryPageDefaultSchema } from './checkout/unavailable-category-page/default';
import { findYourPillPageDefaultSchema } from './checkout/find-your-pill-page/default';
import { checkoutAuth } from './checkout/auth-page/default';
import { FAQDefaultSchema } from './checkout/faq/default';
import { onlinePrescriptionDeEdV2BridgePageDefaultSchema } from './checkout/online-prescription-de-ed-v2-bridge-page/default';
import { CheckoutCommonDefaultSchema } from './checkout/common/default';
import { SubscriptionCommonDefaultSchema } from './subscription/common/default';
import { AccountCommonDefaultSchema } from './account/common/default';
import { editShippingAddressSchema } from './account/edit-shipping-address/default';
import { informationUpdateDefaultSchema } from './information-update/default';
import {
  subscriptionsTermsAndConditionsModalDefaultSchema
} from './subscription/subscription-terms-and-conditions-modal/default';
import { packstationModalDefaultSchema } from './subscription/packstation-modal/default';
import { skipSubscriptionDefaultSchema } from './account/skip-subscription/default';
import { skipSubscriptionSuccessDefaultSchema } from './account/skip-subscription-success/default';
import { changeDeliveryFrequencySuccessDefaultSchema } from './account/change-delivery-frequency-success/default';
import { changeDeliveryFrequencyDefaultSchema } from './account/change-delivery-frequency/default';
import { changePackageDefaultSchema } from './account/change-package/default';
import { changePackageSuccessDefaultSchema } from './account/change-package-success/default';
import { choosePackageDefaultSchema } from './features/choose-package/default';
import { subscriptionCancelReasonsDefaultSchema } from './account/subscription-cancel-reasons/default';
import { subscriptionCancelCommentDefaultSchema } from './account/subscription-cancel-comment/default';
import { subscriptionCancelBenefitsDefaultSchema } from './account/subscription-cancel-benefits/default';
import { subscriptionCancelSuccessDefaultSchema } from './account/subscription-cancel-success/default';
import { subscriptionConfirmCancelDefaultSchema } from './account/subscription-confirm-cancel/default';
import { changeProductDefaultSchema } from './account/change-product/default';
import { changeProductSuccessDefaultSchema } from './account/change-product-success/default';
import { notificationRedirectToHomePageDefaultSchema } from './shared/notification-redirect-to-home-page/default';
import { accountSubscriptionCancelManageDefaultSchema } from './account/subscription-cancel-manage/default';
import { HAGPaymentPageDefaultSchema } from './subscription/hg-payment-page/default';
import { subscriptionsTermsAndConditionsModalV2DefaultSchema } from './subscription/subscription-terms-and-conditions-modal-v2/default';
import {
  onlinePrescriptionTransitionBridgePageDefaultSchema
} from './checkout/online-prescription-bridge-page-transition/default';
import { updatePaymentWrong } from './account/update-payment-wrong/default';
import { updatePaymentSuccess } from './account/update-payment-success/default';
import { updatePaymentMethod } from './account/update-payment-method/default';
import {
  onlinePrescriptionBridgePageWlChatDefaultSchema
} from './checkout/online-prescription-bridge-page-wl-chat/default';
import {
  onlinePrescriptionHagTransitionBridgePageDefaultSchema
} from './checkout/online-prescription-bridge-page-hag-transition/default';
import { subscriptionManagePageDefaultSchema } from './account/subscription-manage-page/default';
import { editAddressSuccessModalDefaultSchema } from './account/edit-address-success-modal/default';
import { editAddressErrorModalDefaultSchema } from './account/edit-address-error-modal/default';
import { onlinePrescriptionBridgePageWlDefaultSchema } from './checkout/online-prescription-bridge-page-wl/default';
import {
  onlinePrescriptionBridgePageWlExpertsModalDefaultSchema
} from './checkout/online-prescription-bridge-page-wl-experts-modal/default';
import {
  onlinePrescriptionDeEdV4BridgePageDefaultSchema
} from './checkout/online-prescription-bridge-page-de-ed-v4/default';
import { subscriptionCancelKeepSavingDefaultSchema } from './account/subscription-cancel-keep-saving/default';
import { subscriptionCancelDiscountDefaultSchema } from './account/subscription-cancel-discount/default';
import { exitIntentV2ModalDefaultSchema } from './checkout-v2/exit-intent-modal/default';
import { sessionExpiredV2ModalDefaultSchema } from './checkout-v2/session-expired-modal/default';
import { footerV2DefaultSchema } from './checkout-v2/footer/default';

export const spaPage: ConfigInterface[] = [
  {
    id: 'subscriptionCommon',
    enabled: true,
    name: '[SUBSCRIPTION] Common',
    category: SPA_CATEGORIES.SUBSCRIPTION,
    templates: [
      {
        locale: 'default',
        schema: SubscriptionCommonDefaultSchema
      }
    ]
  },
  {
    id: 'verificationModal',
    enabled: true,
    name: '[SUBSCRIPTION] verification modal',
    category: SPA_CATEGORIES.SUBSCRIPTION,
    templates: [
      {
        locale: 'default',
        schema: verificationModalDefaultSchema
      }
    ]
  },
  {
    id: 'packstationModal',
    enabled: true,
    name: '[SUBSCRIPTION] packstation modal',
    category: SPA_CATEGORIES.SUBSCRIPTION,
    templates: [
      {
        locale: 'default',
        schema: packstationModalDefaultSchema
      }
    ]
  },
  {
    id: 'privacyPolicyModal',
    enabled: true,
    name: '[SUBSCRIPTION] Privacy Policy Modal',
    category: SPA_CATEGORIES.SUBSCRIPTION,
    templates: [
      {
        locale: 'default',
        schema: privacyPolicyModalDefaultSchema
      }
    ]
  },
  {
    id: 'termsAndConditionsModal',
    enabled: true,
    name: '[SUBSCRIPTION] Terms and Conditions Modal',
    category: SPA_CATEGORIES.SUBSCRIPTION,
    templates: [
      {
        locale: 'default',
        schema: termsAndConditionsModalDefaultSchema
      }
    ]
  },
  {
    id: 'subscriptionsTermsAndConditionsModal',
    enabled: true,
    name: '[SUBSCRIPTION] Subscriptions Terms and Conditions Modal',
    category: SPA_CATEGORIES.SUBSCRIPTION,
    templates: [
      {
        locale: 'default',
        schema: subscriptionsTermsAndConditionsModalDefaultSchema
      }
    ]
  },
  {
    id: 'subscriptionsTermsAndConditionsModalV2',
    enabled: true,
    name: '[SUBSCRIPTION] Subscriptions Terms and Conditions Modal V2',
    category: SPA_CATEGORIES.SUBSCRIPTION,
    templates: [
      {
        locale: 'default',
        schema: subscriptionsTermsAndConditionsModalV2DefaultSchema
      }
    ]
  },
  {
    id: 'cookiePolicyModal',
    enabled: true,
    name: '[SUBSCRIPTION] Cookie Policy Modal',
    category: SPA_CATEGORIES.SUBSCRIPTION,
    templates: [
      {
        locale: 'default',
        schema: cookiePolicyModalDefaultSchema
      }
    ]
  },
  {
    id: 'resetPasswordPage',
    enabled: true,
    name: '[SUBSCRIPTION] Reset password',
    category: SPA_CATEGORIES.SUBSCRIPTION,
    templates: [
      {
        locale: 'default',
        schema: ResetPasswordDefaultSchema
      }
    ]
  },
  {
    id: 'blockSuccessLeaving',
    enabled: true,
    name: '[SUBSCRIPTION] Success leaving modal',
    category: SPA_CATEGORIES.SUBSCRIPTION,
    templates: [
      {
        locale: 'default',
        schema: blockSuccessLeavingDefaultSchema
      }
    ]
  },
  {
    id: 'cartModal',
    enabled: true,
    name: '[SUBSCRIPTION] Cart modal',
    category: SPA_CATEGORIES.SUBSCRIPTION,
    templates: [
      {
        locale: 'default',
        schema: cartModalDefaultSchema
      }
    ]
  },
  {
    id: 'changeBillingAddress',
    enabled: true,
    name: '[SUBSCRIPTION] Change billing address modal',
    category: SPA_CATEGORIES.SUBSCRIPTION,
    templates: [
      {
        locale: 'default',
        schema: changeBillingAddressModalDefaultSchema
      }
    ]
  },
  {
    id: 'deliveryFAQ',
    enabled: true,
    name: '[SUBSCRIPTION] delivery FAQ modal',
    category: SPA_CATEGORIES.SUBSCRIPTION,
    templates: [
      {
        locale: 'default',
        schema: deliveryFAQDefaultSchema
      }
    ]
  },
  {
    id: 'emptyBillingAddress',
    enabled: true,
    name: '[SUBSCRIPTION] empty billing address modal',
    category: SPA_CATEGORIES.SUBSCRIPTION,
    templates: [
      {
        locale: 'default',
        schema: emptyBillingAddressModalDefaultSchema
      }
    ]
  },
  {
    id: 'freeDelivery',
    enabled: true,
    name: '[SUBSCRIPTION] free delivery widget',
    category: SPA_CATEGORIES.SUBSCRIPTION,
    templates: [
      {
        locale: 'default',
        schema: freeDeliveryDefaultSchema
      }
    ]
  },
  {
    id: 'genderErrorModal',
    enabled: true,
    name: '[SUBSCRIPTION] gender error modal',
    category: SPA_CATEGORIES.SUBSCRIPTION,
    templates: [
      {
        locale: 'default',
        schema: genderErrorModalDefaultSchema
      }
    ]
  },
  {
    id: 'HAGdeliveryFAQ',
    enabled: true,
    name: '[SUBSCRIPTION] HAG delivery FAQ modal',
    category: SPA_CATEGORIES.SUBSCRIPTION,
    templates: [
      {
        locale: 'default',
        schema: HAGdeliveryFAQDefaultSchema
      }
    ]
  },
  {
    id: 'header',
    enabled: true,
    name: '[SUBSCRIPTION] header',
    category: SPA_CATEGORIES.SUBSCRIPTION,
    templates: [
      {
        locale: 'default',
        schema: headerDefaultSchema
      }
    ]
  },
  {
    id: 'invalidAddress',
    enabled: true,
    name: '[SUBSCRIPTION] invalid address modal',
    category: SPA_CATEGORIES.SUBSCRIPTION,
    templates: [
      {
        locale: 'default',
        schema: invalidAddressModalDefaultSchema
      }
    ]
  },
  {
    id: 'login',
    enabled: true,
    name: '[SUBSCRIPTION] login page',
    category: SPA_CATEGORIES.SUBSCRIPTION,
    templates: [
      {
        locale: 'default',
        schema: loginPageDefaultSchema
      }
    ]
  },
  {
    id: 'orderDetails',
    enabled: true,
    name: '[SUBSCRIPTION] order details page',
    category: SPA_CATEGORIES.SUBSCRIPTION,
    templates: [
      {
        locale: 'default',
        schema: orderDetailsPageDefaultSchema
      }
    ]
  },
  {
    id: 'otcPayment',
    enabled: true,
    name: '[SUBSCRIPTION] otc payment page',
    category: SPA_CATEGORIES.SUBSCRIPTION,
    templates: [
      {
        locale: 'default',
        schema: otcPaymentPageDefaultSchema
      }
    ]
  },
  {
    id: 'HAGPayment',
    enabled: true,
    name: '[SUBSCRIPTION] HAG payment page',
    category: SPA_CATEGORIES.SUBSCRIPTION,
    templates: [
      {
        locale: 'default',
        schema: HAGPaymentPageDefaultSchema
      }
    ]
  },
  {
    id: 'paymentInProgress',
    enabled: true,
    name: '[SUBSCRIPTION] payment in progress modal',
    category: SPA_CATEGORIES.SUBSCRIPTION,
    templates: [
      {
        locale: 'default',
        schema: paymentInProgressModalDefaultSchema
      }
    ]
  },
  {
    id: 'promocode',
    enabled: true,
    name: '[SUBSCRIPTION] promocode block',
    category: SPA_CATEGORIES.SUBSCRIPTION,
    templates: [
      {
        locale: 'default',
        schema: promocodeDefaultSchema
      }
    ]
  },
  {
    id: 'registration',
    enabled: true,
    name: '[SUBSCRIPTION] registration page',
    category: SPA_CATEGORIES.SUBSCRIPTION,
    templates: [
      {
        locale: 'default',
        schema: registrationPageDefaultSchema
      }
    ]
  },
  {
    id: 'resetPassword',
    enabled: true,
    name: '[SUBSCRIPTION] reset password modal',
    category: SPA_CATEGORIES.SUBSCRIPTION,
    templates: [
      {
        locale: 'default',
        schema: resetPasswordModalDefaultSchema
      }
    ]
  },
  {
    id: 'sessionExpiredModal',
    enabled: true,
    name: '[SUBSCRIPTION] session expired modal',
    category: SPA_CATEGORIES.SUBSCRIPTION,
    templates: [
      {
        locale: 'default',
        schema: sessionExpiredModalDefaultSchema
      }
    ]
  },
  {
    id: 'notificationRedirectToHomePage',
    enabled: true,
    name: '[SHARED] notification redirect to home page',
    category: SPA_CATEGORIES.SUBSCRIPTION,
    templates: [
      {
        locale: 'default',
        schema: notificationRedirectToHomePageDefaultSchema
      }
    ]
  },
  {
    id: 'shipping',
    enabled: true,
    name: '[SUBSCRIPTION] shipping page',
    category: SPA_CATEGORIES.SUBSCRIPTION,
    templates: [
      {
        locale: 'default',
        schema: shippingPageDefaultSchema
      }
    ]
  },
  {
    id: 'success',
    enabled: true,
    name: '[SUBSCRIPTION] success page',
    category: SPA_CATEGORIES.SUBSCRIPTION,
    templates: [
      {
        locale: 'default',
        schema: successPageDefaultSchema
      }
    ]
  },
  {
    id: 'suggestAddress',
    enabled: true,
    name: '[SUBSCRIPTION] suggest address modal',
    category: SPA_CATEGORIES.SUBSCRIPTION,
    templates: [
      {
        locale: 'default',
        schema: suggestAddressModalDefaultSchema
      }
    ]
  },
  {
    id: 'accountCommon',
    enabled: true,
    name: '[ACCOUNT] accountCommon',
    category: SPA_CATEGORIES.PROFILE,
    templates: [
      {
        locale: 'default',
        schema: AccountCommonDefaultSchema
      }
    ]
  },
  {
    id: 'accountBreadcrumbs',
    enabled: true,
    name: '[ACCOUNT] breadcrumbs',
    category: SPA_CATEGORIES.PROFILE,
    templates: [
      {
        locale: 'default',
        schema: breadcrumbsDefaultSchema
      }
    ]
  },
  {
    id: 'account',
    enabled: true,
    name: '[ACCOUNT] main',
    category: SPA_CATEGORIES.PROFILE,
    templates: [
      {
        locale: 'default',
        schema: accountDefaultSchema
      }
    ]
  },
  {
    id: 'accountDetails',
    enabled: true,
    name: '[ACCOUNT] details',
    category: SPA_CATEGORIES.PROFILE,
    templates: [
      {
        locale: 'default',
        schema: accountDetailsDefaultSchema
      }
    ]
  },
  {
    id: 'accountOrderDetails',
    enabled: true,
    name: '[ACCOUNT] order details',
    category: SPA_CATEGORIES.PROFILE,
    templates: [
      {
        locale: 'default',
        schema: orderDetailsDefaultSchema,
      }
    ]
  },
  {
    id: 'accountOrderHistory',
    enabled: true,
    name: '[ACCOUNT] order history',
    category: SPA_CATEGORIES.PROFILE,
    templates: [
      {
        locale: 'default',
        schema: orderHistoryDefaultSchema
      }
    ]
  },
  {
    id: 'accountSubscriptionDetails',
    enabled: true,
    name: '[ACCOUNT] subscriptions details',
    category: SPA_CATEGORIES.PROFILE,
    templates: [
      {
        locale: 'default',
        schema: subscriptionDetailsDefaultSchema
      }
    ]
  },
  {
    id: 'accountSubscriptionHistory',
    enabled: true,
    name: '[ACCOUNT] subscriptions history',
    category: SPA_CATEGORIES.PROFILE,
    templates: [
      {
        locale: 'default',
        schema: subscriptionHistoryDefaultSchema
      }
    ]
  },
  {
    id: 'accountSkipSubscription',
    enabled: true,
    name: '[ACCOUNT] skip subscriptions',
    category: SPA_CATEGORIES.PROFILE,
    templates: [
      {
        locale: 'default',
        schema: skipSubscriptionDefaultSchema
      }
    ]
  },
  {
    id: 'accountSkipSubscriptionSuccess',
    enabled: true,
    name: '[ACCOUNT] skip subscriptions success',
    category: SPA_CATEGORIES.PROFILE,
    templates: [
      {
        locale: 'default',
        schema: skipSubscriptionSuccessDefaultSchema
      }
    ]
  },
  {
    id: 'accountSubscriptionManagePage',
    enabled: true,
    name: '[ACCOUNT] subscriptions manage page',
    category: SPA_CATEGORIES.PROFILE,
    templates: [
      {
        locale: 'default',
        schema: subscriptionManagePageDefaultSchema
      }
    ]
  },
  {
    id: 'accountChangeDeliveryFrequency',
    enabled: true,
    name: '[ACCOUNT] change delivery frequency',
    category: SPA_CATEGORIES.PROFILE,
    templates: [
      {
        locale: 'default',
        schema: changeDeliveryFrequencyDefaultSchema
      }
    ]
  },
  {
    id: 'accountChangeDeliveryFrequencySuccess',
    enabled: true,
    name: '[ACCOUNT] change delivery frequency success',
    category: SPA_CATEGORIES.PROFILE,
    templates: [
      {
        locale: 'default',
        schema: changeDeliveryFrequencySuccessDefaultSchema
      }
    ]
  },
  {
    id: 'updatePaymentMethod',
    enabled: true,
    name: '[ACCOUNT] update payment method',
    category: SPA_CATEGORIES.PROFILE,
    templates: [
      {
        locale: 'default',
        schema: updatePaymentMethod
      }
    ]
  },
  {
    id: 'updatePaymentWrong',
    enabled: true,
    name: '[ACCOUNT] update payment wrong',
    category: SPA_CATEGORIES.PROFILE,
    templates: [
      {
        locale: 'default',
        schema: updatePaymentWrong
      }
    ]
  },
  {
    id: 'updatePaymentSuccess',
    enabled: true,
    name: '[ACCOUNT] update payment success',
    category: SPA_CATEGORIES.PROFILE,
    templates: [
      {
        locale: 'default',
        schema: updatePaymentSuccess
      }
    ]
  },
  {
    id: 'accountChangePackage',
    enabled: true,
    name: '[ACCOUNT] change package',
    category: SPA_CATEGORIES.PROFILE,
    templates: [
      {
        locale: 'default',
        schema: changePackageDefaultSchema
      }
    ]
  },
  {
    id: 'accountChangePackageSuccess',
    enabled: true,
    name: '[ACCOUNT] change package success',
    category: SPA_CATEGORIES.PROFILE,
    templates: [
      {
        locale: 'default',
        schema: changePackageSuccessDefaultSchema
      }
    ]
  },
  {
    id: 'accountChangeProduct',
    enabled: true,
    name: '[ACCOUNT] change product',
    category: SPA_CATEGORIES.PROFILE,
    templates: [
      {
        locale: 'default',
        schema: changeProductDefaultSchema
      }
    ]
  },
  {
    id: 'accountChangeProductSuccess',
    enabled: true,
    name: '[ACCOUNT] change product success',
    category: SPA_CATEGORIES.PROFILE,
    templates: [
      {
        locale: 'default',
        schema: changeProductSuccessDefaultSchema
      }
    ]
  },
  {
    id: 'accountSubscriptionConfirmCancel',
    enabled: true,
    name: '[ACCOUNT] subscription confirm cancel',
    category: SPA_CATEGORIES.PROFILE,
    templates: [
      {
        locale: 'default',
        schema: subscriptionConfirmCancelDefaultSchema
      }
    ]
  },
  {
    id: 'accountSubscriptionCancelReasons',
    enabled: true,
    name: '[ACCOUNT] subscription cancel reasons',
    category: SPA_CATEGORIES.PROFILE,
    templates: [
      {
        locale: 'default',
        schema: subscriptionCancelReasonsDefaultSchema
      }
    ]
  },
  {
    id: 'accountSubscriptionCancelKeepSaving',
    enabled: true,
    name: '[ACCOUNT] subscription keep saving',
    category: SPA_CATEGORIES.PROFILE,
    templates: [
      {
        locale: 'default',
        schema: subscriptionCancelKeepSavingDefaultSchema
      }
    ]
  },
  {
    id: 'accountSubscriptionCancelDiscount',
    enabled: true,
    name: '[ACCOUNT] subscription discount',
    category: SPA_CATEGORIES.PROFILE,
    templates: [
      {
        locale: 'default',
        schema: subscriptionCancelDiscountDefaultSchema
      }
    ]
  },
  {
    id: 'accountSubscriptionCancelComment',
    enabled: true,
    name: '[ACCOUNT] subscription cancel comment',
    category: SPA_CATEGORIES.PROFILE,
    templates: [
      {
        locale: 'default',
        schema: subscriptionCancelCommentDefaultSchema
      }
    ]
  },
  {
    id: 'accountSubscriptionCancelBenefits',
    enabled: true,
    name: '[ACCOUNT] subscription cancel benefits',
    category: SPA_CATEGORIES.PROFILE,
    templates: [
      {
        locale: 'default',
        schema: subscriptionCancelBenefitsDefaultSchema
      }
    ]
  },
  {
    id: 'accountSubscriptionCancelSuccess',
    enabled: true,
    name: '[ACCOUNT] subscription cancel success',
    category: SPA_CATEGORIES.PROFILE,
    templates: [
      {
        locale: 'default',
        schema: subscriptionCancelSuccessDefaultSchema
      }
    ]
  },
  {
    id: 'accountSubscriptionCancelManage',
    enabled: true,
    name: '[ACCOUNT] subscription cancel manage',
    category: SPA_CATEGORIES.PROFILE,
    templates: [
      {
        locale: 'default',
        schema: accountSubscriptionCancelManageDefaultSchema
      }
    ]
  },
  {
    id: 'accountSubscriptionDeliveryLogModal',
    enabled: true,
    name: '[ACCOUNT] subscription delivery log modal',
    category: SPA_CATEGORIES.PROFILE,
    templates: [
      {
        locale: 'default',
        schema: subscriptionDeliveryLogModalDefaultSchema
      }
    ]
  },
  {
    id: 'onlinePrescriptionBridgePageDefaultSchema',
    enabled: true,
    name: '[CHECKOUT] Online prescription bridge page',
    category: SPA_CATEGORIES.CHECKOUT,
    templates: [
      {
        locale: 'default',
        schema: onlinePrescriptionBridgePageDefaultSchema
      }
    ]
  },
  {
    id: 'onlinePrescriptionBridgePageMinimalDefaultSchema',
    enabled: true,
    name: '[CHECKOUT] Online prescription bridge page minimal',
    category: SPA_CATEGORIES.CHECKOUT,
    templates: [
      {
        locale: 'default',
        schema: onlinePrescriptionBridgePageMinimalDefaultSchema
      }
    ]
  },
  {
    id: 'onlinePrescriptionDeEdV2BridgePageDefaultSchema',
    enabled: true,
    name: '[CHECKOUT] Online prescription DE ED V2 bridge page',
    category: SPA_CATEGORIES.CHECKOUT,
    templates: [
      {
        locale: 'default',
        schema: onlinePrescriptionDeEdV2BridgePageDefaultSchema
      }
    ]
  },
  {
    id: 'onlinePrescriptionDeEdV4BridgePage',
    enabled: true,
    name: '[CHECKOUT] Online prescription DE ED V4 bridge page',
    category: SPA_CATEGORIES.CHECKOUT,
    templates: [
      {
        locale: 'default',
        schema: onlinePrescriptionDeEdV4BridgePageDefaultSchema
      }
    ]
  },
  {
    id: 'onlinePrescriptionTransitionBridgePage',
    enabled: true,
    name: '[CHECKOUT] Online prescription transition bridge page',
    category: SPA_CATEGORIES.CHECKOUT,
    templates: [
      {
        locale: 'default',
        schema: onlinePrescriptionTransitionBridgePageDefaultSchema
      }
    ]
  },
  {
    id: 'onlinePrescriptionHagTransitionBridgePage',
    enabled: true,
    name: '[CHECKOUT] Online prescription hag transition bridge page',
    category: SPA_CATEGORIES.CHECKOUT,
    templates: [
      {
        locale: 'default',
        schema: onlinePrescriptionHagTransitionBridgePageDefaultSchema
      }
    ]
  },
  {
    id: 'confirmOfflinePrescriptionPageDefaultSchema',
    enabled: true,
    name: '[CHECKOUT] Confirm offline prescription page',
    category: SPA_CATEGORIES.CHECKOUT,
    templates: [
      {
        locale: 'default',
        schema: confirmOfflinePrescriptionPageDefaultSchema
      }
    ]
  },
  {
    id: 'checkoutAuth',
    enabled: true,
    name: '[CHECKOUT] auth page',
    category: SPA_CATEGORIES.CHECKOUT,
    templates: [
      {
        locale: 'default',
        schema: checkoutAuth
      }
    ]
  },
  {
    id: 'successPage',
    enabled: true,
    name: '[CHECKOUT] success page',
    category: SPA_CATEGORIES.CHECKOUT,
    templates: [
      {
        locale: 'default',
        schema: thankYouPageDefaultSchema
      }
    ]
  },
  {
    id: 'choosePackageV1',
    enabled: true,
    name: '[CHECKOUT] Choose Package V1',
    category: SPA_CATEGORIES.CHECKOUT,
    templates: [
      {
        locale: 'default',
        schema: choosePackageV1DefaultSchema
      }
    ]
  },
  {
    id: 'choosePackageV2',
    enabled: true,
    name: '[CHECKOUT] Choose Package V2',
    category: SPA_CATEGORIES.CHECKOUT,
    templates: [
      {
        locale: 'default',
        schema: choosePackageV2DefaultSchema
      }
    ]
  },
  {
    id: 'checkoutFooter',
    enabled: true,
    name: '[CHECKOUT] [SHARED] footer',
    category: SPA_CATEGORIES.CHECKOUT,
    templates: [
      {
        locale: 'default',
        schema: footerDefaultSchema
      }
    ]
  },
  {
    id: 'shipmentPageDefaultSchema',
    enabled: true,
    name: '[CHECKOUT] Shipment page',
    category: SPA_CATEGORIES.CHECKOUT,
    templates: [
      {
        locale: 'default',
        schema: shipmentPageDefaultSchema
      }
    ]
  },
  {
    id: 'chooseProductV1Page',
    enabled: true,
    name: '[CHECKOUT] choose product v1 page',
    category: SPA_CATEGORIES.CHECKOUT,
    templates: [
      {
        locale: 'default',
        schema: chooseProductV1PageDefaultSchema
      }
    ]
  },
  {
    id: 'chooseProductV2Page',
    enabled: true,
    name: '[CHECKOUT] choose product v2 page',
    category: SPA_CATEGORIES.CHECKOUT,
    templates: [
      {
        locale: 'default',
        schema: chooseProductV2PageDefaultSchema
      }
    ]
  },
  {
    id: 'stickyCartDefaultSchema',
    enabled: true,
    name: '[CHECKOUT] Sticky cart component',
    category: SPA_CATEGORIES.CHECKOUT,
    templates: [
      {
        locale: 'default',
        schema: stickyCartDefaultSchema
      }
    ]
  },
  {
    id: 'findProduct',
    enabled: true,
    name: '[CHECKOUT] [SHARED] find product',
    category: SPA_CATEGORIES.CHECKOUT,
    templates: [
      {
        locale: 'default',
        schema: findProductsDefaultSchema
      }
    ]
  },
  {
    id: 'packageSelector',
    enabled: true,
    name: '[CHECKOUT] [SHARED] package selector',
    category: SPA_CATEGORIES.CHECKOUT,
    templates: [
      {
        locale: 'default',
        schema: choosePackageDefaultSchema
      }
    ]
  },
  {
    id: 'productMoreInfoDialog',
    enabled: true,
    name: '[CHECKOUT] [SHARED] product more info dialog',
    category: SPA_CATEGORIES.CHECKOUT,
    templates: [
      {
        locale: 'default',
        schema: productMoreInfoDialogDefaultSchema
      }
    ]
  },
  {
    id: 'paymentPage',
    enabled: true,
    name: '[CHECKOUT] payment details page',
    category: SPA_CATEGORIES.CHECKOUT,
    templates: [
      {
        locale: 'default',
        schema: paymentDetailsPageDefaultSchema
      }
    ]
  },
  {
    id: 'exitIntentModal',
    enabled: true,
    name: '[CHECKOUT] exit intent modal',
    category: SPA_CATEGORIES.CHECKOUT,
    templates: [
      {
        locale: 'default',
        schema: exitIntentModalDefaultSchema
      }
    ]
  },
  {
    id: 'unavailableProduct',
    enabled: true,
    name: '[CHECKOUT] Unavailable product page',
    category: SPA_CATEGORIES.CHECKOUT,
    templates: [
      {
        locale: 'default',
        schema: unavailableProductPageDefaultSchema
      }
    ]
  },
  {
    id: 'unavailableCategory',
    enabled: true,
    name: '[CHECKOUT] Unavailable category page',
    category: SPA_CATEGORIES.CHECKOUT,
    templates: [
      {
        locale: 'default',
        schema: unavailableCategoryPageDefaultSchema
      }
    ]
  },
  {
    id: 'findYourPill',
    enabled: true,
    name: '[CHECKOUT] find your pill page',
    category: SPA_CATEGORIES.CHECKOUT,
    templates: [
      {
        locale: 'default',
        schema: findYourPillPageDefaultSchema
      }
    ]
  },
  {
    id: 'checkoutFAQ',
    enabled: true,
    name: '[CHECKOUT] [SHARED] FAQ modal',
    category: SPA_CATEGORIES.CHECKOUT,
    templates: [
      {
        locale: 'default',
        schema: FAQDefaultSchema
      }
    ]
  },
  {
    id: 'checkoutCommon',
    enabled: true,
    name: '[CHECKOUT] Common',
    category: SPA_CATEGORIES.CHECKOUT,
    templates: [
      {
        locale: 'default',
        schema: CheckoutCommonDefaultSchema
      }
    ]
  },
  {
    id: 'successPageV2',
    enabled: true,
    name: '[CHECKOUT V2] success page',
    category: SPA_CATEGORIES.CHECKOUT,
    templates: [
      {
        locale: 'default',
        schema: successPageDefaultSchema
      }
    ]
  },
  {
    id: 'exitIntentV2Modal',
    enabled: true,
    name: '[CHECKOUT V2] exit intent modal',
    category: SPA_CATEGORIES.CHECKOUT,
    templates: [
      {
        locale: 'default',
        schema: exitIntentV2ModalDefaultSchema
      }
    ]
  },
  {
    id: 'sessionExpiredV2Modal',
    enabled: true,
    name: '[CHECKOUT V2] session expired modal',
    category: SPA_CATEGORIES.CHECKOUT,
    templates: [
      {
        locale: 'default',
        schema: sessionExpiredV2ModalDefaultSchema
      }
    ]
  },
  {
    id: 'checkoutFooterV2',
    enabled: true,
    name: '[CHECKOUT V2] [SHARED] footer',
    category: SPA_CATEGORIES.CHECKOUT,
    templates: [
      {
        locale: 'default',
        schema: footerV2DefaultSchema
      }
    ]
  },
  {
    id: 'editShippingAddress',
    enabled: true,
    name: '[ACCOUNT] edit shipping address',
    category: SPA_CATEGORIES.PROFILE,
    templates: [
      {
        locale: 'default',
        schema: editShippingAddressSchema
      }
    ]
  },
  {
    id: 'editAddressSuccessModal',
    enabled: true,
    name: '[ACCOUNT] edit address modal success',
    category: SPA_CATEGORIES.PROFILE,
    templates: [
      {
        locale: 'default',
        schema: editAddressSuccessModalDefaultSchema
      }
    ]
  },
  {
    id: 'editAddressErrorModal',
    enabled: true,
    name: '[ACCOUNT] edit address modal error',
    category: SPA_CATEGORIES.PROFILE,
    templates: [
      {
        locale: 'default',
        schema: editAddressErrorModalDefaultSchema
      }
    ]
  },
  {
    id: 'informationUpdate',
    enabled: true,
    name: '[INFORMATION UPDATE] Default',
    category: SPA_CATEGORIES.CHECKOUT,
    templates: [
      {
        locale: 'default',
        schema: informationUpdateDefaultSchema
      }
    ]
  },
  {
    id: 'onlinePrescriptionBridgePageWlChat',
    enabled: true,
    name: '[CHECKOUT] Online prescription bridge page with chat',
    category: SPA_CATEGORIES.CHECKOUT,
    templates: [
      {
        locale: 'default',
        schema: onlinePrescriptionBridgePageWlChatDefaultSchema,
      },
    ],
  },
  {
    id: 'onlinePrescriptionBridgePageWl',
    enabled: true,
    name: '[CHECKOUT] Online prescription bridge page WL',
    category: SPA_CATEGORIES.CHECKOUT,
    templates: [
      {
        locale: 'default',
        schema: onlinePrescriptionBridgePageWlDefaultSchema,
      },
    ],
  },
  {
    id: 'onlinePrescriptionBridgePageWlExpertsModal',
    enabled: true,
    name: '[CHECKOUT] Online prescription bridge page WL experts modal',
    category: SPA_CATEGORIES.CHECKOUT,
    templates: [
      {
        locale: 'default',
        schema: onlinePrescriptionBridgePageWlExpertsModalDefaultSchema,
      },
    ],
  },
];
