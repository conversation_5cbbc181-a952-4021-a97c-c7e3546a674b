import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';

export const successPageDefaultSchema: TemplatesViewConfigInterface = {
  'main': [
    {
      'type': 'group',
      'field': 'changePasswordModal',
      'title': 'Change Password Modal',
      'items': [
        {
          'type': 'string',
          'field': 'changePassword',
          'title': 'Change password'
        },
        {
          'type': 'string',
          'field': 'close',
          'title': 'close'
        },
        {
          'type': 'string',
          'field': 'currentPassword',
          'title': 'Current Password'
        },
        {
          'type': 'string',
          'field': 'newPassword',
          'title': 'New Password'
        },
        {
          'type': 'string',
          'field': 'repeatPassword',
          'title': 'Repeat password'
        },
        {
          'type': 'string',
          'field': 'apply',
          'title': 'Apply'
        }
      ]
    },
    {
      'type': 'group',
      'field': 'packstationModal',
      'title': 'Packstation modal',
      'items': [
        {
          'type': 'string',
          'field': 'title',
          'title': 'Title'
        },
        {
          'type': 'code',
          'field': 'description',
          'title': 'Description'
        },
        {
          'type': 'string',
          'field': 'ctaButtonText',
          'title': 'CTA button text'
        },
        {
          'type': 'string',
          'field': 'warning',
          'title': 'Warning'
        }
      ]
    },
    {
      'type': 'group',
      'field': 'requestPhoneModal',
      'title': 'Request phone modal',
      'items': [
        {
          'type': 'string',
          'field': 'phonePlaceholder',
          'title': 'Phone placeholder'
        },
        {
          'type': 'string',
          'field': 'title',
          'title': 'Title'
        },
        {
          'type': 'string',
          'field': 'description',
          'title': 'Description'
        },
        {
          'type': 'string',
          'field': 'addPhoneButton',
          'title': 'Add phone button'
        },
        {
          'type': 'string',
          'field': 'thankYou',
          'title': 'Thank you'
        },
        {
          'type': 'group',
          'field': 'requestPhoneFormErrors',
          'title': 'Request Phone Form Errors',
          'items': [
            {
              'type': 'string',
              'field': 'invalid',
              'title': 'Invalid phone format'
            },
            {
              'type': 'string',
              'field': 'minLength',
              'title': 'Min length error'
            },
            {
              'type': 'string',
              'field': 'maxLength',
              'title': 'Max length error'
            }
          ]
        }
      ]
    },
    {
      'type': 'group',
      'field': 'requestPhonePackstationModal',
      'title': 'Request phone and packstation modal',
      'items': [
        {
          'type': 'string',
          'field': 'title',
          'title': 'Title'
        },
        {
          'type': 'string',
          'field': 'required',
          'title': 'Required'
        },
        {
          'type': 'string',
          'field': 'received',
          'title': 'Received'
        },
        {
          'type': 'string',
          'field': 'description',
          'title': 'Description'
        },
        {
          'type': 'string',
          'field': 'enterPhone',
          'title': 'Enter phone'
        }
      ]
    },
    {
      'type': 'group',
      'field': 'paymentInfo',
      'title': 'Payment info',
      'items': [
        {
          'type': 'string',
          'field': 'bank',
          'title': 'Bank'
        },
        {
          'type': 'string',
          'field': 'remittanceAmount',
          'title': 'Remittance amount'
        },
        {
          'type': 'string',
          'field': 'beneficiaryName',
          'title': 'Beneficiary name'
        },
        {
          'type': 'string',
          'field': 'beneficiaryAddress',
          'title': 'Beneficiary address'
        },
        {
          'type': 'string',
          'field': 'bankName',
          'title': 'Bank name'
        },
        {
          'type': 'string',
          'field': 'bankAddress',
          'title': 'Bank address'
        },
        {
          'type': 'string',
          'field': 'accountNumber',
          'title': 'Account number'
        },
        {
          'type': 'string',
          'field': 'iban',
          'title': 'IBAN'
        },
        {
          'type': 'string',
          'field': 'swift',
          'title': 'SWIFT'
        },
        {
          'type': 'string',
          'field': 'paymentDetails',
          'title': 'Payment details'
        },
        {
          'type': 'string',
          'field': 'orderId',
          'title': 'Order id'
        },
        {
          'type': 'string',
          'field': 'sureOrderIdInPayment',
          'title': 'Make sure add order id in payment'
        },
        {
          'type': 'string',
          'field': 'downloadInvoice',
          'title': 'Download PDF Copy of Invoice'
        }
      ]
    },
    {
      'type': 'group',
      'field': 'bankWireHeader',
      'title': 'Bank Wire Header',
      'items': [
        {
          'type': 'string',
          'field': 'title',
          'title': 'Title'
        },
        {
          'type': 'string',
          'field': 'description',
          'title': 'Description'
        }
      ]
    },
    {
      'type': 'group',
      'field': 'commonOrderInfo',
      'title': 'Common order info',
      'items': [
        {
          'type': 'string',
          'field': 'order',
          'title': 'Order'
        },
        {
          'type': 'string',
          'field': 'prescriptionIncluded',
          'title': 'Prescription included hint'
        },
        {
          'type': 'string',
          'field': 'orderTotal',
          'title': 'Order Total'
        },
        {
          'type': 'string',
          'field': 'product',
          'title': 'Product'
        },
        {
          'type': 'string',
          'field': 'delivery',
          'title': 'Delivery'
        },
        {
          'type': 'string',
          'field': 'free',
          'title': 'Free'
        },
        {
          'type': 'string',
          'field': 'paymentMethod',
          'title': 'Payment Method'
        },
        {
          'type': 'string',
          'field': 'firstShipmentExpectationText',
          'title': 'First Shipment Expectation Text'
        },
        {
          'type': 'string',
          'field': 'firstShipmentExpectationOneTimeText',
          'title': 'First Shipment Expectation One Time Text'
        },
        {
          'type': 'string',
          'field': 'hideOrderDetails',
          'title': 'Hide Order Details'
        },
        {
          'type': 'string',
          'field': 'showOrderDetails',
          'title': 'Show Order Details'
        }
      ]
    },
    {
      'type': 'group',
      'field': 'genderVerification',
      'title': 'Gender verification',
      'items': [
        {
          'type': 'string',
          'field': 'verificationTitle',
          'title': 'Verification title'
        },
        {
          'type': 'string',
          'field': 'verificationSubtitle',
          'title': 'Verification subtitle'
        },
        {
          'type': 'string',
          'field': 'note',
          'title': 'Note'
        },
        {
          'type': 'string',
          'field': 'firstName',
          'title': 'First name'
        },
        {
          'type': 'string',
          'field': 'lastName',
          'title': 'Last name'
        },
        {
          'type': 'string',
          'field': 'submitButtonText',
          'title': 'Submit button text'
        },
        {
          'type': 'string',
          'field': 'cancelButtonText',
          'title': 'Cancel button text'
        },
        {
          'type': 'string',
          'field': 'confirmButtonText',
          'title': 'Confirm button text'
        },
        {
          'type': 'string',
          'field': 'updateButtonText',
          'title': 'Update button text'
        },
        {
          'type': 'string',
          'field': 'nameConfirmed',
          'title': 'Name confirmed'
        },
        {
          'type': 'string',
          'field': 'nameChanged',
          'title': 'Name changed'
        }
      ]
    },
    {
      'type': 'group',
      'field': 'multibancoInfo',
      'title': 'Multibanco info',
      'items': [
        {
          'type': 'string',
          'field': 'amount',
          'title': 'Amount'
        },
        {
          'type': 'string',
          'field': 'beneficiaryName',
          'title': 'Beneficiary name'
        },
        {
          'type': 'string',
          'field': 'referenceNumber',
          'title': 'Reference number'
        },
        {
          'type': 'string',
          'field': 'voucherExpirationDate',
          'title': 'Voucher expiration date'
        },
        {
          'type': 'string',
          'field': 'receiveApprovementOnEmail',
          'title': 'You will receive approvement on email'
        }
      ]
    },
    {
      'type': 'group',
      'field': 'placedOrderInfo',
      'title': 'Placed order info',
      'items': [
        {
          'type': 'string',
          'field': 'title',
          'title': 'Title'
        },
        {
          'type': 'string',
          'field': 'description',
          'title': 'Description'
        }
      ]
    },
    {
      'type': 'group',
      'field': 'redeemInfo',
      'title': 'Redeem info',
      'items': [
        {
          'type': 'string',
          'field': 'sendPaperRecipe',
          'title': 'Send paper recipe'
        },
        {
          'type': 'string',
          'field': 'sendMedication',
          'title': 'Send medication after prescription confirmed'
        }
      ]
    },
    {
      'type': 'group',
      'field': 'userInfo',
      'title': 'User info',
      'items': [
        {
          'type': 'string',
          'field': 'yourCredentials',
          'title': 'Your credentials'
        },
        {
          'type': 'string',
          'field': 'yourEmail',
          'title': 'Your email'
        },
        {
          'type': 'string',
          'field': 'yourPassword',
          'title': 'Your password'
        },
        {
          'type': 'string',
          'field': 'copyClipboard',
          'title': 'Copy clipboard'
        },
        {
          'type': 'string',
          'field': 'warningMessage',
          'title': 'Warning Message'
        },
        {
          'type': 'string',
          'field': 'changePassword',
          'title': 'Change password'
        },
        {
          'type': 'string',
          'field': 'patientDetails',
          'title': 'Patient Details'
        },
        {
          'type': 'string',
          'field': 'patientsName',
          'title': 'Patients Name'
        },
        {
          'type': 'string',
          'field': 'email',
          'title': 'Email'
        },
        {
          'type': 'string',
          'field': 'deliveryAddress',
          'title': 'Delivery Address'
        }
      ]
    },
    {
      'type': 'group',
      'field': 'verificationModal',
      'title': 'Verification modal',
      'items': [
        {
          'type': 'string',
          'field': 'failedTitle',
          'title': 'Failed title'
        },
        {
          'type': 'code',
          'field': 'failedText',
          'title': 'Failed text'
        },
        {
          'type': 'string',
          'field': 'inProgressTitle',
          'title': 'In progress title'
        },
        {
          'type': 'code',
          'field': 'inProgressText',
          'title': 'In progress text'
        },
        {
          'type': 'string',
          'field': 'thankYou',
          'title': 'Thank you'
        },
        {
          'type': 'code',
          'field': 'thankYouTitle',
          'title': 'Thank you title'
        },
        {
          'type': 'code',
          'field': 'warningText',
          'title': 'Warning text'
        },
        {
          'type': 'code',
          'field': 'rulesTitle',
          'title': 'Rules Title'
        },
        {
          'type': 'group',
          'field': 'rules',
          'title': 'Rules',
          'items': [
            {
              'type': 'repeater',
              'field': 'items',
              'title': 'Items',
              'items': [
                {
                  'type': 'string',
                  'field': 'item',
                  'title': 'Item'
                }
              ]
            }
          ]
        },
        {
          'type': 'string',
          'field': 'scanCodeText',
          'title': 'Scan code text'
        },
        {
          'type': 'code',
          'field': 'reasonsTitle',
          'title': 'Reasons Title'
        },
        {
          'type': 'group',
          'field': 'reasons',
          'title': 'Reasons',
          'items': [
            {
              'type': 'repeater',
              'field': 'items',
              'title': 'Items',
              'items': [
                {
                  'type': 'string',
                  'field': 'item',
                  'title': 'Item'
                }
              ]
            }
          ]
        },
        {
          'type': 'group',
          'field': 'certificatesIcons',
          'title': 'Certificates Icons',
          'items': [
            {
              'type': 'repeater',
              'field': 'items',
              'title': 'Items',
              'items': [
                {
                  'type': 'image',
                  'field': 'item',
                  'title': 'Item'
                }
              ]
            }
          ]
        },
        {
          'type': 'string',
          'field': 'startVerification',
          'title': 'Start verification'
        },
        {
          'type': 'string',
          'field': 'titleText',
          'title': 'Title text'
        }
      ]
    },
    {
      'type': 'group',
      'field': 'processedOrderModal',
      'title': 'Order has been processed modal',
      'items': [
        {
          'type': 'string',
          'field': 'orderHasBeenProcessedTitle',
          'title': 'Order has been processed title'
        },
        {
          'type': 'code',
          'field': 'contactToChangeOrder',
          'title': 'Contact to change order'
        },
        {
          'type': 'string',
          'field': 'createNewOrder',
          'title': 'Create new order'
        }
      ]
    },
    {
      'type': 'string',
      'field': 'continueShopping',
      'title': 'Continue shopping'
    },
    {
      'type': 'string',
      'field': 'orderHistory',
      'title': 'Order history'
    },
    {
      'type': 'group',
      'field': 'errors',
      'title': 'Errors',
      'items': [
        {
          'type': 'group',
          'field': 'resetPassword',
          'title': 'Reset password',
          'items': [
            {
              'type': 'string',
              'field': 'oldPassRequired',
              'title': 'Old password required'
            },
            {
              'type': 'string',
              'field': 'oldPassIncorrect',
              'title': 'Old password incorrect'
            },
            {
              'type': 'string',
              'field': 'passRequired',
              'title': 'Password is required'
            },
            {
              'type': 'string',
              'field': 'passEmpty',
              'title': 'Password is empty'
            },
            {
              'type': 'string',
              'field': 'passMinLength',
              'title': 'Password min length'
            },
            {
              'type': 'string',
              'field': 'passMaxLength',
              'title': 'Password max length'
            },
            {
              'type': 'string',
              'field': 'confirmPassRequired',
              'title': 'Confirm password is required'
            },
            {
              'type': 'string',
              'field': 'confirmPassNotMatch',
              'title': 'Confirm password is not match'
            }
          ]
        }
      ]
    }
  ],
  'other': [
    {
      'type': 'string',
      'field': 'pageTitle',
      'title': 'Page Title'
    }
  ]
};
