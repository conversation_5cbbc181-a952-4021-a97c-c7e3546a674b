import { TemplatesViewConfigInterface } from '../../../apps/website/src/app/api/v1/templates/templates.interface';

export const informationUpdateDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      "type": "group",
      "field": "form",
      "title": "Form",
      "items": [
        {
          "type": "string",
          "field": "title",
          "title": "Title",
        },
        {
          "type": "code",
          "field": "description",
          "title": "Description",
        },
        {
          "type": "string",
          "field": "hintRecommended",
          "title": "Hint recommended",
        },
        {
          "type": "string",
          "field": "hintChoose",
          "title": "Hint choose",
        },
        {
          "type": "string",
          "field": "preferDifferentDosage",
          "title": "Prefer different dosage",
        },
        {
          "type": "string",
          "field": "showMore",
          "title": "Show more",
        },
        {
          "type": "string",
          "field": "ctaHint",
          "title": "CTA hint",
        },
        {
          "type": "string",
          "field": "ctaText",
          "title": "CTA text",
        },
      ],
    },
    {
      "type": "group",
      "field": "success",
      "title": "Success screen",
      "items": [
        {
          "type": "image",
          "field": "image",
          "title": "Image",
        },
        {
          "type": "string",
          "field": "title",
          "title": "Title",
        },
        {
          "type": "code",
          "field": "description",
          "title": "Description",
        },
      ],
    },
    {
      "type": "group",
      "field": "expired",
      "title": "Expired screen",
      "items": [
        {
          "type": "image",
          "field": "image",
          "title": "Image",
        },
        {
          "type": "string",
          "field": "title",
          "title": "Title",
        },
        {
          "type": "code",
          "field": "description",
          "title": "Description",
        },
      ],
    },
  ]
}
