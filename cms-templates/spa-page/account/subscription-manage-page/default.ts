import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';
import { pageTitleSchema } from "../../../shared/pageTitle";

export const subscriptionManagePageDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      "type": "string",
      "field": "back",
      "title": "Back"
    },
    {
      "type": "string",
      "field": "title",
      "title": "Title"
    },
    {
      "type": "string",
      "field": "changeDeliveryFrequencyTitle",
      "title": "Change delivery frequency Title"
    },
    {
      "type": "string",
      "field": "changeDeliveryFrequencyDescription",
      "title": "Change delivery frequency Description"
    },
    {
      "type": "string",
      "field": "changePackageSizeTitle",
      "title": "Change package size Title"
    },
    {
      "type": "string",
      "field": "changePackageSizeDescription",
      "title": "Change package size Description"
    },
    {
      "type": "string",
      "field": "skipNextOrderTitle",
      "title": "Skip next order Title"
    },
    {
      "type": "string",
      "field": "skipNextOrderDescription",
      "title": "Skip next order Description"
    },
    {
      "type": "string",
      "field": "changeMedicationTitle",
      "title": "Change medication Title"
    },
    {
      "type": "string",
      "field": "changeMedicationDescription",
      "title": "Change medication Description"
    },
    {
      "type": "string",
      "field": "cancelSubscriptionTitle",
      "title": "Cancel subscription Title"
    },
    {
      "type": "string",
      "field": "tooltip",
      "title": "Tooltip"
    },
  ],
  "other": [
    pageTitleSchema,
  ]
}
