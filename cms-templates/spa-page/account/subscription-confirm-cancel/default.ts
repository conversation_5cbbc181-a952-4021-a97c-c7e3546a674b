import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';
import { pageTitleSchema } from "../../../shared/pageTitle";

export const subscriptionConfirmCancelDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      "type": "string",
      "field": "backButton",
      "title": "Back button",
    },
    {
      "type": "string",
      "field": "title",
      "title": "Title",
    },
    {
      "type": "string",
      "field": "description",
      "title": "Description",
    },
    {
      "type": "string",
      "field": "proceed",
      "title": "Proceed cancellation",
    },
    {
      "type": "string",
      "field": "keep",
      "title": "Keep Subscription",
    },
  ],
  "other": [
    pageTitleSchema,
  ]
}
