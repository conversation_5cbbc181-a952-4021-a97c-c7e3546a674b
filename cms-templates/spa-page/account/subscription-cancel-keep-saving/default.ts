import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';
import { pageTitleSchema } from "../../../shared/pageTitle";

export const subscriptionCancelKeepSavingDefaultSchema: TemplatesViewConfigInterface = {
  'main': [
    {
      'type': 'string',
      'field': 'title',
      'title': 'Title'
    },
    {
      'type': 'string',
      'field': 'changePackageSubtitle',
      'title': 'Change package subtitle',
    },
    {
      'type': 'string',
      'field': 'skipSubscriptionSubtitle',
      'title': 'Skip subscription subtitle',
    },
    {
      'type': 'string',
      'field': 'switchToGenericSubtitle',
      'title': 'Switch to generic subtitle',
    },
    {
      'type': 'string',
      'field': 'changePackageSize',
      'title': 'Change package size'
    },
    {
      'type': 'string',
      'field': 'skipNextOrder',
      'title': 'Skip next order'
    },
    {
      'type': 'string',
      'field': 'switchToGeneric',
      'title': 'Switch to generic',
    },
    {
      'type': 'string',
      'field': 'cancelAnyway',
      'title': 'Cancel anyway',
    },
  ],
  "other": [
    pageTitleSchema,
  ]
};
