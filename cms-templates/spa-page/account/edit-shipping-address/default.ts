import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';
import { pageTitleSchema } from "../../../shared/pageTitle";

export const editShippingAddressSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      "type": "feature",
      "field": "invalidAddressModal",
      "title": "Invalid Address Modal"
    },
    {
      "type": "feature",
      "field": "suggestAddressModal",
      "title": "Suggest Address Modal"
    },
    {
      "type": "string",
      "field": "country",
      "title": "Country"
    },
    {
      "type": "string",
      "field": "postcode",
      "title": "Postcode"
    },
    {
      "type": "string",
      "field": "city",
      "title": "City"
    },
    {
      "type": "string",
      "field": "street",
      "title": "Street"
    },
    {
      "type": "string",
      "field": "houseNumber",
      "title": "House Number"
    },
    {
      "type": "string",
      "field": "buildingCode",
      "title": "Building Code"
    },
    {
      "type": "string",
      "field": "additionalInfo",
      "title": "Additional Info"
    },
    {
      "type": "string",
      "field": "phoneNumberLabel",
      "title": "Phone Number Label"
    },
    {
      "type": "string",
      "field": "phoneNumberOptionalLabel",
      "title": "Phone Number Optional Label"
    },
    {
      "type": "string",
      "field": "checkboxLabel",
      "title": "Checkbox Label"
    },
    {
      "type": "string",
      "field": "updateAddressButton",
      "title": "Update Address Button"
    },
    {
      "type": "string",
      "field": "notSaveButton",
      "title": "Not Save Button"
    }
  ],
  "other": [
    pageTitleSchema,
  ]
}
