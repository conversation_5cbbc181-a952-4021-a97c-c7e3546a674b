import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';
import { pageTitleSchema } from "../../../shared/pageTitle";

export const subscriptionCancelCommentDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      "type": "string",
      "field": "backButton",
      "title": "Back button",
    },
    {
      "type": "string",
      "field": "title",
      "title": "Title",
    },
    {
      "type": "string",
      "field": "optional",
      "title": "Optional label",
    },
    {
      "type": "string",
      "field": "placeholder",
      "title": "Comment Placeholder",
    },
    {
      "type": "group",
      "field": "errors",
      "title": "Errors",
      "items": [
        {
          "type": "string",
          "field": "required",
          "title": "Required",
        },
      ]
    },
    {
      "type": "string",
      "field": "proceed",
      "title": "Proceed cancellation",
    },
    {
      "type": "string",
      "field": "keep",
      "title": "Keep Subscription",
    },
  ],
  "other": [
    pageTitleSchema,
  ]
}
