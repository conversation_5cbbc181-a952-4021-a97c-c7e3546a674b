import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';
import { pageTitleSchema } from "../../../shared/pageTitle";

export const skipSubscriptionSuccessDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      "type": "string",
      "field": "title",
      "title": "Title Text"
    },
    {
      "type": "string",
      "field": "thankYouText",
      "title": "Thank You Text"
    },
    {
      "type": "string",
      "field": "descriptionBefore",
      "title": "Description Before date"
    },
    {
      "type": "string",
      "field": "descriptionAfter",
      "title": "Description After date"
    },
    {
      "type": "string",
      "field": "link",
      "title": "Link to treatment"
    },
    {
      "type": "string",
      "field": "bottomTextBefore",
      "title": "Bottom Text Before"
    },
    {
      "type": "string",
      "field": "bottomTextLink",
      "title": "Bottom Text Link"
    },
    {
      "type": "string",
      "field": "bottomTextAfter",
      "title": "Bottom Text After"
    }
  ],
  "other": [
    pageTitleSchema,
  ]
}
