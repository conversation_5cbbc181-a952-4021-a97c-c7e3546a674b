import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';
import { pageTitleSchema } from "../../../shared/pageTitle";

export const subscriptionHistoryDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      "type": "string",
      "field": "title",
      "title": "Title"
    },
    {
      "type": "string",
      "field": "showOnlyActive",
      "title": "Show Only Active"
    },
    {
      "type": "string",
      "field": "otc",
      "title": "OTC"
    },
    {
      "type": "string",
      "field": "rx",
      "title": "RX"
    },
    {
      "type": "group",
      "field": "regularity",
      "title": "Regularity",
      "items": [
        {
          "type": "string",
          "field": "1_month",
          "title": "One month"
        },
        {
          "type": "string",
          "field": "3_month",
          "title": "Three months"
        },
        {
          "type": "string",
          "field": "6_month",
          "title": "Six months"
        },
      ]
    },
    {
      "type": "string",
      "field": "nextDelivery",
      "title": "Next Delivery"
    },
    {
      "type": "string",
      "field": "moreInfo",
      "title": "More Info"
    },
    {
      "type": "feature",
      "field": "subscriptionStatus",
      "title": "Subscription Status"
    },
    {
      "type": "string",
      "field": "noDataAvailableInTable",
      "title": "No Data Available In Table"
    },
  ],
  "other": [
    pageTitleSchema,
  ]
}
