import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';
import { pageTitleSchema } from "../../../shared/pageTitle";

export const subscriptionDetailsDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      "type": "string",
      "field": "lessInfo",
      "title": "Less Info"
    },
    {
      "type": "group",
      "field": "regularity",
      "title": "Regularity",
      "items": [
        {
          "type": "string",
          "field": "1_month",
          "title": "One month"
        },
        {
          "type": "string",
          "field": "3_month",
          "title": "Three months"
        },
        {
          "type": "string",
          "field": "6_month",
          "title": "Six months"
        },
      ]
    },
    {
      "type": "string",
      "field": "nextDelivery",
      "title": "Next Delivery"
    },
    {
      "type": "string",
      "field": "deliveriesQuantity",
      "title": "Deliveries in this subscription"
    },
    {
      "type": "string",
      "field": "prescriptionStatus",
      "title": "Prescription Status"
    },
    {
      "type": "string",
      "field": "deliveryInfo",
      "title": "Delivery Info"
    },
    {
      "type": "string",
      "field": "paymentMethod",
      "title": "Payment Method"
    },
    {
      "type": "string",
      "field": "updatePaymentBtn",
      "title": "Update Payment Button Text"
    },
    {
      "type": "string",
      "field": "updatePaymentWarningText",
      "title": "Update Payment Warning Text"
    },
    {
      "type": "string",
      "field": "manageSubscription",
      "title": "Manage Subscription"
    },
    {
      "type": "string",
      "field": "changeFrequency",
      "title": "Change Frequency"
    },
    {
      "type": "string",
      "field": "cancelSubscription",
      "title": "Cancel Subscription"
    },
    {
      "type": "string",
      "field": "skipSubscription",
      "title": "Skip Subscription"
    },
    {
      "type": "feature",
      "field": "subscriptionStatus",
      "title": "Subscription Status"
    },
  ],
  "other": [
    pageTitleSchema,
  ]
}
