import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';
import { pageTitleSchema } from "../../../shared/pageTitle";

export const changeProductSuccessDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      "type": "string",
      "field": "title",
      "title": "Title Text"
    },
    {
      "type": "string",
      "field": "thankYouText",
      "title": "Thank You Text"
    },
    {
      "type": "string",
      "field": "description",
      "title": "Description"
    },
    {
      "type": "string",
      "field": "linkToTreatment",
      "title": "Link to treatment"
    },
  ],
  "other": [
    pageTitleSchema,
  ]
}
