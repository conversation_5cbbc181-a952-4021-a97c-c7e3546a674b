import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';

export const breadcrumbsDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      "type": "string",
      "field": "home",
      "title": "Home"
    },
    {
      "type": "string",
      "field": "title",
      "title": "Title"
    },
    {
      "type": "string",
      "field": "orderHistoryTitle",
      "title": "Order History Title"
    },
    {
      "type": "string",
      "field": "subscriptionHistoryTitle",
      "title": "Subscription History Title"
    },
    {
      "type": "string",
      "field": "accountDetailsTitle",
      "title": "Account Details Title"
    },
    {
      "type": "string",
      "field": "subscriptionManageTitle",
      "title": "Subscription Manage Title"
    },
  ]
}
