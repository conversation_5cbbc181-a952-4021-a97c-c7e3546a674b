import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';
import { pageTitleSchema } from "../../../shared/pageTitle";

export const subscriptionCancelReasonsDefaultSchema: TemplatesViewConfigInterface = {
  'main': [
    {
      'type': 'string',
      'field': 'backButton',
      'title': 'Back button'
    },
    {
      'type': 'string',
      'field': 'title',
      'title': 'Title'
    },
    {
      'type': 'string',
      'field': 'description',
      'title': 'Description'
    },
    {
      'type': 'string',
      'field': 'customReasonTitle',
      'title': 'Custom reason title'
    },
    {
      'type': 'string',
      'field': 'customReasonLabel',
      'title': 'Custom reason label'
    },
    {
      'type': 'string',
      'field': 'customReasonSubmit',
      'title': 'Custom reason submit'
    },
    {
      'type': 'repeater',
      'field': 'reasons',
      'title': 'Reasons',
      'items': [
        {
          'type': 'string',
          'field': 'id',
          'title': 'ID'
        },
        {
          'type': 'string',
          'field': 'title',
          'title': 'Title'
        },
        {
          'type': 'string',
          'field': 'position',
          'title': 'Position'
        },
        {
          'type': 'boolean',
          'field': 'is_comment',
          'title': 'Is a comment possible?'
        },
        {
          'type': 'boolean',
          'field': 'is_comment_required',
          'title': 'Is a comment required?'
        }
      ]
    }
  ],
  "other": [
    pageTitleSchema,
  ]
};
