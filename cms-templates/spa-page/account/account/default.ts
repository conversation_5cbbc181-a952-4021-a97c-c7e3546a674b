import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';
import { pageTitleSchema } from "../../../shared/pageTitle";

export const accountDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      "type": "string",
      "field": "title",
      "title": "Title"
    },
    {
      "type": "string",
      "field": "backButton",
      "title": "Back Button"
    },
    {
      "type": "string",
      "field": "accountDetailsTitle",
      "title": "Account Details Title"
    },
    {
      "type": "string",
      "field": "orderHistoryTitle",
      "title": "Order History Title"
    },
    {
      "type": "string",
      "field": "subscriptionHistoryTitle",
      "title": "Subscription History Title"
    },
  ],
  "other": [
    pageTitleSchema,
  ]
}
