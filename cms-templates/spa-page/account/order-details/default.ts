import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';
import { pageTitleSchema } from "../../../shared/pageTitle";

export const orderDetailsDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      "type": "string",
      "field": "shippingAddress",
      "title": "Shipping Address"
    },
    {
      "type": "string",
      "field": "addressLine1",
      "title": "Address Line 1"
    },
    {
      "type": "string",
      "field": "addressLine2",
      "title": "Address Line 2"
    },
    {
      "type": "string",
      "field": "editShippigAddress",
      "title": "Edit Shippig Address"
    },
    {
      "type": "string",
      "field": "billingAddress",
      "title": "Billing Address"
    },
    {
      "type": "string",
      "field": "paymentMethod",
      "title": "Payment Method"
    },
    {
      "type": "string",
      "field": "shippingCarrier",
      "title": "Shipping Carrier"
    },
    {
      "type": "string",
      "field": "trackingLink",
      "title": "Tracking Link"
    },
    {
      "type": "string",
      "field": "item",
      "title": "Item"
    },
    {
      "type": "string",
      "field": "price",
      "title": "Price"
    },
    {
      "type": "string",
      "field": "quantity",
      "title": "Quantity"
    },
    {
      "type": "string",
      "field": "total",
      "title": "Total"
    },
    {
      "type": "string",
      "field": "order",
      "title": "Order"
    },
    {
      "type": "string",
      "field": "backToOrderHistory",
      "title": "Back To Order History"
    },
    {
      "type": "string",
      "field": "noDataAvailableInTable",
      "title": "No Data Available In Table"
    },
    {
      "type": "string",
      "field": "prescriptionIncluded",
      "title": "Prescription Included"
    },
    {
      "type": "string",
      "field": "waitingForPrescriptionInfo",
      "title": "Waiting For Prescription Info"
    },
    {
      "type": "feature",
      "field": "orderStatus",
      "title": "Order Status"
    },
    {
      "type": "group",
      "field": "delivery",
      "title": "Delivery",
      "items": [
        {
          "type": "string",
          "field": "standard",
          "title": "By standard"
        },
        {
          "type": "string",
          "field": "express",
          "title": "By express"
        },
      ]
    },
  ],
  "other": [
    pageTitleSchema,
  ]
}
