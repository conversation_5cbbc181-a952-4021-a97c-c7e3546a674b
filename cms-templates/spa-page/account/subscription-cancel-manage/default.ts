import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';

export const accountSubscriptionCancelManageDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      "type": "string",
      "field": "backButton",
      "title": "Back button",
    },
    {
      "type": "string",
      "field": "title",
      "title": "Title",
    },
    {
      "type": "string",
      "field": "description",
      "title": "Description",
    },
    {
      "type": "string",
      "field": "change",
      "title": "Change delivery frequency",
    },
    {
      "type": "string",
      "field": "skip",
      "title": "Skip next order",
    },
    {
      "type": "string",
      "field": "cancel",
      "title": "Cancel anyway",
    },
  ]
}
