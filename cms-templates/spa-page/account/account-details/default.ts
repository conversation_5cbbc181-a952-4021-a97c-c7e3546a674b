import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';
import { pageTitleSchema } from "../../../shared/pageTitle";

export const accountDetailsDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      "type": "feature",
      "field": "header",
      "title": "Header"
    },
    {
      "type": "string",
      "field": "accountDetailsTitle",
      "title": "Account Details Title"
    },
    {
      "type": "string",
      "field": "deliveryCountry",
      "title": "Delivery Country"
    },
    {
      "type": "string",
      "field": "deliveryCountryDescription",
      "title": "Delivery Country Description"
    },
    {
      "type": "string",
      "field": "patientFirstNameLabel",
      "title": "Patient First Name Label"
    },
    {
      "type": "string",
      "field": "patientLastNameLabel",
      "title": "Patient Last Name Label"
    },
    {
      "type": "string",
      "field": "emailAddressLabel",
      "title": "Email Address Label"
    },
    {
      "type": "string",
      "field": "submit",
      "title": "Submit"
    },
    {
      "type": "string",
      "field": "cancel",
      "title": "Cancel"
    },
    {
      "type": "string",
      "field": "accountHolderName",
      "title": "Account Holder Name"
    },
    {
      "type": "string",
      "field": "accountEmailAddress",
      "title": "Account Email Address"
    },
    {
      "type": "string",
      "field": "editDetails",
      "title": "Edit Details"
    },
    {
      "type": "string",
      "field": "changePassword",
      "title": "Change Password"
    },
    {
      "type": "string",
      "field": "currentPassword",
      "title": "Current Password"
    },
    {
      "type": "string",
      "field": "newPassword",
      "title": "New Password"
    },
    {
      "type": "string",
      "field": "repeatPassword",
      "title": "Repeat Password"
    },
    {
      "type": "feature",
      "field": "footer",
      "title": "Footer"
    }
  ],
  "other": [
    pageTitleSchema,
    {
      "type": "feature",
      "field": "social",
      "title": "Social"
    },
    {
      "type": "feature",
      "field": "contacts",
      "title": "Contacts"
    },
    {
      "type": "feature",
      "field": "webcomponents",
      "title": "Webcomponents"
    },
  ]
}
