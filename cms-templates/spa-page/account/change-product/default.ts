import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';
import { pageTitleSchema } from "../../../shared/pageTitle";

export const changeProductDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      "type": "string",
      "field": "backButton",
      "title": "Back Button"
    },
    {
      "type": "string",
      "field": "title",
      "title": "Title Text"
    },
    {
      "type": "string",
      "field": "description",
      "title": "Description Text"
    },
    {
      "type": "string",
      "field": "sameSize",
      "title": "Same Size"
    },
    {
      "type": "string",
      "field": "savedAmount",
      "title": "Saved Amount"
    },
    {
      "type": "string",
      "field": "retainDiscount",
      "title": "Retain Discount"
    },
    {
      "type": "string",
      "field": "current",
      "title": "Current"
    },
    {
      "type": "string",
      "field": "generic",
      "title": "Generic"
    },
    {
      "type": "string",
      "field": "switchMedication",
      "title": "Switch Medication"
    },
    {
      "type": "string",
      "field": "cancelAnyway",
      "title": "Cancel Anyway"
    },
  ],
  "other": [
    pageTitleSchema,
  ]
}
