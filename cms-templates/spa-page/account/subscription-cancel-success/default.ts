import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';
import { pageTitleSchema } from "../../../shared/pageTitle";

export const subscriptionCancelSuccessDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      "type": "image",
      "field": "image",
      "title": "Image"
    },
    {
      "type": "string",
      "field": "title",
      "title": "Title",
    },
    {
      "type": "string",
      "field": "description",
      "title": "Description",
    },
    {
      "type": "string",
      "field": "explore",
      "title": "Explore other products",
    }
  ],
  "other": [
    pageTitleSchema,
  ]
}
