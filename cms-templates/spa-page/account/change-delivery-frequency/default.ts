import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';
import { pageTitleSchema } from "../../../shared/pageTitle";

export const changeDeliveryFrequencyDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      "type": "string",
      "field": "backButton",
      "title": "Back Button"
    },
    {
      "type": "string",
      "field": "title",
      "title": "Title Text"
    },
    {
      "type": "string",
      "field": "description",
      "title": "Description Text"
    },
    {
      "type": "group",
      "field": "regularity",
      "title": "Regularity",
      "items": [
        {
          "type": "string",
          "field": "1_month",
          "title": "One month"
        },
        {
          "type": "string",
          "field": "3_month",
          "title": "Three months"
        }
      ]
    },
    {
      "type": "string",
      "field": "current",
      "title": "Current"
    },
    {
      "type": "string",
      "field": "new",
      "title": "New"
    },
    {
      "type": "string",
      "field": "estimatedDeliveryDate",
      "title": "Estimated delivery date"
    },
    {
      "type": "string",
      "field": "note",
      "title": "Note"
    },
    {
      "type": "string",
      "field": "changeButtonText",
      "title": "Change Button Text"
    },
    {
      "type": "string",
      "field": "cancelButtonText",
      "title": "Cancel Button Text"
    },
    {
      "type": "group",
      "field": "changePackage",
      "title": "Change Package",
      "items": [
        {
          "type": "string",
          "field": "preferDifferentPackageQuestion",
          "title": "Prefer different package Question"
        },
        {
          "type": "string",
          "field": "changePackageLink",
          "title": "Link text"
        },
        {
          "type": "string",
          "field": "changePackageDescription",
          "title": "Link description"
        },
      ]
    },
  ],
  "other": [
    pageTitleSchema,
  ]
}
