import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';
import { pageTitleSchema } from "../../../shared/pageTitle";

export const skipSubscriptionDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      "type": "string",
      "field": "backButton",
      "title": "Back Button"
    },
    {
      "type": "string",
      "field": "title",
      "title": "Title Text"
    },
    {
      "type": "string",
      "field": "description",
      "title": "Description Text"
    },
    {
      "type": "string",
      "field": "dateLabel",
      "title": "Date Label"
    },
    {
      "type": "string",
      "field": "confirmButton",
      "title": "Confirm Button"
    }
  ],
  "other": [
    pageTitleSchema,
  ]
}
