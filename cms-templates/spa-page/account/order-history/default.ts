import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';
import { pageTitleSchema } from "../../../shared/pageTitle";

export const orderHistoryDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      "type": "string",
      "field": "orderHistoryTitle",
      "title": "Order History Title"
    },
    {
      "type": "string",
      "field": "order",
      "title": "Order"
    },
    {
      "type": "string",
      "field": "productTitle",
      "title": "Product Title"
    },
    {
      "type": "string",
      "field": "priceTitle",
      "title": "Price Title"
    },
    {
      "type": "string",
      "field": "moreDetails",
      "title": "More Details"
    },
    {
      "type": "string",
      "field": "noDataAvailableInTable",
      "title": "No Data Available In Table"
    },
    {
      "type": "feature",
      "field": "orderStatus",
      "title": "Order Status"
    },
  ],
  "other": [
    pageTitleSchema,
  ]
}
