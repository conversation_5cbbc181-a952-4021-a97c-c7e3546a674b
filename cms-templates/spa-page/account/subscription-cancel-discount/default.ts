import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';
import { pageTitleSchema } from "../../../shared/pageTitle";

export const subscriptionCancelDiscountDefaultSchema: TemplatesViewConfigInterface = {
  'main': [
    {
      'type': 'image',
      'field': 'gift',
      'title': 'Gift'
    },
    {
      'type': 'string',
      'field': 'title',
      'title': 'Title'
    },
    {
      'type': 'string',
      'field': 'subtitle',
      'title': 'Subtitle'
    },
    {
      'type': 'string',
      'field': 'staySubscribed',
      'title': 'Stay subscribed for'
    },
    {
      'type': 'repeater',
      'field': 'benefits',
      'title': 'Benefits',
      'items': [
        {
          'type': 'image',
          'field': 'icon',
          'title': 'Icon'
        },
        {
          'type': 'string',
          'field': 'text',
          'title': 'Text'
        },
      ],
    },
    {
      'type': 'string',
      'field': 'getMyDiscount',
      'title': 'Get my discount',
    },
    {
      'type': 'string',
      'field': 'noThanksIWantToCancel',
      'title': 'No thanks, I want to cancel',
    },
    {
      'type': 'image',
      'field': 'discountAppliedCheck',
      'title': 'Discount applied check',
    },
    {
      'type': 'string',
      'field': 'discountAppliedTitle',
      'title': 'Discount applied title',
    },
    {
      'type': 'string',
      'field': 'discountAppliedSubtitle',
      'title': 'Discount applied subtitle',
    },
    {
      'type': 'string',
      'field': 'discountAppliedLink',
      'title': 'Discount applied link',
    },
  ],
  "other": [
    pageTitleSchema,
  ]
};
