import { ConfigInterface } from './config.interface';
import { faq } from './faq/faq';
import { EntityEnum } from '../shared/models/entity.enum';
import { expert } from './expert/expert';
import { feature } from './feature/feature';
import { page } from './page/page';
import { spaPage } from './spa-page/spa-page';
import { blogArticle } from './blog/blog-article';
import { spaFeature } from './spa-page/features/features';

export const config: {[key in EntityEnum]?: ConfigInterface[]} = {
  faq: faq,
  expert: expert,
  'blog-article': blogArticle,
  feature: feature,
  page: page,
  'spa-page': spaPage,
  'spa-feature': spaFeature
};
