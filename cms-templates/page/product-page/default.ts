import { TemplatesViewConfigInterface } from '../../../apps/website/src/app/api/v1/templates/templates.interface';
import { ChangefreqEnum, PriorityEnum, seoSitemapSchema } from '../../shared/seo-sitemap-schema';

export const productPageDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      "type": "feature",
      "field": "header",
      "title": "Header"
    },
    {
      "type": "string",
      "field": "slug",
      "title": "slug",
      "validations": {
        "isRequired": true
      },
      "disabled": false
    },
    {
      'type': 'breadcrumbs',
      'field': 'breadcrumbs',
      'title': 'Parent page. (for breadcrumbs)',
      'validations': {
        'isRequired': true
      },
      "defaultValue": {
        "id": 71, // Child category page
        "type": "relation",
        "entity": "breadcrumbs"
      }
    },
    {
      "type": "group",
      "field": "content",
      "title": "Content",
      "items": [
        {
          "type": "group",
          "field": "modificationsList",
          "title": "Modifications list",
          "items": [
            {
              "type": "string",
              "field": "from",
              "title": "From"
            },
            {
              "type": "string",
              "field": "perPill",
              "title": "Per pill"
            },
            {
              "type": "string",
              "field": "savedPrice",
              "title": "Saved price"
            },
            {
              "type": "string",
              "field": "outOfStock",
              "title": "Out of stock"
            },
          ]
        },
        {
          "type": "group",
          "field": "deliveryTime",
          "title": "Delivery time",
          "items": [
            {
              "type": "string",
              "field": "orderNowAndGetItByDate",
              "title": "Order now and get it by [date]"
            },
          ]
        },
        {
          "type": "group",
          "field": "productDetails",
          "title": "Product details",
          "items": [
            {
              "type": "string",
              "field": "about",
              "title": "About"
            },
            {
              "type": "repeater",
              "field": "tabs",
              "title": "Tabs",
              "items": [
                {
                  "type": "string",
                  "field": "slug",
                  "title": "Slug"
                },
                {
                  "type": "string",
                  "field": "title",
                  "title": "Title"
                },
                {
                  "type": "code",
                  "field": "text",
                  "title": "Text"
                },
              ],
            }
          ]
        },
        {
          "type": "group",
          "field": "faq",
          "title": "FAQ",
          "items": [
            {
              "type": "string",
              "field": "questionsAndAnswersAbout",
              "title": "Questions and answers about"
            },
            {
              "type": "string",
              "field": "specialistsAnswer",
              "title": "Specialists answer"
            },
          ]
        },
        {
          "type": "group",
          "field": "productInfo",
          "title": "Product info",
          "items": [
            {
              "type": "string",
              "field": "leftArrowButton",
              "title": "Left arrow button"
            },
            {
              "type": "string",
              "field": "rightArrowButton",
              "title": "Right arrow button"
            },
            {
              "type": "string",
              "field": "cheaperProductAvailablePill",
              "title": "Cheaper {{name}} product available - starting at {{price}} per pill"
            },
            {
              "type": "string",
              "field": "cheaperProductAvailable",
              "title": "Cheaper {{name}} product available - starting at {{price}}"
            },
            {
              "type": "string",
              "field": "imageIllustrationPurpose",
              "title": "Images for illustration purposes"
            },
            {
              "type": "string",
              "field": "getBackToProducts",
              "title": "Go back to start consultation"
            },
          ]
        },
        {
          "type": "group",
          "field": "infoCheckoutButtons",
          "title": "Info checkout buttons",
          "items": [
            {
              "type": "string",
              "field": "priceFrom",
              "title": "Price from"
            },
            {
              "type": "string",
              "field": "perPill",
              "title": "Per pill"
            },
            {
              "type": "string",
              "field": "freeExpressDeliveryIncluded",
              "title": "FREE delivery included"
            },
            {
              "type": "string",
              "field": "cheaperProductAvailablePill",
              "title": "Cheaper {{name}} product available - starting at {{price}} per pill"
            },
            {
              "type": "string",
              "field": "cheaperProductAvailable",
              "title": "Cheaper {{name}} product available - starting at {{price}}"
            },
            {
              "type": "string",
              "field": "requestOnlinePrescription",
              "title": "Request online prescription"
            },
            {
              "type": "string",
              "field": "ePrescriptionFee",
              "title": "E-prescription fee"
            },
            {
              "type": "string",
              "field": "redeemYourPrescription",
              "title": "Send us your prescription"
            },
            {
              "type": "string",
              "field": "or",
              "title": "Or"
            },
            {
              "type": "string",
              "field": "startYourOrder",
              "title": "Start your order"
            },
            {
              "type": "string",
              "field": "privatePrescription",
              "title": "Private prescription"
            },
            {
              "type": "string",
              "field": "productOutOfStock",
              "title": "This product is temporarily out of stock"
            },
          ]
        },
        {
          "type": "group",
          "field": "infoTable",
          "title": "Info table",
          "items": [
            {
              "type": "string",
              "field": "infoTableTitle",
              "title": "Information"
            },
            {
              "type": "string",
              "field": "infoTableBrandName",
              "title": "Brand name"
            },
            {
              "type": "string",
              "field": "infoTableManufacturer",
              "title": "Manufacturer"
            },
            {
              "type": "string",
              "field": "infoTableActiveIngredient",
              "title": "Active ingredient"
            },
            {
              "type": "string",
              "field": "infoTableDosage",
              "title": "Dosage"
            },
            {
              "type": "string",
              "field": "infoTableDosageForm",
              "title": "Dosage form"
            },
            {
              "type": "string",
              "field": "infoTablePrescriptionRequirement",
              "title": "Prescription requirement"
            },
            {
              "type": "string",
              "field": "infoTablePrescriptionOnly",
              "title": "Prescription only"
            },
            {
              "type": "string",
              "field": "infoTableDrugClass",
              "title": "Drug class"
            },
            {
              "type": "string",
              "field": "infoTablePzn",
              "title": "PZN"
            }
          ]
        },
        {
          "type": "group",
          "field": "modificationsTable",
          "title": "Modifications table",
          "items": [
            {
              "type": "string",
              "field": "packageSizeAndDosage",
              "title": "Package size & Dosage"
            },
            {
              "type": "string",
              "field": "requestOnlinePrescription",
              "title": "Request online prescription"
            },
            {
              "type": "string",
              "field": "ePrescriptionFee",
              "title": "E-prescription fee"
            },
            {
              "type": "string",
              "field": "redeemYourPrescription",
              "title": "Send us your prescription"
            },
            {
              "type": "string",
              "field": "startYourOrder",
              "title": "Start your order"
            },
            {
              "type": "string",
              "field": "productOutOfStock",
              "title": "This product is temporarily out of stock"
            },
          ]
        },
        {
          "type": "group",
          "field": "servicesList",
          "title": "Services list",
          "items": [
            {
              "type": "string",
              "field": "titleServices",
              "title": "That´s why Apomeds"
            },
            {
              'type': 'image',
              'field': 'genuineAndBrandedMedicationImage',
              'title': 'Genuine & Branded Medication Image',
            },
            {
              "type": "string",
              "field": "genuineAndBrandedMedication",
              "title": "Genuine & Branded Medication"
            },
            {
              "type": "string",
              "field": "deliveredDyEuRegulatedPharmacy",
              "title": "Delivered by an EU-Regulated Pharmacy"
            },
            {
              "type": "image",
              "field": "prescriptionIncludedImage",
              "title": "Prescription Included Image"
            },
            {
              "type": "string",
              "field": "prescriptionIncluded",
              "title": "Prescription Included"
            },
            {
              "type": "string",
              "field": "issuedByEuLicensedDoctor",
              "title": "Issued by an EU-licensed doctor"
            },
            {
              "type": "image",
              "field": "fastDiscreetDeliveryImage",
              "title": "Fast Discreet Delivery Image"
            },
            {
              "type": "string",
              "field": "fastDiscreetDelivery",
              "title": "Fast Discreet Delivery"
            },
            {
              "type": "string",
              "field": "fastAndDiscreetShippingIncluded",
              "title": "Fast and discreet shipping included"
            },
          ]
        },
        {
          "type": "group",
          "field": "similarTreatments",
          "title": "Similar treatments",
          "items": [
            {
              "type": "string",
              "field": "similarTreatmentsTitle",
              "title": "Similar treatments title"
            },
            {
              "type": "string",
              "field": "productTreatmentsLeftButton",
              "title": "Product treatments left button"
            },
            {
              "type": "string",
              "field": "from",
              "title": "From"
            },
            {
              "type": "string",
              "field": "perPill",
              "title": "Per pill"
            },
            {
              "type": "string",
              "field": "moreInfo",
              "title": "More info"
            },
            {
              "type": "string",
              "field": "productTreatmentsRightButton",
              "title": "Product treatments right button"
            },
          ]
        },
        {
          "type": "group",
          "field": "stickyFooter",
          "title": "Sticky footer",
          "items": [
            {
              "type": "string",
              "field": "canWeHelp",
              "title": "Can We Help?"
            },
            {
              "type": "string",
              "field": "customerServiceOnline",
              "title": "Customer Service Online"
            },
            {
              "type": "string",
              "field": "outOf",
              "title": "out of"
            },
            {
              "type": "string",
              "field": "orderNowAndGetItByDate",
              "title": "Order now and get it by [date]"
            },
            {
              "type": "string",
              "field": "requestOnlinePrescription",
              "title": "Request online prescription"
            },
            {
              "type": "string",
              "field": "ePrescriptionFee",
              "title": "E-prescription fee"
            },
            {
              "type": "string",
              "field": "redeemYourPrescription",
              "title": "Send us your prescription"
            },
            {
              "type": "string",
              "field": "startYourOrder",
              "title": "Start your order"
            },
            {
              "type": "string",
              "field": "getBackToProducts",
              "title": "Go back to start consultation"
            },
          ]
        },
        {
          "type": "group",
          "field": "availableGeneric",
          "title": "Available generic",
          "items": [
            {
              "type": "string",
              "field": "cheaperProductAvailablePill",
              "title": "Cheaper {{name}} product available - starting at {{price}} per pill"
            },
            {
              "type": "string",
              "field": "cheaperProductAvailable",
              "title": "Cheaper {{name}} product available - starting at {{price}}"
            },
            {
              "type": "string",
              "field": "checkNow",
              "title": "Check now"
            },
          ]
        },
        {
          "type": "feature",
          "field": "howItWorks",
          "title": "How It Works block",
        },
        {
          "type": "feature",
          "field": "disclaimer",
          "title": "Disclaimer",
        }
      ]
    },
    {
      'type': 'feature',
      'field': 'safeAndSecure',
      'title': 'Safe and Secure',
    },
    {
      'type': 'feature',
      'field': 'pageSummary',
      'title': 'Page Summary',
    },
    {
      "type": "feature",
      "field": "treatmentSidebar",
      "title": "Treatment Sidebar"
    },
    {
      "type": "feature",
      "field": "footer",
      "title": "Footer"
    }
  ],
  "seo": [
    seoSitemapSchema(ChangefreqEnum.weekly, PriorityEnum.onePointZero)
  ],
  "other": [
    {
      "type": "feature",
      "field": "social",
      "title": "Social"
    },
    {
      "type": "feature",
      "field": "contacts",
      "title": "Contacts"
    },
    {
      "type": "feature",
      "field": "webcomponents",
      "title": "Webcomponents"
    },
  ]
}
