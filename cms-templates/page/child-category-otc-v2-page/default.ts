import { TemplatesViewConfigInterface } from '../../../apps/website/src/app/api/v1/templates/templates.interface';
import { ChangefreqEnum, PriorityEnum, seoSitemapSchema } from '../../shared/seo-sitemap-schema';

export const ChildCategoryOtcV2DefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      "type": "feature",
      "field": "header",
      "title": "Header"
    },
    {
      "type": "string",
      "field": "slug",
      "title": "slug",
      "validations": {
        "isRequired": true
      },
      "disabled": false
    },
    {
      type: 'breadcrumbs',
      field: 'breadcrumbs',
      title: 'Parent page. (for breadcrumbs)',
      validations: {
        isRequired: true
      },
      defaultValue: {
        id: 452, // Parent category page
        type: "relation",
        entity: "breadcrumbs"
      }
    },
    {
      "type": "group",
      "field": "content",
      "title": "Content",
      "items": [
        {
          "type": "group",
          "field": "additional",
          "title": "Additional",
          "items": [
            {
              "type": "string",
              "field": "text",
              "title": "Text"
            },
          ]
        },
        {
          "type": "group",
          "field": "banner",
          "title": "Banner",
          "items": [
            {
              "type": "string",
              "field": "infoTag",
              "title": "Info tag"
            },
          ]
        },
        {
          "type": "group",
          "field": "bottomList",
          "title": "Bottom list",
          "items": [
            {
              "type": "string",
              "field": "new",
              "title": "New"
            },
            {
              "type": "string",
              "field": "button",
              "title": "Button"
            },
          ]
        },
        {
          "type": "group",
          "field": "description",
          "title": "Description",
          "items": [
            {
              "type": "string",
              "field": "freeShipping",
              "title": "Free shipping"
            },
            {
              "type": "string",
              "field": "satisfaction",
              "title": "Satisfaction"
            },
            {
              "type": "string",
              "field": "recommended",
              "title": "Recommended"
            }
          ]
        },
        {
          "type": "group",
          "field": "reviewList",
          "title": "Review list",
          "items": [
            {
              "type": "string",
              "field": "title",
              "title": "Title"
            },
            {
              "type": "string",
              "field": "verifiedReview",
              "title": "Verified review"
            }
          ]
        },
        {
          "type": "group",
          "field": "topList",
          "title": "Top list",
          "items": [
            {
              "type": "string",
              "field": "new",
              "title": "New"
            },
            {
              "type": "string",
              "field": "button",
              "title": "Button"
            },
          ]
        },
      ]
    },
    {
      "type": "feature",
      "field": "treatmentSidebar",
      "title": "Treatment Sidebar"
    },
    {
      "type": "feature",
      "field": "footer",
      "title": "Footer"
    }
  ],
  "seo": [
    seoSitemapSchema(ChangefreqEnum.weekly, PriorityEnum.onePointZero)
  ],
  "other": [
    {
      "type": "feature",
      "field": "social",
      "title": "Social"
    },
    {
      "type": "feature",
      "field": "contacts",
      "title": "Contacts"
    },
    {
      "type": "feature",
      "field": "webcomponents",
      "title": "Webcomponents"
    },
  ]
}
