import { TemplatesViewConfigInterface } from '../../../apps/website/src/app/api/v1/templates/templates.interface';
import { ChangefreqEnum, PriorityEnum, seoSitemapSchema } from '../../shared/seo-sitemap-schema';

export const ChildCategoryDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      "type": "feature",
      "field": "header",
      "title": "Header"
    },
    {
      "type": "string",
      "field": "slug",
      "title": "slug",
      "validations": {
        "isRequired": true
      },
      "disabled": false
    },
    {
      type: 'breadcrumbs',
      field: 'breadcrumbs',
      title: 'Parent page. (for breadcrumbs)',
      validations: {
        isRequired: true
      },
      defaultValue: {
        id: 452, // Parent category page
        type: "relation",
        entity: "breadcrumbs"
      }
    },
    {
      "type": "group",
      "field": "content",
      "title": "Content",
      "items": [
        {
          "type": "group",
          "field": "notEuCategoryStickyFooter",
          "title": "Not EU category sticky footer",
          "items": [
            {
              "type": "string",
              "field": "startYourConsultation",
              "title": "Start your consultation"
            }
          ]
        },
        {
          "type": "group",
          "field": "protectedData",
          "title": "Protected data",
          "items": [
            {
              "type": "string",
              "field": "title",
              "title": "Title"
            },
            {
              "type": "repeater",
              "field": "services",
              "title": "Safe Services",
              "items": [
                {
                  "type": "string",
                  "field": "link",
                  "title": "Link"
                },
                {
                  'type': 'group',
                  'field': 'externalImage',
                  'title': 'External Image',
                  'items': [
                    {
                      'type': 'string',
                      'field': 'src',
                      'title': 'Src'
                    },
                    {
                      'type': 'string',
                      'field': 'alt',
                      'title': 'Alt'
                    },
                    {
                      'type': 'string',
                      'field': 'width',
                      'title': 'Width'
                    },
                    {
                      'type': 'string',
                      'field': 'height',
                      'title': 'Height'
                    },
                  ]
                },
                {
                  "type": "image",
                  "field": "internalImage",
                  "title": "Internal Image"
                }
              ]
            }
          ]
        },
        {
          "type": "group",
          "field": "categoryInfo",
          "title": "Category info",
          "items": [
            {
              "type": "string",
              "field": "requestOnlineDoctorPrescription",
              "title": "Request prescription from online doctor"
            },
            {
              "type": "string",
              "field": "requestPrescription",
              "title": "Request prescription"
            },
            {
              "type": "string",
              "field": "alreadyHavePrescription",
              "title": "Already have a prescription?"
            },
            {
              "type": "string",
              "field": "sendUsYourPrescription",
              "title": "Send us your prescription"
            },
            {
              "type": "string",
              "field": "freeShipping",
              "title": "Free shipping"
            },
            {
              "type": "string",
              "field": "euLicensedDoctor",
              "title": "EU licensed doctor"
            },
            {
              "type": "string",
              "field": "discreetPackaging",
              "title": "Discreet packaging"
            },
            {
              "type": "string",
              "field": "startYourConsultation",
              "title": "Start your consultation"
            },
            {
              "type": "string",
              "field": "priceFrom",
              "title": "Price from"
            },
            {
              "type": "string",
              "field": "perPill",
              "title": "Per pill"
            },
            {
              "type": "string",
              "field": "readMore",
              "title": "Read more"
            },
            {
              "type": "string",
              "field": "ePrescriptionFee",
              "title": "E-prescription fee"
            },
            {
              "type": "string",
              "field": "freeNextDayExpressDeliveryIncluded",
              "title": "FREE next day express delivery included"
            },
            {
              "type": "string",
              "field": "privatePrescription",
              "title": "Private prescription"
            },
            {
              "type": "string",
              "field": "withoutMedicalPrescription",
              "title": "Without medical prescription"
            },
            {
              "type": "string",
              "field": "categoryMedicationsOutOfStock",
              "title": "All medications in this category are temporarily out of stock"
            }
          ]
        },
        {
          "type": "group",
          "field": "productsBlock",
          "title": "Products block",
          "items": [
            {
              "type": "string",
              "field": "seeProducts",
              "title": "See products"
            },
            {
              "type": "string",
              "field": "clickToClose",
              "title": "Click to close"
            },
            {
              "type": "string",
              "field": "treatments",
              "title": "Treatments"
            },
            {
              "type": "string",
              "field": "bestValue",
              "title": "Best value"
            },
            {
              "type": "string",
              "field": "inStock",
              "title": "In stock"
            },
            {
              "type": "string",
              "field": "outOfStock",
              "title": "Out of stock"
            },
            {
              "type": "string",
              "field": "readMore",
              "title": "Read more"
            },
            {
              "type": "string",
              "field": "continue",
              "title": "Continue"
            },
            {
              "type": "string",
              "field": "startConsultation",
              "title": "Start consultation"
            },
            {
              "type": "string",
              "field": "pricesAndProductInformation",
              "title": "Prices and product information"
            },
            {
              "type": "string",
              "field": "popular",
              "title": "Popular"
            },
            {
              "type": "string",
              "field": "alphabetic",
              "title": "Alphabetic"
            },
            {
              "type": "string",
              "field": "micropill",
              "title": "Micropill"
            },
            {
              "type": "string",
              "field": "minipill",
              "title": "Minipill"
            },
            {
              "type": "string",
              "field": "other",
              "title": "Other"
            },
          ]
        },
        {
          "type": "group",
          "field": "productSearch",
          "title": "Product search",
          "items": [
            {
              "type": "string",
              "field": "title",
              "title": "Title"
            },
            {
              "type": "string",
              "field": "subtitle",
              "title": "Subtitle"
            },
            {
              "type": "string",
              "field": "placeholder",
              "title": "Placeholder"
            },
            {
              "type": "string",
              "field": "nothingFound",
              "title": "Nothing found"
            }
          ]
        },
        {
          "type": "feature",
          "field": "howItWorks",
          "title": "How It Works block",
        },
        {
          "type": "feature",
          "field": "disclaimer",
          "title": "Disclaimer",
        }
      ]
    },
    {
      'type': 'feature',
      'field': 'pageSummary',
      'title': 'Page Summary',
    },
    {
      "type": "feature",
      "field": "treatmentSidebar",
      "title": "Treatment Sidebar"
    },
    {
      "type": "feature",
      "field": "footer",
      "title": "Footer"
    }
  ],
  "seo": [
    seoSitemapSchema(ChangefreqEnum.weekly, PriorityEnum.onePointZero)
  ],
  "other": [
    {
      "type": "feature",
      "field": "social",
      "title": "Social"
    },
    {
      "type": "feature",
      "field": "contacts",
      "title": "Contacts"
    },
    {
      "type": "feature",
      "field": "webcomponents",
      "title": "Webcomponents"
    },
  ]
}
