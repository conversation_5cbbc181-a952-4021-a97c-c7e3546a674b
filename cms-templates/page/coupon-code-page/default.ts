import { TemplatesViewConfigInterface } from "../../../apps/website/src/app/api/v1/templates/templates.interface";
import { ChangefreqEnum, PriorityEnum, seoSitemapSchema } from '../../shared/seo-sitemap-schema';

export const couponCodeDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      "type": "feature",
      "field": "header",
      "title": "Header"
    },
    {
      "type": "string",
      "field": "slug",
      "title": "slug",
      "validations": {
        "isRequired": true
      },
      "disabled": false
    },
    {
      "type": "group",
      "field": "content",
      "title": "Content",
      "items": [
        {
          "type": "string",
          "field": "title",
          "title": "Title"
        },
        {
          "type": "string",
          "field": "subTitle",
          "title": "Subtitle"
        },
        {
          "type": "string",
          "field": "shortDesc",
          "title": "Short description"
        },
        {
          "type": "string",
          "field": "desc",
          "title": "Description"
        },
      ],
    },
    {
      "type": "group",
      "field": "giftBlock",
      "title": "Gift block",
      "items": [
        {
          "type": "image",
          "field": "img",
          "title": "Gift image"
        },
        {
          "type": "string",
          "field": "desc",
          "title": "Description"
        },
        {
          "type": "string",
          "field": "code",
          "title": "Coupon code"
        },
        {
          "type": "string",
          "field": "copyTooltip",
          "title": "Tooltip for copying text"
        },
        {
          "type": "string",
          "field": "copiedSuccessfullyTooltip",
          "title": "Successfully copied tooltip text"
        }
      ]
    },
    {
      "type": "string",
      "field": "pageNote",
      "title": "Page note"
    },
    {
      "type": "string",
      "field": "CTABtn",
      "title": "CTA button"
    },
    {
      "type": "repeater",
      "field": "benefits",
      "title": "Benefits line",
      "items": [
        {
          "type": "group",
          "field": "benefit",
          "title": "Benefit",
          "items": [
            {
              "type": "image",
              "field": "image",
              "title": "Icon"
            },
            {
              "type": "string",
              "field": "title",
              "title": "Title"
            },
          ],
        }
      ]
    },
    {
      "type": "feature",
      "field": "treatmentSidebar",
      "title": "Treatment Sidebar"
    },
    {
      "type": "feature",
      "field": "footer",
      "title": "Footer"
    }
  ],
  "seo": [
    {
      "field": "title",
      "type": "string",
      "title": "SEO Title",
      "validations": {
        "isRequired": true
      }
    },
    {
      "field": "description",
      "type": "string",
      "title": "SEO Description",
      "validations": {
        "isRequired": false
      }
    },
    seoSitemapSchema(ChangefreqEnum.monthly, PriorityEnum.zeroPointFive)
  ],
  "other": [
    {
      "type": "feature",
      "field": "social",
      "title": "Social"
    },
    {
      "type": "feature",
      "field": "contacts",
      "title": "Contacts"
    },
    {
      "type": "feature",
      "field": "webcomponents",
      "title": "Webcomponents"
    },
  ]
}
