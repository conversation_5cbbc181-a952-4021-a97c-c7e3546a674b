import { ConfigInterface } from '../config.interface';
import { productPageDefaultSchema } from './product-page/default';
import { otcProductDefaultSchema } from './otc-product-page/default';
import { ChildCategoryDefaultSchema } from './child-category-page/default';
import { ChildCategoryOtcV2DefaultSchema } from './child-category-otc-v2-page/default';
import { browserSupportDefaultSchema } from './website/browser-support/default';
import { resetPasswordDefaultSchema } from './website/reset-password/default';
import { threeGummiesDefaultSchema } from './website/three-gummies/default';
import { couponCodeDefaultSchema } from './coupon-code-page/default';
import { notFoundPageDefaultSchema } from './website/not-found-page/default';
import { aboutUsPageDefaultSchema } from './website/about-us-page/default';
import { homePageDefaultSchema } from './website/home-page/default';
import { contactUsPageDefaultSchema } from './website/contact-us-page/default';
import { expertsListPageDefaultSchema } from './website/experts-list-page/default';
import { StaticPageDefaultSchema } from './static-page/default';
import { blogPageDefaultSchema } from './website/blog/default';
import { selectRegionPageDefaultSchema } from './website/select-region-page/default';
import { ParentCategoryDefaultSchema } from './parent-category-page/default';
import { faqPageDefaultSchema } from './website/faq-page/default';

export const page: ConfigInterface[] = [
  {
    id: 'product-page-template',
    enabled: true,
    name: '[COMMON] Product page template',
    templates: [
      {
        locale: 'default',
        schema: productPageDefaultSchema
      }
    ]
  },
  {
    id: 'otc-product-page-template',
    enabled: true,
    name: '[COMMON] OTC Product page template',
    templates: [
      {
        locale: 'default',
        schema: otcProductDefaultSchema
      }
    ]
  },
  {
    id: 'child-category-page-template',
    enabled: true,
    name: '[COMMON] Child category page template',
    templates: [
      {
        locale: 'default',
        schema: ChildCategoryDefaultSchema
      }
    ]
  },
  {
    id: 'child-category-otc-v2-page-template',
    enabled: true,
    name: '[COMMON] Child category OTC V2 page template',
    templates: [
      {
        locale: 'default',
        schema: ChildCategoryOtcV2DefaultSchema
      }
    ]
  },
  {
    id: 'parent-category-page-template',
    enabled: true,
    name: '[COMMON] Parent category page template',
    templates: [
      {
        locale: 'default',
        schema: ParentCategoryDefaultSchema
      }
    ]
  },
  {
    id: 'browserSupport',
    enabled: true,
    name: '[WEBSITE] browser support',
    templates: [
      {
        locale: 'default',
        schema: browserSupportDefaultSchema,
      }
    ]
  },
  {
    id: 'resetPassword',
    enabled: true,
    name: '[WEBSITE] reset password',
    templates: [
      {
        locale: 'default',
        schema: resetPasswordDefaultSchema
      }
    ]
  },
  {
    id: 'threeGummies',
    enabled: true,
    name: '[WEBSITE] three gummies',
    templates: [
      {
        locale: 'default',
        schema: threeGummiesDefaultSchema
      }
    ]
  },
  {
    id: 'couponCode',
    enabled: true,
    name: '[WEBSITE] coupon code',
    templates: [
      {
        locale: 'default',
        schema: couponCodeDefaultSchema,
      }
    ]
  },
  {
    id: 'notFountPage',
    enabled: true,
    name: '[WEBSITE] not found page',
    templates: [
      {
        locale: 'default',
        schema: notFoundPageDefaultSchema
      }
    ]
  },
  {
    id: 'aboutUsPage',
    enabled: true,
    name: '[WEBSITE] about us page',
    templates: [
      {
        locale: 'default',
        schema: aboutUsPageDefaultSchema,
      }
    ]
  },
  {
    id: 'homePage',
    enabled: true,
    name: '[WEBSITE] Home Page',
    templates: [
      {
        locale: 'default',
        schema: homePageDefaultSchema,
      }
    ]
  },
  {
    id: 'contactUsPage',
    enabled: true,
    name: '[WEBSITE] contact us page',
    templates: [
      {
        locale: 'default',
        schema: contactUsPageDefaultSchema,
      }
    ]
  },
  {
    id: 'expertsListPage',
    enabled: true,
    name: '[WEBSITE] experts list page',
    templates: [
      {
        locale: 'default',
        schema: expertsListPageDefaultSchema,
      }
    ]
  },
  {
    id: 'static-page',
    enabled: true,
    name: '[COMMON] Static page',
    templates: [
      {
        locale: 'default',
        schema: StaticPageDefaultSchema
      }
    ]
  },
  {
    id: 'blogPage',
    enabled: true,
    name: '[WEBSITE] blog page',
    templates: [
      {
        locale: 'default',
        schema: blogPageDefaultSchema,
      }
    ]
  },
  {
    id: 'selectRegionPage',
    enabled: true,
    name: '[WEBSITE] Select region page',
    templates: [
      {
        locale: 'default',
        schema: selectRegionPageDefaultSchema,
      }
    ]
  },
  {
    id: 'faqPage',
    enabled: true,
    name: '[WEBSITE] FAQ page',
    templates: [
      {
        locale: 'default',
        schema: faqPageDefaultSchema,
      }
    ]
  },
];
