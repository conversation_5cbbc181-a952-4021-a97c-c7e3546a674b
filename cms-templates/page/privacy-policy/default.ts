import { TemplatesViewConfigInterface } from '../../../apps/website/src/app/api/v1/templates/templates.interface';
import { ChangefreqEnum, PriorityEnum, seoSitemapSchema } from '../../shared/seo-sitemap-schema';

export const PrivacyPolicyDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      "type": "feature",
      "field": "header",
      "title": "Header"
    },
    {
      "type": "string",
      "field": "slug",
      "title": "slug",
      "validations": {
        "isRequired": true
      },
      "disabled": false
    },
    {
      "type": "string",
      "field": "title",
      "title": "title",
      "validations": {
        "isRequired": true
      },
      "disabled": false
    },
    {
      "type": "group",
      "field": "content",
      "items": [
        {
          "type": "code",
          "field": "text",
          "title": "Text"
        }
      ],
      "title": "Content"
    },
    {
      "type": "feature",
      "field": "treatmentSidebar",
      "title": "Treatment Sidebar"
    },
    {
      "type": "feature",
      "field": "footer",
      "title": "Footer"
    }
  ],
  "seo": [
    {
      "field": "title",
      "type": "string",
      "title": "SEO Title",
      "validations": {
        "isRequired": true
      }
    },
    {
      "field": "description",
      "type": "string",
      "title": "SEO Description",
      "validations": {
        "isRequired": false
      }
    },
    seoSitemapSchema(ChangefreqEnum.monthly, PriorityEnum.zeroPointFive)
  ],
  "other": [
    {
      "type": "feature",
      "field": "social",
      "title": "Social"
    },
    {
      "type": "feature",
      "field": "contacts",
      "title": "Contacts"
    },
    {
      "type": "feature",
      "field": "webcomponents",
      "title": "Webcomponents"
    },
  ]
}
