import { TemplatesViewConfigInterface } from "../../../apps/website/src/app/api/v1/templates/templates.interface";
import { ChangefreqEnum, PriorityEnum, seoSitemapSchema } from '../../shared/seo-sitemap-schema';

export const otcProductDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      "type": "feature",
      "field": "header",
      "title": "Header"
    },
    {
      "type": "string",
      "field": "slug",
      "title": "slug",
      "validations": {
        "isRequired": true
      },
      "disabled": false
    },
    {
      'type': 'breadcrumbs',
      'field': 'breadcrumbs',
      'title': 'Parent page. (for breadcrumbs)',
      'validations': {
        'isRequired': true
      },
      "defaultValue": {
        "id": 71, // Child category page
        "type": "relation",
        "entity": "breadcrumbs"
      }
    },
    {
      "type": "feature",
      "field": "otcPromocodeNotification",
      "title": "OTC Promocode Notification Feature"
    },
    {
      "type": "group",
      "field": "content",
      "title": "Content",
      "items": [
        {
          "type": "group",
          "field": "actions",
          "title": "Actions",
          "items": [
            {
              "type": "string",
              "field": "temporaryOutOfStock",
              "title": "Temporary out of stock"
            },
            {
              "type": "string",
              "field": "subscribeTo",
              "title": "Subscribe to"
            },
            {
              "type": "string",
              "field": "oneTimePurchase",
              "title": "One-time purchase"
            },
            {
              "type": "string",
              "field": "securePayment",
              "title": "Secure payment"
            },
            {
              "type": "string",
              "field": "cancelAnyTime",
              "title": "Cancel any time"
            },
            {
              "type": "string",
              "field": "freeShipping",
              "title": "Free shipping"
            },
            {
              "type": "string",
              "field": "expectedDelivery",
              "title": "Expected delivery"
            },
            {
              "type": "string",
              "field": "proceedToCheckout",
              "title": "Proceed to checkout"
            },
            {
              "type": "string",
              "field": "clickingIAgree",
              "title": "By clicking next I agree to the"
            },
            {
              "type": "link",
              "field": "subscriptionTermsOfUseLink",
              "title": "Subscription terms of use link"
            },
          ]
        },
        {
          "type": "group",
          "field": "subscriptions",
          "title": "Subscriptions",
          "items": [
            {
              "type": "string",
              "field": "deliveredEvery",
              "title": "Delivered every"
            },
            {
              "type": "string",
              "field": "month",
              "title": "Month"
            },
            {
              "type": "string",
              "field": "deliveredEveryPlural",
              "title": "Delivered every (plural)"
            },
            {
              "type": "string",
              "field": "months",
              "title": "Months"
            },
            {
              "type": "string",
              "field": "mostPopular",
              "title": "Most popular"
            },
            {
              "type": "string",
              "field": "subscriptionSave",
              "title": "Subscription save"
            },
            {
              "type": "string",
              "field": "items",
              "title": "Items"
            },
            {
              "type": "string",
              "field": "item",
              "title": "Item"
            },
            {
              "type": "string",
              "field": "total",
              "title": "Total"
            },
            {
              "type": "string",
              "field": "1MonthTitle",
              "title": "1 month title"
            },
            {
              "type": "string",
              "field": "3MonthTitle",
              "title": "3 month title"
            },
            {
              "type": "string",
              "field": "6MonthTitle",
              "title": "6 month title"
            }
          ]
        },
        {
          "type": "group",
          "field": "oneTimeBuy",
          "title": "One Time Buy",
          "items": [
            {
              "type": "string",
              "field": "subscriptionSave",
              "title": "Subscription save"
            },
            {
              "type": "string",
              "field": "item",
              "title": "Item"
            },
            {
              "type": "string",
              "field": "total",
              "title": "Total"
            },
          ]
        },
        {
          "type": "group",
          "field": "faq",
          "title": "FAQ",
          "items": [
            {
              "type": "string",
              "field": "faqTitle",
              "title": "Faq title"
            },
          ]
        },
        {
          "type": "group",
          "field": "similarProducts",
          "title": "Similar Products",
          "items": [
            {
              "type": "string",
              "field": "youMayAlsoLike",
              "title": "You may also like"
            },
            {
              "type": "string",
              "field": "from",
              "title": "From"
            },
          ]
        },
        {
          "type": "group",
          "field": "reviews",
          "title": "Reviews",
          "items": [
            {
              "type": "string",
              "field": "verifiedReview",
              "title": "Verified review"
            },
          ]
        }
      ]
    },
    {
      "type": "feature",
      "field": "treatmentSidebar",
      "title": "Treatment Sidebar"
    },
    {
      "type": "feature",
      "field": "footer",
      "title": "Footer"
    }
  ],
  "seo": [
    seoSitemapSchema(ChangefreqEnum.weekly, PriorityEnum.onePointZero)
  ],
  "other": [
    {
      "type": "feature",
      "field": "social",
      "title": "Social"
    },
    {
      "type": "feature",
      "field": "contacts",
      "title": "Contacts"
    },
    {
      "type": "feature",
      "field": "webcomponents",
      "title": "Webcomponents"
    },
  ]
}
