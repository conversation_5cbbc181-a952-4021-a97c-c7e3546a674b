import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';
import { ChangefreqEnum, PriorityEnum, seoSitemapSchema } from '../../../shared/seo-sitemap-schema';

export const selectRegionPageDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      "type": "string",
      "field": "slug",
      "title": "slug",
      "validations": {
        "isRequired": true
      },
      "disabled": false
    },
  ],
  "seo": [
    seoSitemapSchema(ChangefreqEnum.monthly, PriorityEnum.zeroPointFive)
  ],
  "other": [
    {
      "type": "feature",
      "field": "social",
      "title": "Social"
    },
    {
      "type": "feature",
      "field": "contacts",
      "title": "Contacts"
    },
    {
      "type": "feature",
      "field": "webcomponents",
      "title": "Webcomponents"
    },
  ]
}
