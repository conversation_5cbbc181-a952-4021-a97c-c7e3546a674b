import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';
import { ChangefreqEnum, PriorityEnum, seoSitemapSchema } from '../../../shared/seo-sitemap-schema';

export const contactUsPageDefaultSchema: TemplatesViewConfigInterface = {
  main: [
    {
      "type": "feature",
      "field": "header",
      "title": "Header"
    },
    {
      type: "string",
      field: "slug",
      title: "slug",
      validations: {
        isRequired: true
      },
      disabled: false
    },
    {
      field: 'pageTitle',
      title: 'Page Title',
      type: 'string',
    },
    {
      field: 'title',
      title: 'Title',
      type: 'string',
    },
    {
      type: 'breadcrumbs',
      field: 'breadcrumbs',
      title: 'Parent page. (for breadcrumbs)',
      validations: {
        isRequired: true
      },
      defaultValue: {
        id: 108, // Home page
        type: "relation",
        entity: "breadcrumbs"
      }
    },
    {
      type: "group",
      field: "contactInfo",
      title: "Contact Info",
      items: [
        {
          field: 'image',
          title: 'image',
          type: 'image',
        },
        {
          field: 'title',
          title: 'title',
          type: 'string',
        },
        {
          field: 'subtitle',
          title: 'Subtitle',
          type: 'string',
        },
        {
          type: "group",
          field: "phone",
          title: "Phone",
          items: [
            {
              field: 'label',
              title: 'Label',
              type: 'string',
            },
            {
              field: 'hint',
              title: 'Hint',
              type: 'string',
            },
          ]
        },
        {
          field: 'email',
          title: 'Email',
          type: 'group',
          items: [
            {
              field: 'label',
              title: 'Label',
              type: 'string',
            },
          ]
        },
      ],
    },
    {
      type: "group",
      field: "officeInfo",
      title: "Office Info",
      items: [
        {
          field: 'title',
          title: 'title',
          type: 'string',
        },
        {
          field: 'companyNameTitle',
          title: 'Company Name Title',
          type: 'string',
        },
        {
          field: 'companyName',
          title: 'Company Name',
          type: 'group',
          items: [
            {
              field: 'label',
              title: 'Label',
              type: 'string',
            },
            {
              field: 'value',
              title: 'Value',
              type: 'string',
            },
          ],
        },
        {
          field: 'companyNumber',
          title: 'Company Number',
          type: 'group',
          items: [
            {
              field: 'label',
              title: 'Label',
              type: 'string',
            },
            {
              field: 'value',
              title: 'Value',
              type: 'string',
            },
          ],
        },
        {
          field: 'operationalAddress',
          title: 'Operational Address',
          type: 'group',
          items: [
            {
              field: 'label',
              title: 'Label',
              type: 'string',
            },
            {
              field: 'value',
              title: 'Value',
              type: 'string',
            },
          ],
        },
        {
          field: 'legalAddress',
          title: 'Legal Address',
          type: 'string',
        },
        {
          field: 'position',
          title: 'Geo Position',
          type: 'group',
          items: [
            {
              field: 'lat',
              title: 'Latitude',
              type: 'number',
            },
            {
              field: 'lng',
              title: 'Longitude',
              type: 'number',
            }
          ]
        },
      ],
    },
    {
      field: 'salesOperator',
      title: 'Current pharmacy sales operator',
      type: 'group',
      items: [
        {
          field: 'title',
          title: 'Title',
          type: 'string',
        },
        {
          field: 'companyName',
          title: 'Company name',
          type: 'group',
          items: [
            {
              field: 'label',
              title: 'Label',
              type: 'string',
            },
            {
              field: 'value',
              title: 'Value',
              type: 'string',
            },
          ]
        },
        {
          field: 'zipcode',
          title: 'Zipcode',
          type: 'group',
          items: [
            {
              field: 'label',
              title: 'Label',
              type: 'string',
            },
            {
              field: 'value',
              title: 'Value',
              type: 'string',
            },
          ]
        },
        {
          field: 'companyAddress',
          title: 'Company address',
          type: 'group',
          items: [
            {
              field: 'label',
              title: 'Label',
              type: 'string',
            },
            {
              field: 'value',
              title: 'Value',
              type: 'string',
            },
          ]
        },
      ]
    },
    {
      "type": "feature",
      "field": "treatmentSidebar",
      "title": "Treatment Sidebar"
    },
    {
      "type": "feature",
      "field": "footer",
      "title": "Footer"
    }
  ],
  seo: [
    {
      field: "title",
      type: "string",
      title: "SEO Title",
      validations: {
        isRequired: true
      }
    },
    {
      field: "description",
      type: "string",
      title: "SEO Description",
      validations: {
        isRequired: false
      }
    },
    seoSitemapSchema(ChangefreqEnum.monthly, PriorityEnum.zeroPointEight)
  ],
  other: [
    {
      "type": "feature",
      "field": "social",
      "title": "Social"
    },
    {
      "type": "feature",
      "field": "contacts",
      "title": "Contacts"
    },
    {
      "type": "feature",
      "field": "webcomponents",
      "title": "Webcomponents"
    },
  ]
}
