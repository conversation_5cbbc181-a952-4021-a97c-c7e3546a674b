import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';

export const threeGummiesDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      "type": "feature",
      "field": "header",
      "title": "Header"
    },
    {
      "type": "string",
      "field": "slug",
      "title": "slug",
      "validations": {
        "isRequired": true
      },
      "disabled": false
    },
    {
      field: 'title',
      title: 'Title (for breadcrumbs)',
      type: 'string',
    },
    {
      type: 'breadcrumbs',
      field: 'breadcrumbs',
      title: 'Parent page. (for breadcrumbs)',
      validations: {
        isRequired: true
      },
      defaultValue: {
        id: 108, // Home page
        type: "relation",
        entity: "breadcrumbs"
      }
    },
    {
      "type": "group",
      "field": "content",
      "title": "Content",
      "items": [
        {
          "type": "string",
          "field": "trustedShopsVerifiedReview",
          "title": "Trusted Shops Verified Review"
        }
      ]
    },
    {
      "type": "feature",
      "field": "treatmentSidebar",
      "title": "Treatment Sidebar"
    },
    {
      "type": "feature",
      "field": "footer",
      "title": "Footer"
    }
  ],
  "other": [
    {
      "type": "feature",
      "field": "social",
      "title": "Social"
    },
    {
      "type": "feature",
      "field": "contacts",
      "title": "Contacts"
    },
    {
      "type": "feature",
      "field": "webcomponents",
      "title": "Webcomponents"
    },
  ]
}
