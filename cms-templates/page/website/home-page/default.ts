import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';
import { ChangefreqEnum, PriorityEnum, seoSitemapSchema } from '../../../shared/seo-sitemap-schema';

export const homePageDefaultSchema: TemplatesViewConfigInterface = {
  'main': [
    {
      "type": "feature",
      "field": "header",
      "title": "Header"
    },
    {
      'type': 'string',
      'field': 'slug',
      'title': 'slug',
    },
    {
      "type": "string",
      "field": "title",
      "title": "Title (Used for breadcrumbs)",
    },
    {
      'type': 'group',
      'field': 'banner',
      'title': 'Banner',
      'items': [
        {
          'type': 'string',
          'field': 'title',
          'title': 'Title',
        },
        {
          'type': 'string',
          'field': 'subtitle',
          'title': 'Subtitle',
        },
        {
          'type': 'string',
          'field': 'cta',
          'title': 'CTA',
        },
      ],
    },
    {
      'type': 'group',
      'field': 'categories',
      'title': 'Categories',
      'items': [
        {
          'type': 'string',
          'field': 'title',
          'title': 'Title',
        },
        {
          'type': 'repeater',
          'field': 'items',
          'title': 'Items',
          'items': [
            {
              'type': 'string',
              'field': 'id',
              'title': 'Category ID',
            },
            {
              'type': 'image',
              'field': 'image',
              'title': 'Image (SVG only)',
            },
            {
              'type': 'link',
              'field': 'link',
              'title': 'Link',
            },
          ],
        },
      ],
    },
    {
      'type': 'group',
      'field': 'greetings',
      'title': 'Greetings',
      'items': [
        {
          'type': 'string',
          'field': 'title',
          'title': 'Title',
        },
        {
          'type': 'code',
          'field': 'text',
          'title': 'Text',
        },
        {
          'type': 'repeater',
          'field': 'items',
          'title': 'Items',
          'items': [
            {
              'type': 'image',
              'field': 'image',
              'title': 'Image',
            },
            {
              'type': 'string',
              'field': 'title',
              'title': 'Title',
            },
            {
              'type': 'string',
              'field': 'subtitle',
              'title': 'Subtitle',
            },
          ],
        },
      ],
    },
    {
      'type': 'feature',
      'field': 'howItWorks',
      'title': 'How it works',
    },
    {
      'type': 'feature',
      'field': 'safeAndSecure',
      'title': 'Safe and Secure',
    },
    {
      'type': 'group',
      'field': 'doctorsBlock',
      'title': 'Doctors Block',
      'items': [
        {
          'type': 'string',
          'field': 'title',
          'title': 'Title',
        },
        {
          'type': 'repeater',
          'field': 'items',
          'title': 'Items',
          'items': [
            {
              'type': 'image',
              'field': 'image',
              'title': 'Image',
            },
            {
              'type': 'string',
              'field': 'name',
              'title': 'Name',
            },
            {
              'type': 'text',
              'field': 'position',
              'title': 'Position',
            },
            {
              'type': 'text',
              'field': 'location',
              'title': 'Location',
            },
          ],
        },
        {
          'type': 'link',
          'field': 'cta',
          'title': 'CTA',
        },
      ],
    },
    {
      'type': 'group',
      'field': 'orderOnline',
      'title': 'Order Online',
      'items': [
        {
          'type': 'string',
          'field': 'title',
          'title': 'Title',
        },
        {
          'type': 'code',
          'field': 'text',
          'title': 'Text',
        },
      ],
    },
    {
      'type': 'group',
      'field': 'aboutUs',
      'title': 'About us',
      'items': [
        {
          'type': 'string',
          'field': 'title',
          'title': 'Title',
        },
        {
          'type': 'code',
          'field': 'text',
          'title': 'Text',
        },
      ],
    },
    {
      'type': 'string',
      'field': 'verifiedReview',
      'title': 'Verified review',
    },
    {
      "type": "feature",
      "field": "treatmentSidebar",
      "title": "Treatment Sidebar"
    },
    {
      "type": "feature",
      "field": "footer",
      "title": "Footer"
    },
  ],
  "other": [
    {
      "type": "feature",
      "field": "social",
      "title": "Social"
    },
    {
      "type": "feature",
      "field": "contacts",
      "title": "Contacts"
    },
    {
      "type": "feature",
      "field": "webcomponents",
      "title": "Webcomponents"
    },
  ],
  'seo': [
    {
      'type': 'string',
      'field': 'title',
      'title': 'Title',
    },
    {
      'type': 'string',
      'field': 'description',
      'title': 'Description',
    },
    seoSitemapSchema(ChangefreqEnum.daily, PriorityEnum.onePointZero)
  ],
}
