import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';
import { ChangefreqEnum, PriorityEnum, seoSitemapSchema } from '../../../shared/seo-sitemap-schema';

export const blogPageDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      "type": "feature",
      "field": "header",
      "title": "Header",
      'validations': {
        'isRequired': true
      },
      "defaultValue": {
        "id": 241,
        "type": "relation",
        "entity": "feature"
      }
    },
    {
      "type": "string",
      "field": "slug",
      "title": "slug",
      "validations": {
        "isRequired": true
      },
      "disabled": false
    },
    {
      'type': 'breadcrumbs',
      'field': 'breadcrumbs',
      'title': 'Parent page. (for breadcrumbs)',
      'validations': {
        'isRequired': true
      },
      "defaultValue": {
        "id": 108,
        "type": "relation",
        "entity": "breadcrumbs"
      }
    },
    {
      "type": "string",
      "field": "title",
      "title": "Title (Used for menu link)",
    },
    {
      "type": "string",
      "field": "pageHeaderTitle",
      "title": "Page Header Title",
    },
    {
      "type": "string",
      "field": "pageNumberText",
      "title": "Page Number Text",
    },
    {
      "type": "string",
      "field": "continueReadingText",
      "title": "Continue Reading Text",
    },
    {
      "type": "group",
      "field": "pagination",
      "title": "Pagination",
      "items": [
        {
          "type": "string",
          "field": "previous",
          "title": "Previous",
        },
        {
          "type": "string",
          "field": "next",
          "title": "Next",
        }
      ]
    },
    {
      "type": "feature",
      "field": "footer",
      "title": "Footer"
    }
  ],
  "seo": [
    {
      "field": "title",
      "type": "string",
      "title": "SEO Title",
      "validations": {
        "isRequired": true
      }
    },
    {
      "field": "description",
      "type": "string",
      "title": "SEO Description",
    },
    seoSitemapSchema(ChangefreqEnum.daily, PriorityEnum.onePointZero)
  ],
  "other": [
    {
      "type": "feature",
      "field": "social",
      "title": "Social",
      'validations': {
        'isRequired': true
      },
      "defaultValue": {
        "id": 239,
        "type": "relation",
        "entity": "feature"
      }
    },
    {
      "type": "feature",
      "field": "contacts",
      "title": "Contacts",
      'validations': {
        'isRequired': true
      },
      "defaultValue": {
        "id": 238,
        "type": "relation",
        "entity": "feature"
      }
    },
    {
      "type": "feature",
      "field": "webcomponents",
      "title": "Webcomponents",
      'validations': {
        'isRequired': true
      },
      "defaultValue": {
        "id": 258,
        "type": "relation",
        "entity": "feature"
      }
    },
  ]
}
