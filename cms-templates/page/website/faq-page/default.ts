import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';
import { ChangefreqEnum, PriorityEnum, seoSitemapSchema } from '../../../shared/seo-sitemap-schema';

export const faqPageDefaultSchema: TemplatesViewConfigInterface = {
  main: [
    {
      type: 'feature',
      field: 'header',
      title: 'Header'
    },
    {
      type: 'string',
      field: 'slug',
      title: 'slug',
      validations: {
        isRequired: true
      },
      disabled: false
    },
    {
      field: 'title',
      title: 'Title',
      type: 'string'
    },
    {
      type: 'breadcrumbs',
      field: 'breadcrumbs',
      title: 'Parent page. (for breadcrumbs)',
      validations: {
        isRequired: true
      },
      defaultValue: {
        id: 108, // Home page
        type: 'relation',
        entity: 'breadcrumbs'
      }
    },
    {
      type: 'group',
      field: 'faqSection',
      title: 'FAQ section',
      items: [
        {
          type: 'repeater',
          field: 'faqCategories',
          title: 'FAQ categories',
          items: [
            {
              type: 'string',
              field: 'button',
              title: 'Button'
            },
            {
              type: 'string',
              field: 'title',
              title: 'Title'
            },
            {
              type: 'repeater',
              field: 'faqItems',
              title: 'FAQ items',
              items: [
                {
                  type: 'string',
                  field: 'question',
                  title: 'Question'
                },
                {
                  type: 'code',
                  field: 'answer',
                  title: 'Answer'
                }
              ]
            }
          ]
        }
      ]
    },
    {
      type: 'feature',
      field: 'treatmentSidebar',
      title: 'Treatment Sidebar'
    },
    {
      type: 'feature',
      field: 'footer',
      title: 'Footer'
    }
  ],
  seo: [
    {
      field: 'title',
      type: 'string',
      title: 'SEO Title',
      validations: {
        isRequired: true
      }
    },
    {
      field: 'description',
      type: 'string',
      title: 'SEO Description',
      validations: {
        isRequired: false
      }
    },
    seoSitemapSchema(ChangefreqEnum.monthly, PriorityEnum.zeroPointEight)
  ],
  other: [
    {
      type: 'feature',
      field: 'social',
      title: 'Social'
    },
    {
      type: 'feature',
      field: 'contacts',
      title: 'Contacts'
    },
    {
      type: 'feature',
      field: 'webcomponents',
      title: 'Webcomponents'
    }
  ]
};
