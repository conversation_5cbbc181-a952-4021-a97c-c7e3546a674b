import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';

export const resetPasswordDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      "type": "feature",
      "field": "header",
      "title": "Header"
    },
    {
      "type": "string",
      "field": "slug",
      "title": "slug",
      "validations": {
        "isRequired": true
      },
      "disabled": false
    },
    {
      "type": "group",
      "field": "content",
      "title": "Content",
      "items": [
        {
          "type": "string",
          "field": "resetPasswordTitle",
          "title": "Reset Password Title"
        },
        {
          "type": "string",
          "field": "resetPasswordDescription",
          "title": "Reset Password Description"
        },
        {
          "type": "string",
          "field": "newPasswordTitle",
          "title": "New Password Title"
        },
        {
          "type": "string",
          "field": "repeatPasswordTitle",
          "title": "Repeat Password Title"
        },
        {
          "type": "string",
          "field": "update",
          "title": "Update"
        },
        {
          "type": "string",
          "field": "successMessage",
          "title": "Success Message"
        },
        {
          "type": "string",
          "field": "errorOccurred",
          "title": "Error Occurred"
        },
        {
          "type": "string",
          "field": "backToLogin",
          "title": "Back To Login"
        },
        {
          "type": "group",
          "field": "errors",
          "title": "Errors",
          "items": [
            {
              "type": "string",
              "field": "passwordInvalid",
              "title": "Password Invalid"
            },
            {
              "type": "string",
              "field": "confirmPasswordNotMatch",
              "title": "Confirm Password Not Match"
            },
            {
              "type": "string",
              "field": "tokenInvalid",
              "title": "Token Invalid"
            },
            {
              "type": "string",
              "field": "confirmPasswordCustomerAccountDisabled",
              "title": "Confirm Password Customer Account Disabled"
            },
          ]
        }
      ]
    },
    {
      "type": "feature",
      "field": "treatmentSidebar",
      "title": "Treatment Sidebar"
    },
    {
      "type": "feature",
      "field": "footer",
      "title": "Footer"
    }
  ],
  "other": [
    {
      "type": "feature",
      "field": "social",
      "title": "Social"
    },
    {
      "type": "feature",
      "field": "contacts",
      "title": "Contacts"
    },
    {
      "type": "feature",
      "field": "webcomponents",
      "title": "Webcomponents"
    },
  ]
}
