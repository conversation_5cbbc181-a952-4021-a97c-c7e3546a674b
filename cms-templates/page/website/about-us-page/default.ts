import { TemplatesViewConfigInterface } from '../../../../apps/website/src/app/api/v1/templates/templates.interface';
import { ChangefreqEnum, PriorityEnum, seoSitemapSchema } from '../../../shared/seo-sitemap-schema';

export const aboutUsPageDefaultSchema: TemplatesViewConfigInterface = {
  "main": [
    {
      "type": "feature",
      "field": "header",
      "title": "Header"
    },
    {
      "type": "string",
      "field": "slug",
      "title": "slug",
      "validations": {
        "isRequired": true
      },
      "disabled": false
    },
    {
      "type": "group",
      "field": "content",
      "items": [
        {
          "type": "group",
          "field": "whoAreWe",
          "items": [
            {
              "type": "group",
              "field": "images",
              "items": [
                {
                  "type": "image",
                  "field": "desktop",
                  "title": "Desktop BG"
                },
                {
                  "type": "image",
                  "field": "mobile",
                  "title": "Mobile BG"
                }
              ]
            },
            {
              "type": "string",
              "field": "title",
              "title": "Title"
            },
            {
              "type": "code",
              "field": "description",
              "title": "Description"
            }
          ],
          "title": "Who Are We Section"
        },
        {
          "type": "group",
          "field": "whatAreWeDoing",
          "items": [
            {
              "type": "image",
              "field": "image",
              "title": "Image"
            },
            {
              "type": "string",
              "field": "title",
              "title": "Title"
            },
            {
              "type": "code",
              "field": "description",
              "title": "Description"
            }
          ],
          "title": "What Are We Doing Section"
        },
        {
          "type": "group",
          "field": "whyApomeds",
          "items": [
            {
              "type": "string",
              "field": "title",
              "title": "Title",
            },
            {
              "type": "repeater",
              "field": "items",
              "items": [
                {
                  "type": "image",
                  "field": "image",
                  "title": "Icon"
                },
                {
                  "type": "string",
                  "field": "title",
                  "title": "Title"
                },
                {
                  "type": "code",
                  "field": "description",
                  "title": "Description"
                }
              ],
              "title": "Item"
            },
          ],
          "title": "Why Apomeds Section"
        },
        {
          "type": "group",
          "field": "whyApomedsExtra",
          "items": [
            {
              "type": "image",
              "field": "image",
              "title": "Icon"
            },
            {
              "type": "string",
              "field": "title",
              "title": "Title"
            },
            {
              "type": "code",
              "field": "description",
              "title": "Description"
            }
          ],
          "title": "Why Apomeds Extra Section"
        },
        {
          "type": "group",
          "field": "articles",
          "items": [
            {
              "type": "repeater",
              "field": "items",
              "items": [
                {
                  "type": "image",
                  "field": "image",
                  "title": "Icon"
                },
                {
                  "type": "string",
                  "field": "title",
                  "title": "Title"
                },
                {
                  "type": "code",
                  "field": "description",
                  "title": "Description"
                }
              ],
              "title": "Item"
            }
          ],
          "title": "Articles"
        }
      ],
      "title": "Content"
    },
    {
      "type": "feature",
      "field": "treatmentSidebar",
      "title": "Treatment Sidebar"
    },
    {
      "type": "feature",
      "field": "footer",
      "title": "Footer"
    }
  ],
  "seo": [
    {
      "field": "title",
      "type": "string",
      "title": "SEO Title",
      "validations": {
        "isRequired": true
      }
    },
    {
      "field": "description",
      "type": "string",
      "title": "SEO Description",
      "validations": {
        "isRequired": false
      }
    },
    {
      "field": "homePageTitle",
      "type": "string",
      "title": "Home Page SEO Title",
      "validations": {
        "isRequired": true
      }
    },
    seoSitemapSchema(ChangefreqEnum.monthly, PriorityEnum.zeroPointEight)
  ],
  "other": [
    {
      "type": "feature",
      "field": "social",
      "title": "Social"
    },
    {
      "type": "feature",
      "field": "contacts",
      "title": "Contacts"
    },
    {
      "type": "feature",
      "field": "webcomponents",
      "title": "Webcomponents"
    },
  ]
}
