import { TemplatesViewConfigInterface } from '../../../apps/website/src/app/api/v1/templates/templates.interface';
import { ChangefreqEnum, PriorityEnum, seoSitemapSchema } from '../../shared/seo-sitemap-schema';

export const ExpertPageDefaultSchema: TemplatesViewConfigInterface = {
  'seo': [
    {
      'type': 'string',
      'field': 'seo_title',
      'title': 'Seo Title',
      'validations': {
        'isRequired': true
      }
    },
    {
      'type': 'text',
      'field': 'seo_description',
      'title': 'Seo Meta Description',
      'validations': {
        'isRequired': false
      }
    },
    {
      'type': 'group',
      'field': 'schema',
      'items': [
        {
          'type': 'string',
          'field': 'firstName',
          'title': 'First Name'
        },
        {
          'type': 'string',
          'field': 'lastName',
          'title': 'Last Name'
        },
        {
          'type': 'string',
          'field': 'email',
          'title': 'Email'
        },
        {
          'type': 'string',
          'field': 'phoneNumber',
          'title': 'Phone Number'
        },
        {
          'type': 'image',
          'field': 'image',
          'title': 'Image',
          'validations': {
            'isRequired': false
          }
        },
        {
          'type': 'group',
          'field': 'workplace',
          'title': 'Workplace',
          'items': [
            {
              'type': 'repeater',
              'field': 'workplaceItem',
              'items': [
                {
                  'type': 'string',
                  'field': 'value',
                  'title': 'Workplace'
                },
                {
                  'type': 'string',
                  'field': 'link',
                  'title': 'Workplace Link'
                },
                {
                  'type': 'group',
                  'field': 'address',
                  'title': 'Address',
                  'items': [
                    {
                      'type': 'string',
                      'field': 'region',
                      'title': 'Region'
                    },
                    {
                      'type': 'string',
                      'field': 'postalCode',
                      'title': 'Postal Code'
                    },
                    {
                      'type': 'string',
                      'field': 'street',
                      'title': 'Street'
                    },
                    {
                      'type': 'string',
                      'field': 'country',
                      'title': 'Country'
                    },
                  ]
                },
              ]
            }
          ],
        }
      ],
      'title': 'Schema',
      'validations': {
        'isRequired': false
      }
    },
    seoSitemapSchema(ChangefreqEnum.monthly, PriorityEnum.zeroPointFive)
  ],
  'main': [
    {
      "type": "feature",
      "field": "header",
      "title": "Header"
    },
    {
      'type': 'string',
      'field': 'title',
      'title': 'Title (Used for menu link)',
      'validations': {
        'isRequired': true
      }
    },
    {
      'type': 'string',
      'field': 'slug',
      'title': 'slug',
      'validations': {
        'isRequired': true
      },
      'disabled': false
    },
    {
      'type': 'breadcrumbs',
      'field': 'breadcrumbs',
      'title': 'Parent page. (for breadcrumbs)',
      'validations': {
        'isRequired': true
      },
      "defaultValue": {
        "id": 191, // Editorial Team Page
        "type": "relation",
        "entity": "breadcrumbs"
      }
    },
    {
      'type': 'boolean',
      'field': 'hasOwnPage',
      'title': 'Has own page (will provide a link from experts list page)',
      'defaultValue': true,
      'validations': {
        'isRequired': false
      },
    },
    {
      'type': 'group',
      'field': 'content',
      'items': [
        {
          'type': 'string',
          'field': 'name',
          'title': 'Name'
        },
        {
          'type': 'image',
          'field': 'image',
          'title': 'Avatar'
        },
        {
          'type': 'code',
          'field': 'qualifications',
          'title': 'Qualifications'
        },
        {
          'type': 'group',
          'field': 'workplace',
          'items': [
            {
              'type': 'string',
              'field': 'name',
              'title': 'Workplace Key'
            },
            {
              'type': 'repeater',
              'field': 'workplaceItem',
              'items': [
                {
                  'type': 'string',
                  'field': 'value',
                  'title': 'Workplace'
                },
                {
                  'type': 'string',
                  'field': 'link',
                  'title': 'Workplace Link'
                }
              ]
            }
          ],
          'title': 'Workplace'
        },
        {
          'type': 'group',
          'field': 'jobTitle',
          'items': [
            {
              'type': 'string',
              'field': 'name',
              'title': 'Job Title Key'
            },
            {
              'type': 'string',
              'field': 'value',
              'title': 'Job Title'
            }
          ],
          'title': 'Job title'
        },
        {
          'type': 'group',
          'field': 'specialties',
          'items': [
            {
              'type': 'string',
              'field': 'name',
              'title': 'Specialtiy Key'
            },
            {
              'type': 'repeater',
              'field': 'specialtyItem',
              'items': [
                {
                  'type': 'string',
                  'field': 'value',
                  'title': 'Specialtiy'
                }
              ]
            }
          ],
          'title': 'Specialties'
        },
        {
          'type': 'group',
          'field': 'training',
          'items': [
            {
              'type': 'string',
              'field': 'name',
              'title': 'Training Key'
            },
            {
              'type': 'repeater',
              'field': 'trainingItem',
              'items': [
                {
                  'type': 'string',
                  'field': 'value',
                  'title': 'Training'
                }
              ]
            }
          ],
          'title': 'Training'
        },
        {
          'type': 'group',
          'field': 'awards',
          'items': [
            {
              'type': 'string',
              'field': 'name',
              'title': 'Award Key'
            },
            {
              'type': 'repeater',
              'field': 'awardItem',
              'items': [
                {
                  'type': 'string',
                  'field': 'value',
                  'title': 'Award'
                }
              ]
            }
          ],
          'title': 'Awards'
        },
        {
          'type': 'group',
          'field': 'description',
          'items': [
            {
              'type': 'string',
              'field': 'title',
              'title': 'Title'
            },
            {
              'type': 'code',
              'field': 'content',
              'title': 'Description'
            },
            {
              'type': 'string',
              'field': 'short',
              'title': 'Short Description (for experts list page)'
            }
          ],
          'title': 'Description'
        },
        {
          'type': 'repeater',
          'field': 'social',
          'items': [
            {
              'type': 'string',
              'field': 'name',
              'title': 'Social Name'
            },
            {
              'type': 'string',
              'field': 'icon',
              'title': 'Icon (class name)'
            },
            {
              'type': 'link',
              'field': 'link',
              'title': 'Link'
            }
          ],
          'title': 'Social'
        }
      ],
      'title': 'Content'
    },
    {
      "type": "feature",
      "field": "footer",
      "title": "Footer"
    },
  ],
  "other": [
    {
      "type": "feature",
      "field": "social",
      "title": "Social"
    },
    {
      "type": "feature",
      "field": "contacts",
      "title": "Contacts"
    },
    {
      "type": "feature",
      "field": "webcomponents",
      "title": "Webcomponents"
    },
  ]
};
