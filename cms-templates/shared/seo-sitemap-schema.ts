import { TemplatesTabItemInterface } from '../../apps/website/src/app/api/v1/templates/templates.interface';

export enum ChangefreqEnum {
  always = 'always',
  hourly = 'hourly',
  daily = 'daily',
  weekly = 'weekly',
  monthly = 'monthly',
  yearly = 'yearly',
  never = 'never'
}

export enum PriorityEnum {
  zeroPointZero = '0.0',
  zeroPointOne = '0.1',
  zeroPointTwo = '0.2',
  zeroPointThree = '0.3',
  zeroPointFour = '0.4',
  zeroPointFive = '0.5',
  zeroPointSix = '0.6',
  zeroPointSeven = '0.7',
  zeroPointEight = '0.8',
  zeroPointNine = '0.9',
  onePointZero = '1.0',
}

export const seoSitemapSchema = (
  changefreq: ChangefreqEnum = ChangefreqEnum.monthly,
  priority: PriorityEnum = PriorityEnum.onePointZero
): TemplatesTabItemInterface => {
  return {
    'type': 'group',
    'field': 'sitemap',
    'title': 'Sitemap',
    'items': [
      {
        'type': 'dropdown',
        'field': 'changefreq',
        'title': 'Changefreq',
        'options': [
          {
            'title': 'Always',
            'value': 'always'
          },
          {
            'title': 'Hourly',
            'value': 'hourly'
          },
          {
            'title': 'Daily',
            'value': 'daily'
          },
          {
            'title': 'Weekly',
            'value': 'weekly'
          },
          {
            'title': 'Monthly',
            'value': 'monthly'
          },
          {
            'title': 'Yearly',
            'value': 'yearly'
          },
          {
            'title': 'Never',
            'value': 'never'
          }
        ],
        'defaultValue': changefreq
      },
      {
        'type': 'dropdown',
        'field': 'priority',
        'title': 'Priority',
        'options': [
          {
            'title': '0.0',
            'value': '0.0'
          },
          {
            'title': '0.1',
            'value': '0.1'
          },
          {
            'title': '0.2',
            'value': '0.2'
          },
          {
            'title': '0.3',
            'value': '0.3'
          },
          {
            'title': '0.4',
            'value': '0.4'
          },
          {
            'title': '0.5',
            'value': '0.5'
          },
          {
            'title': '0.6',
            'value': '0.6'
          },
          {
            'title': '0.7',
            'value': '0.7'
          },
          {
            'title': '0.8',
            'value': '0.8'
          },
          {
            'title': '0.9',
            'value': '0.9'
          },
          {
            'title': '1.0',
            'value': '1.0'
          }
        ],
        'defaultValue': priority
      },
      {
        'type': 'boolean',
        'field': 'exclude',
        'title': 'Exclude'
      }
    ]
  };
};
