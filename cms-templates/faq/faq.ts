import { ConfigInterface } from '../config.interface';
import { FaqItemDefaultSchema } from './faq-item-template/default';

export const faq: ConfigInterface[] = [
  {
    id: 'faq-item-template',
    enabled: true,
    name: '[COMMON] Template for regular FAQ item',
    templates: [
      {
        locale: 'default',
        schema: FaqItemDefaultSchema
      },
      // {
      //   locale: 'de-DE',
      //   schema: FaqItemDeSchema
      // }
    ]
  }
];
