{"$schema": "./node_modules/nx/schemas/nx-schema.json", "targetDefaults": {"build": {"dependsOn": [], "inputs": ["production", "^production"]}, "@nx/eslint:lint": {"inputs": ["default", "{workspaceRoot}/.eslintrc.json"], "cache": false}}, "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default", "!{projectRoot}/**/?(*.)+(spec|test).[jt]s?(x)?(.snap)", "!{projectRoot}/.eslintrc.json", "!{projectRoot}/**/*.stories.@(js|jsx|ts|tsx|mdx)", "!{projectRoot}/.storybook/**/*", "!{projectRoot}/tsconfig.storybook.json"], "sharedGlobals": []}, "defaultProject": "website", "generators": {"@nx/angular:application": {"style": "scss", "linter": "eslint"}, "@nx/angular:library": {"linter": "eslint"}, "@nx/angular:component": {"style": "scss"}}, "useInferencePlugins": false, "defaultBase": "main", "useLegacyCache": false}