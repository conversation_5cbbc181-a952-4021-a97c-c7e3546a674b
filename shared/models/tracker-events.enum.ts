export enum TrackerEventsEnum {
  initSpa = 'init_spa',
  initOrderResponse = 'init_order_response',
  findOrderResponse = 'find_order_response',
  viewOnlinePrescription = 'landed_on_bridge_page',
  startConsultation = 'start_consultation_click',
  sentStartConsultationRequest = 'request_start_consultation',
  receivedStartConsultationResponse = 'response_start_consultation',
  processRedirectCommand = 'process_redirect_command',
  redirectToConsultation = 'start_redirect_to_consultation',
  landedOnFinishConsultation = 'landed_on_finish_consultation_page',
  sentFinishConsultationRequest = 'finish_consultation_request',
  receivedFinishConsultationResponse = 'finish_consultation_response',
  viewRegistrationPage = 'render_registration_page',
  registrationFormChanged = 'customer_change_form',
  signUpSubmitForm = 'signup_submit_click',
  sentRegistrationRequest = 'save_signup_form_request',
  receivedRegistrationResponse = 'save_signup_form_response',
  registrationSuccess = 'signup_finished',
  registrationBackClicked = 'sign_up_back_click',
  renderChooseProductV1 = 'render_choose_product_v1',
  renderChoosePackageV1 = 'render_choose_package_v1',
  renderChooseProductV2 = 'render_choose_product_v2',
  renderChoosePackageV2 = 'render_choose_package_v2',
  changeModification = 'change_modification',
  clickSaveTreatment = 'click_save_treatment',
  saveTreatmentResponse = 'request_save_treatment', // probably needs rename
  saveProductRequest = 'save_product_request',
  saveProductResponse = 'save_product_response',
  savePackageRequest = 'save_package_request',
  savePackageResponse = 'save_package_response',
  renderShippingDetails = 'render_shipping_details',
  changeShippingForm = 'change_shipping_form',
  shipmentStepEmailValidationError = 'shipment_step_email_validation_error',
  clickSaveShippingDetails = 'click_save_shipping_details',
  saveShippingDetailsResponse = 'request_save_shipping_details', // probably needs rename
  renderPaymentPage = 'render_payment_page',
  applyPromoCode = 'apply_promo_code',
  applyUpsell = 'apply_upsell',
  paymentFailed = 'payment_failed',
  paymentSuccess = 'payment_success',
  thankYouPage = 'thank_you_page',
  redirectToMainPage = 'checkout_redirect_to_main_page',
  successPageModalCloseWithoutPhone = 'success_page_modal_close_without_phone',
  successPagePhoneAdded = 'success_page_phone_added',
  redirectTo3DS = '3ds_inject',
  signInUnsuccessful = 'sign_in_unsuccessful',
  resetPasswordClicked = 'reset_password_clicked',
  otpClicked = 'otp_clicked',
  otpLogin = 'otp_login',
  resetPasswordLinkSuccessLoggedLn = 'reset_password_link_success_logged_in',
  onlinePrescriptionEmailPopupShown = 'online_prescription_email_popup_shown',
  liveChatOpened = 'livechat_opened',
  liveChatClosed = 'livechat_closed',
  cantOrderByGenderPopupShown = 'cant_order_by_gender_popup_shown',
  smartSearchSearchButtonClicked = 'smart_search_search_button_clicked',
  smartSearchProductClicked = 'smart_search_product_clicked',
  smartSearchNothingFound = 'smart_search_nothing_found',
  onlinePrescriptionBridgePageContentLoadingStart = 'online_prescription_content_loading_start',
  onlinePrescriptionBridgePageContentLoadingSuccess = 'online_prescription_content_loading_success',
  onlinePrescriptionBridgePageContentLoadingFinalize = 'online_prescription_content_loading_finalize',
  changeStoreBridge = 'change_store_bridge',
  changeStoreBridgeClosePopup = 'change_store_bridge_close_popup',
  changeStoreShipment = 'change_store_shipment',
  otcModalOpened = 'otc_modal_opened',
  otcModalClosed = 'otc_modal_closed',
  createSubscription = 'create_subscription',
  renderSubscriptionShipping = 'render_subscription_shipping',
  renderSubscriptionPayment = 'render_subscription_payment',
  subscriptionPaymentFailed = 'subscription_payment_failed',
  subscriptionPaymentSuccess = 'subscription_payment_success',
  subscriptionThankYouPage = 'subscription_thank_you_page',
  braintreeCCRegularFailedWithout3DS = 'braintree_cc_regular_failed_without_3ds',
  braintreeCCSavedFailedWithout3DS = 'braintree_cc_saved_failed_without_3ds',
  selectionRegion = 'selection_region',
  selectionRegionTheSameRegion = 'selection_region_the_same_region',
  communicationWidgetOpened = 'communication_widget_opened',
  communicationWidgetClosed = 'communication_widget_closed',
  communicationWidgetFreshChatOpened = 'communication_widget_fresh_chat_opened',
  communicationWidgetFreshChatClosed = 'communication_widget_fresh_chat_closed',
  communicationWidgetWhatsappClick = 'communication_widget_whatsapp_click',
  communicationWidgetFaqClick = 'communication_widget_faq_click',
  communicationWidgetCallClick = 'communication_widget_call_click',
  paymentMethodSelected = 'payment_method_selected',
  initOTCProductPage = 'init_otc_product_page',
  changingPlanOTC = 'changing_plan_otc',
  choosePlanOTC = 'choose_plan_otc',
  orderDetailsOtcContinue = 'order_details_otc_continue',
  klarnaClicked = 'klarna_clicked',
  failedKlarna = 'failed_klarna',
  subscriptionKlarnaClicked = 'subscription_klarna_clicked',
  subscriptionFailedKlarna = 'subscription_failed_klarna',
  subscriptionBraintreePayPalClicked = 'subscription_braintree_pay_pal_clicked',
  subscriptionBraintreePayPalFailed = 'subscription_braintree_pay_pal_failed',
  subscriptionBraintreeClicked = 'subscription_braintree_clicked',
  oneTimeChosen = 'one-time-chosen',
  subscriptionChosen = 'subscription-chosen',
  chDisclaimerOpened = 'ch_disclaimer_opened',
  renderSubscriptionRegistrationPage = 'render_subscription_signup_page',
  renderSubscriptionSigninPage = 'render_subscription_signin_page',
  renderSubscriptionOrderDetailsPage = 'render_subscription_order_details_page',
  renderSubscriptionThankYouPage = 'render_subscription_thank_you_page',
  applePayFailed = 'apple_pay_failed',
  googlePayFailed = 'google_pay_failed',
  applePaySubscriptionFailed = 'apple_pay_subscription_failed',
  googlePaySubscriptionFailed = 'google_pay_subscription_failed',
  payPalFailed = 'pay_pal_failed',
  brainTreeGetClientTokenFailed = 'braintree_cc_get_current_token_failed',
  brainTreePlaceOrderFailed = 'braintree_place_order_failed',
  brainTreeSetThreeDsFailed = 'braintree_set_three_ds_failed',
  brainTreeSetClientFailed = 'braintree_set_client_failed',
  brainTreeFailed = 'braintree_failed',
  brainTreeSetThreeDsSubscriptionFailed = 'braintree_set_three_ds_subscription_failed',
  brainTreeSetClientSubscriptionFailed = 'braintree_set_client_subscription_failed',
  brainTreeSubscriptionFailed = 'braintree_subscription_failed',
  brainTreeInitRecurringSubscriptionFailed = 'braintree_init_recurring_subscription_failed',
  subscriptionBrainTreeGetClientTokenFailed = 'subscription_braintree_cc_get_current_token_failed',
  crossSaleSwitchSaveButtonClick = 'cross_sale_switch_save_button_click',
  crossSaleSwitchSaveButtonBuySaveClick = 'cross_sale_switch_save_button_buy_save_click',
  crossSaleSwitchSaveButtonCancelClick = 'cross_sale_switch_save_button_cancel_click',
  crossSaleSwitchSaveButtonModalClose = 'cross_sale_switch_save_button_modal_close',
  crossSaleContinueButtonClick = 'cross_sale_continue_button_click',
  crossSaleContinueButtonBuySaveClick = 'cross_sale_continue_button_buy_save_click',
  crossSaleContinueButtonCancelClick = 'cross_sale_continue_button_cancel_click',
  crossSaleContinueButtonModalClose = 'cross_sale_continue_button_modal_close',
  crossSaleMorePackageSizesClick = 'cross_sale_more_package_sizes_click',
  upSaleSwitchSaveButtonClick = 'up_sale_switch_save_button_click',
  upSaleSwitchSaveButtonBuySaveClick = 'up_sale_switch_save_button_buy_save_click',
  upSaleSwitchSaveButtonCancelClick = 'up_sale_switch_save_button_cancel_click',
  upSaleSwitchSaveButtonModalClose = 'up_sale_switch_save_button_modal_close',
  upSaleContinueButtonClick = 'up_sale_continue_button_click',
  upSaleContinueButtonBuySaveClick = 'up_sale_continue_button_buy_save_click',
  upSaleContinueButtonCancelClick = 'up_sale_continue_button_cancel_click',
  upSaleContinueButtonModalClose = 'up_sale_continue_button_modal_close',
  upSaleMorePackageSizesClick = 'up_sale_more_package_sizes_click',
  mainBannerCtaClick = 'main_banner_cta_click',
  mainBannerSliderDotClick = 'main_banner_slider_dot_click',
  mainBannerSliderCtaClick = 'main_banner_slider_cta_click',
  bridgePagesConsentSeeMore = 'bridge_page_consent_see_more',
  renderOneTimeSignupPage = 'render_one_time_signup_page',
  renderOneTimeSigninPage = 'render_one_time_signin_page',
  renderOneTimeOrderDetailsPage = 'render_one_time_order_details_page',
  renderOneTimeThankYouPage = 'render_one_time_thank_you_page',
  renderOneTimeShipping = 'render_one_time_shipping',
  renderOneTimePayment = 'render_one_time_payment',
  klarnaAuthenticationAttemptClick = 'klarna_authentication_attempt',
  klarnaAuthenticationSuccess = 'klarna_authentication_success',
  klarnaAuthenticationFail = 'klarna_authentication_fail',
  googleOauthCheckoutRegistrationClick = 'google_oauth_checkout_registration_click',
  googleOauthCheckoutLoginClick = 'google_oauth_checkout_login_click',
  googleOauthCheckoutSuccess = 'google_oauth_checkout_success',
  googleOauthSubscriptionRegistrationClick = 'google_oauth_subscription_registration_click',
  googleOauthSubscriptionLoginClick = 'google_oauth_subscription_login_click',
  googleOauthSubscriptionSuccess = 'google_oauth_subscription_success',
  googleOauthOnetimeRegistrationClick = 'google_oauth_onetime_registration_click',
  googleOauthOnetimeLoginClick = 'google_oauth_onetime_login_click',
  googleOauthOnetimeSuccess = 'google_oauth_onetime_success',
  welcomePopupAppeared = 'welcome_popup_appeared',
  welcomePopupStartTyping = 'welcome_popup_start_typing',
  welcomePopupSuccessScreenAppeared = 'welcome_popup_success_screen_appeared',
  welcomePopupErrorCustomerAlreadyExists = 'welcome_popup_error_customer_already_exists',
  welcomePopupErrorValidationError = 'welcome_popup_error_validation_error',
  welcomePopupErrorBounceRate = 'welcome_popup_error_bounce_rate',
  crossalePopupButtonClicked = 'crossale_popup_button_clicked',
  crossalePopupButtonClosed = 'crossale_popup_button_closed',
  reservationPolicyClick = 'reservation_policy_click',
  otherPaymentOptionsClick = 'other_payment_options_click',
  otherProductsLinkClicked = 'other_products_link_clicked',
  addProductsLinkClicked = 'add_products_link_clicked',
  otcProductAdded = 'otc_product_added',
  productInformationLinkClicked = 'product_information_link_clicked',
  otcProductRemoved = 'otc_product_removed',
  promoRemoved = 'promo_removed',
  upsaleClicked = 'upsale_clicked',
  downsaleClicked = 'downsale_clicked',
  otpRequestAttempt = 'otp_request_attempt',
  otpFormShown = 'otp_form_shown',
  otpInvalidAuthentication = 'otp_invalid_authentication',
  otpExpiredAuthentication = 'otp_expired_authentication',
  otpAuthenticationSuccess = 'otp_authentication_success',
  otpTooManyRequestsAuthentication = 'otp_too_many_requests_authentication',
  otpCodeSent = 'otp_code_sent',
  otpEmailError = 'otp_email_error',
  resetPasswordFormShown = 'reset_password_form_shown',
  resetPasswordEmailError = 'reset_password_email_error',
  labelTreatmentFeeIncludedClicked = 'label_treatment_fee_included_clicked',
  repeatServicePopupOpened = 'repeat_service_popup_opened',
  repeatServiceUnavailable = 'repeat_service_unavailable',
  repeatServiceUnavailablePackage = 'repeat_service_unavailable_package',
  repeatServiceCtaClicked = 'repeat_service_cta_clicked',
  repeatServiceOneTimeCtaClicked = 'repeat_service_one-time_cta_clicked',
  repeatServicePrivacyLinkClicked = 'repeat_service_privacy_link_clicked',
  repeatServiceResponse = 'repeat_service_response',
  repeatServiceSwitchedOneTime = 'repeat_service_switched_one_time',
  repeatServiceSwitchedSubscription = 'repeat_service_switched_subscription',
  repeatServiceUnavailableRestriction = 'repeat_service_unavailable_restriction',
  exitIntentPopupShown = 'exit_intent_popup_shown',
  exitIntentPopupClosedByCTAClick = 'exit_intent_popup_closed_by_CTA_click',
  exitIntentPopupClosedByOutsideClick = 'exit_intent_popup_closed_by_outside_click',
  repeatServiceDisabled = 'repeat_service_disabled',
  defaultServiceChanged = 'default_service_changed',
  suggestedAddressPopupOpened = 'suggested_address_popup_opened',
  suggestedAddressPopupCloseButton = 'suggested_address_popup_close_button',
  suggestedAddressPopupProceedButton = 'suggested_address_popup_proceed_button',
  suggestedAddressPopupClosed = 'suggested_address_popup_closed',
  suggestedAddressPopupInitialChosen = 'suggested_address_popup_initial_chosen',
  suggestedAddressPopupInitialClicked = 'suggested_address_popup_initial_clicked',
  wrongAddressPopupOpened = 'wrong_address_popup_opened',
  wrongAddressPopupCloseButton = 'wrong_address_popup_close_button',
  wrongAddressPopupProceedButton = 'wrong_address_popup_proceed_button',
  wrongAddressPopupClosed = 'wrong_address_popup_closed',
  stickyCartShowClicked = 'sticky_cart_show_clicked',
  stickyCartHideClicked = 'sticky_cart_hide_clicked',
  stickyCartTreatmentFeeIncludedClicked = 'sticky_cart_treatment_fee_included_clicked',
  stickyCartCTAClicked = 'sticky_cart_CTA_clicked',
  orderHistoryThankYouPageClicked = 'order_history_thank_you_page_clicked',
  skipSubscription = 'skip_subscription',
  skipSubscriptionConfirmation = 'skip_subscription_confirmation',
  orderHistoryOTCThankYouPageClicked = 'order_history_otc_thank_you_page_clicked',
  orderHistoryFromEmailClicked = 'order_history_from_email_clicked',
  orderHistoryHasBeenProcessedModalClicked = 'order_history_has_been_processed_modal_clicked',
  accountDetailsClicked = 'account_details_clicked',
  accountOrderHistoryClicked = 'account_order_history_clicked',
  accountMySubscriptionClicked = 'account_my_subscription_clicked',
  verificationModalClosed = 'verification_modal_closed',
  verificationModalVerificationStarted = 'verification_modal_verification_started',
  blogStickyCtaClicked = 'blog_sticky_cta_clicked',
  otpPasswordLinkBackToLoginClicked = 'otp_password_link_back_to_login_clicked',
  otpPasswordTabLoginClicked = 'otp_password_tab_login_clicked',
  otpPasswordTabOtpClicked = 'otp_password_tab_otp_clicked',
  hamStickyHeaderClicked = 'ham_sticky_header_clicked',
  buttonStickyHeaderClicked = 'button_sticky_header_clicked',
  addressChangeLinkClicked = 'address_change_link_clicked',
  accountChangeDeliveryDetailsSuccess = 'account_change_delivery_details_success',
  accountChangeDeliveryDetailsFail = 'account_change_delivery_details_fail',
  accountChangeDeliveryDetailsFailClose = 'account_change_delivery_details_fail_close',
  accountChangeDeliveryDetailsValidationError = 'account_change_delivery_details_validation_error',
  addressChanged = 'address_changed',
  addressChangeCancelled = 'address_change_cancelled',
  addressChangeCheckboxUnclicked = 'address_change_checkbox_unclicked',
  thankYouPageShowOrderDetailsClicked = 'thank_you-page_show_order_details_clicked',
  thankYouPageHideOrderDetailsClicked = 'thank_you-page_hide_order_details_clicked',
  thankYouPageCrossSaleClicked = 'thank_you-page_cross_sale_clicked',
  thankYouPageCopyToClipboardClicked = 'thank_you-page_copy_to_clipboard_clicked',
  thankYouPageChangePasswordClicked = 'thank_you-page_change_password_clicked',
  thankYouPageChangePasswordSaveClicked = 'thank_you-page_change_password_save_clicked',
  thankYouPageContinueShoppingClicked = 'thank_you-page_continue_shopping_clicked',
  thankYouPageOrderHistoryClicked = 'thank_you-page_order_history_clicked',
  stickyCartCTAClickedSh = 'sticky_cart_CTA_clicked',
  stickyCartCTAInsideClickedSh = 'sticky_cart_CTA_inside_clicked',
  stickyCartWeiterClickedSh = 'sticky_cart_weiter_clicked',
  cancellationPopup1Shown = 'cancellation_popup1_shown',
  cancellationPopup2Shown = 'cancellation_popup2_shown',
  cancellationPopup3Shown = 'cancellation_popup3_shown',
  cancellationPopup4Shown = 'cancellation_popup4_shown',
  keepSubscriptionPopup1Clicked = 'keep_subscription_popup1_clicked',
  keepSubscriptionPopup2Clicked = 'keep_subscription_popup2_clicked',
  keepSubscriptionPopup3Clicked = 'keep_subscription_popup3_clicked',
  proceedCancellationClicked = 'proceed_cancellation_clicked',
  confirmCancellationClicked = 'confirm_cancellation_clicked',
  manageSubscriptionChangeDeliveryFrequency = 'manage_subscription_change_delivery_frequency',
  manageSubscriptionClicked = 'manage_subscription',
  manageSubscriptionChangeDeliveryFrequency1month = 'manage_subscription_change_delivery_frequency_1month',
  manageSubscriptionChangeDeliveryFrequency3month = 'manage_subscription_change_delivery_frequency_3month',
  cancelChangeFrequency = 'cancel_change_frequency',
  cancelSubscriptionAccountClicked = 'cancel_subscription_account_clicked',
  cancelSubscription = 'cancel_subscription',
  keepSubscription = 'keep_subscription',
  cancelSubscriptionAnyway = 'cancel_subscription_anyway',
  serviceChangedOnetime = 'service_changed_onetime',
  serviceChangedRepeatService = 'service_changed_repeat_service',
  dontMissPopupShown = 'dont_miss_popup_shown',
  dontMissPopupCtaClicked = 'dont_miss_popup_cta_clicked',
  dontMissPopupLinkClicked = 'dont_miss_popup_link_clicked',
  openGmailClicked = 'open_gmail_clicked',
  openOutlookClicked = 'open_outlook_clicked',
  braintreeIdealClicked = 'braintree_ideal_clicked',
  braintreeIdealFailed = 'braintree_ideal_failed',
  otpOtcPasswordTabLoginClicked = 'otp_otc_password_tab_login_clicked',
  otpOtcPasswordTabOtpClicked = 'otp_otc_password_tab_otp_clicked',
  otpOtcPasswordLinkBackToLoginClicked = 'otp_otc_password_link_back_to_login_clicked',
  otpOtcFormShown = 'otp_otc_form_shown',
  otpOtcRequestAttempt = 'otp_otc_request_attempt',
  otpOtcInvalidAuthentication = 'otp_otc_invalid_authentication',
  otpOtcExpiredAuthentication = 'otp_otc_expired_authentication',
  otpOtcTooManyRequestsAuthentication = 'otp_otc_too_many_requests_authentication',
  otpOtcAuthenticationSuccess = 'otp_otc_authentication_success',
  otpOtcCodeSent = 'otp_otc_code_sent',
  otpOtcEmailError = 'otp_otc_email_error',
  otpOtcOpenGmailClicked = 'otp_otc_open_gmail_clicked',
  otpOtcOpenOutlookClicked = 'otp_otc_open_outlook_clicked',
  addressChangePackstationChosen ='address_change_packstation_chosen',
  addressChangeAddressChosen ='address_change_address_chosen',
  backToSettings ='back_to_settings',
  addressChangedPackstation ='address_changed_packstation',
  changePackageSize ='change_package_size',
  allPackageSizes ='all_package_sizes',
  changePackageSizeConfirm ='change_package_size_confirm',
  tooManyPillsReasonChangeDeliveryFrequency = 'too_many_pills_reason_change_delivery_frequency',
  tooManyPillsReasonSkipSubscription = 'too_many_pills_reason_skip_subscription',
  tooManyPillsCancelAnyway = 'too_many_pills_cancel_anyway',
  tooManyPillsReasonChangePackageSize = 'too_many_pills_reason_change_package_size',
  tooManyPillsReasonChangePackageSizeConfirm = 'too_many_pills_reason_change_package_size_confirm',
  tooManyPillsReasonSkipSubscriptionConfirmation = 'too_many_pills_reason_skip_subscription_confirmation',
  expensive = 'expensive',
  switchMedication = 'switch_medication',
  cancelAnywaySwitchMedication = 'cancel_anyway_switch_medication',
  previewNotifyAuthorised = 'preview_notify_authorised',
  previewNotifyUnauthorised = 'preview_notify_unauthorised',
  productViewNotifyAuthorised = 'product_view_notify_authorised',
  productViewNotifyUnauthorised = 'product_view_notify_unauthorised',
  previewNotifyAuthorisedConfirmed = 'preview_notify_unauthorised_confirmed',
  productViewNotifyUnauthorisedConfirmed = 'product_view_notify_unauthorised_confirmed',
  differentBillingShippingAddressOld = 'different-billing-shipping-address-old',
  switchedPackstationOld = 'switched-packstation-old',
  switchedAddressDeliveryOld = 'switched-address-delivery-old',
  switchedPackstation = 'switched-packstation',
  switchedAddressDelivery = 'switched-address-delivery',
  differentBillingShippingAddress = 'different-billing-shipping-address',
  editPatientDetails = 'edit-patient-details',
  editShippingDetails = 'edit-shipping-details',
  resubscribeCheckboxUncheck = 'resubscribe-checkbox-uncheck',
  resubscribeCheckboxCheck = 'resubscribe-checkbox-check',
  showProductsClicked = 'show-products-clicked',
  subscriptionsListUpdate = 'subscriptions_list_update',
  subscriptionsPaymentMethodChanged = 'subscriptions_payment_method_changed',
  subscriptionDetailsPaymentMethodEdit = 'subscription_details_payment_method_edit',
  emailUpdatePaymentMethodClicked = 'email_update_payment_method_clicked',
  loginAttemptByTokenIfActiveCampaign = 'login_attempt_by_token_if_active_campaign',
  loginSuccessByTokenIfActiveCampaign = 'login_success_by_token_if_active_campaign',
  errorByTokenIfActiveCampaign = 'error_by_token_if_active_campaign',
  errorEmailOTPActiveCampaign = 'error_email_otp_active_campaign',
  clickedToRegisterAfterEmailErrorIfActiveCampaign = 'clicked_to_register_after_email_error_if_active_campaign',
  subscriptionLoginAttemptByTokenIfActiveCampaign = 'subscription_login_attempt_by_token_if_active_campaign',
  subscriptionLoginSuccessByTokenIfActiveCampaign = 'subscription_login_success_by_token_if_active_campaign',
  subscriptionErrorByTokenIfActiveCampaign = 'subscription_error_by_token_if_active_campaign',
  subscriptionErrorEmailOTPActiveCampaign = 'subscription_error_email_otp_active_campaign',
  paymentTermsOfUseLinkClicked = 'payment_terms_of_use_link_clicked',
  subscriptionClickedToRegisterAfterEmailErrorIfActiveCampaign = 'subscription_clicked_to_register_after_email_error_if_active_campaign',
  subscriptionUpsellPopupShown = 'subscription_upsell_popup_shown',
  subscriptionUpsellOfferConfirmed = 'subscription_upsell_offer_confirmed',
  subscriptionUpsellOfferDeclined = 'subscription_upsell_offer_declined',
  subscriptionUpsellOfferClosed = 'subscription_upsell_offer_closed',
  oneTimeUpsellPopupShown = 'one_time_upsell_popup_shown',
  oneTimeUpsellOfferClicked = 'one_time_upsell_offer_clicked',
  oneTimeUpsellOfferDeclined = 'one_time_upsell_offer_declined',
  oneTimeUpsellOfferClosed = 'one_time_upsell_offer_closed',
  noTreatmentAddedBot = 'no-treatment-added-bot',
  notAvailableTreatment = 'not-available-treatment',
  learnMoreBotClicked = 'learn-more-bot-clicked',
  stickySildenafilOfferClicked = 'sticky_sildenafil_offer_clicked',
  wlConfirmOnlineRender = 'wl_confirm_online_render',
  wlConfirmOnlineHgLink = 'wl_confirm_online_hg_link',
  wlConfirmOnlineWithPrescription = 'wl_confirm_online_with_prescription',
  wlConfirmOnlineWithoutPrescription = 'wl_confirm_online_without_prescription',
  wlConfirmOnlineWithoutPrescriptionConsultationStart = 'wl_confirm_online_without_prescription_consultation_start',
  edConfirmOnlineRender = 'ed_confirm_online_render',
  edConfirmOnlineHgLink = 'ed_confirm_online_hg_link',
  edConfirmOnlineWithPrescription = 'ed_confirm_online_with_prescription',
  edConfirmOnlineWithoutPrescriptionConsultationStart = 'ed_confirm_online_without_prescription_consultation_start',
}
