export enum CookieEnum {
  statistic = 'statistic',
  marketing = 'marketing',
  crossDomain = 'crossDomain',
}

export enum CookiePolicyLogEvents {
  ACCEPTED_LP_COOKIES = 'accept_lp_cookies',
  ACCEPTED_WEBSITE_COOKIES = 'accept_website_cookies',
  SHOWED_COOKIE_MODAL = 'showed_cookie_modal',
  ALREADY_ACCEPTED_COOKIE = 'already_accepted_cookie',
}

export enum CookieQueryParamEnum {
  cookies = 'cookies'
}
