export enum LangCode {
  en = 'en',
  de = 'de',
  pl = 'pl',
  ch = 'ch',
  se = 'se',
  dk = 'dk',
  pt = 'pt',
  nl = 'nl'
}

export enum LangId {
  ch = "de-CH",
  de = "de-DE",
  en = "en-GB",
  pl = "pl-PL",
  pt = "pt-PT",
  se = "sv-SE",
  dk = "da-DK",
  nl = "nl-NL"
}

export enum CountryAlpha2 {
  EN = 'EN',
  DE = 'DE',
  PL = 'PL',
  PT = 'PT',
  SE = 'SE',
  CH = 'CH',
  GB = 'GB',
  DK = 'DK',
  NL = 'NL'
}
