export interface IOneTimePrice {
  code: string;
  value: number;
}
export interface IMSRP {
  code: string;
  value: number;
}

export interface IProduct { // TODO :All the typings here should be reviewed and reworked  - we already have common one
  id: number;
  category_id: number;
  image_data: string,
  modifications: {
    id: number;
    one_time_price: IOneTimePrice;
    msrp: IMSRP;
    max_sale_qty: number;
  }[],
  delivered_at: string;
}
