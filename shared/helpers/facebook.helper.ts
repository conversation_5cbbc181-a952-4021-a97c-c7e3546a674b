export enum FaceBookEventEnum {
  // Deprecated events
  lead = 'Lead',
  purchase = 'Purchase',
  subscription = 'Subscribe',
  // Actual events
  visit = 'CustomizeProduct',
  fbr = 'FBR', // Registrations
  fbp = 'FBP', // Purchases
  fbs = 'FBS', // Subscriptions
}

export const prefixMap = {
  [FaceBookEventEnum.lead]: 'lead', // CTA button registration page
  [FaceBookEventEnum.purchase]: 'order_placed', // Thank you page
  [FaceBookEventEnum.visit]: 'visit', // Bridge page
  [FaceBookEventEnum.subscription]: 'subscription', //  Thank you page
  [FaceBookEventEnum.fbr]: 'fbr', //  // CTA button registration page
  [FaceBookEventEnum.fbp]: 'fbp', //  Thank you page
  [FaceBookEventEnum.fbs]: 'fbs', //  Thank you page
};

// It will be used to send event after cookiePolicy modal was initialised after we tried to track fb event
const trackEventWhenFaceBookInitialized = (event: FaceBookEventEnum, eventId: string, eventData: any) => {
  let delayedEvent = { event, eventData, eventId };

  window.addEventListener('facebookInitialized', () => {
    if (!delayedEvent) return;

    facebookTrackEvent(delayedEvent.event,  delayedEvent.eventId, delayedEvent.eventData);
    delayedEvent = null;
  });

};

export function facebookTrackEvent(event: FaceBookEventEnum, eventId: string, eventData: any = {}) {
  if (!(window as any).fbq) {
    console.warn('No facebook integration');
    trackEventWhenFaceBookInitialized(event, eventId, eventData);
    return;
  }

  (window as any).fbq('track', event, eventData, {
    eventID: eventId
  });
}

