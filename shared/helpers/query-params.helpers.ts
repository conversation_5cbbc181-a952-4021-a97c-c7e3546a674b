export class QueryParamsHelpers {
  static queryParamsArray(search: string = window.location.search): [string, string][] {
    const queryParamsStr = search.replace(/^\?/, '');
    if (!queryParamsStr) return [];
    return queryParamsStr
      .split('&')
      .map(x => {
        let [key, value] = x.split('=') as [string, string];
        return [this.safeDecodeURIComponent(key), this.safeDecodeURIComponent(value)]
      });
  }

  static get queryParams(): { [key: string]: string } {
    return this.queryParamsArray()
      .reduce((acc, [key, value]) => {
        acc[key] = value;
        return acc;
      }, {});
  }

  static queryParamsObject(search: string = window.location.search): {[key: string]: string; } {
    return this.queryParamsArray(search)
      .reduce((acc, [key, value]) => ({
        ...acc,
        [key]: value,
      }), {});
  }

  static getParamsArray() {
    return window.location.pathname.replace(/^\//, '').split('/');
  }

  static normaliseCheckoutParams(): any {
    let initialParams: { [key: string]: any} = {};
    new URLSearchParams(window.location.search).forEach((value, key) => {
      initialParams[key] = value;
    });

    if (initialParams.category_id instanceof Array) {
      throw new Error('query param `category_id` cannot be array');
    }

    if (initialParams.product_id instanceof Array) {
      throw new Error('query param `product_id` cannot be array');
    }

    if (initialParams.modification_id instanceof Array) {
      throw new Error('query param `modification_id` cannot be array');
    }

    return {
      ...initialParams,
      finish_consultation: initialParams.finish_consultation === 'true',
      start_order: initialParams.start_order === 'true',
      category_id: (initialParams.category_id && Number(initialParams.category_id)) || (initialParams.cat_id && Number(initialParams.cat_id)),
      product_id: initialParams.product_id && Number(initialParams.product_id),
      modification_id: initialParams.modification_id && Number(initialParams.modification_id),
      smartToken: initialParams.authToken || initialParams.auth_token,
      prescription_provider: initialParams.prescription_provider,
      is_landing: initialParams.is_landing === 'true',
    };
  }

  static queryParamsToString(queryParams: { [key: string]: string | number }): string {
    const query = Object
      .entries(queryParams)
      .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
      .join('&');

    return query.length ? `?${query}` : '';
  }

  static getLinkWithoutCertainQueryParams(link: string, paramsToRemove: string[]): string {
    if (!paramsToRemove.length) return link;

    const [originLink, search] = link.split('?');

    if (!search?.length) return link;

    const queryParams: {[key: string]: string} = QueryParamsHelpers.queryParamsObject(search);

    paramsToRemove.forEach((param) => {
      delete queryParams[param];
    })

    return originLink + QueryParamsHelpers.queryParamsToString(queryParams);
  }

  static getQueryParamByName(name: string): string {
    const queryParams: URLSearchParams = new URLSearchParams(window.location.search);
    const value: string = queryParams.get(name);
    return value ? `${name}=${value}` : '';
  }

  private static safeDecodeURIComponent(value) {
    try {
      return decodeURIComponent(value);
    } catch (e) {
      console.error("Error decoding URI component:", e, "Value:", value);
      return value;
    }
  }
}
