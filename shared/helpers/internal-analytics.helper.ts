export function internalAnalyticsHelper(event: string, context = {}) {
  if (!(window as any).apolytics) {
    console.warn('No apolytics initialised');
    return;
  }

  if (Object.keys(context).length) {
    window.dispatchEvent(new CustomEvent(event, {
      detail: { ...context }
    }));
    return;
  }

  window.dispatchEvent(new CustomEvent(event));
}

export function addToContext(key: string, data: any) {
  if (!(window as any)['analyticsContext']) {
    (window as any)['analyticsContext'] = {};
  }

  (window as any)['analyticsContext'][key] = data;
}

export function removeFromContext(key: string) {
  if ((window as any)['analyticsContext']?.[key]) {
    (window as any)['analyticsContext'][key] = undefined;
  }
}

export function clearContext() {
  (window as any)['analyticsContext'] = {};
}
