class PassiveListenerHelperClass {
  passiveSupported = false;

  constructor() {
    this.detectSupport();
  }

  detectSupport() {
    try {
      let passiveSupported = false;

      const options = {
        get passive() {
          passiveSupported = true;
          return false;
        },
      };

      window.addEventListener('test', null, options);
      window.removeEventListener('test', null);

      this.passiveSupported = passiveSupported;
    } catch (err) {
      this.passiveSupported = false;
    }
  }
}

export const PassiveListenerHelper = new PassiveListenerHelperClass();
