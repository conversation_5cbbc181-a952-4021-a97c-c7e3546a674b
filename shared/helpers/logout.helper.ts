import { deleteCookie, getCookie } from './cookie.helper';
import { QueryParamsHelpers } from './query-params.helpers';

export const logout = (): Promise<any> => {
  const url = `${window['apomedsConfigs'].apiUrl}/workflow/public/auth/logout`;
  const authToken = getCookie('x-jwt-token');
  const queryParams = QueryParamsHelpers.queryParams;

  if('authToken' in queryParams) {
    delete queryParams.authToken;

    const targetUrl = `${window.location.origin}/${window['apomedsConfigs'].region}`;
    const search = QueryParamsHelpers.queryParamsToString({ ...queryParams });

    window.location.replace(targetUrl + search);
  }

  deleteCookie('x-jwt-token');
  localStorage.removeItem('current_token');
  localStorage.removeItem('cross_token');

  const headers = new Headers({
    'X-Jwt-Token': `Bearer ${authToken}`
  });

  const request = new Request(url, {
    method: 'POST',
    headers: headers
  });

  return fetch(request);
};
