import { loadScript } from '../../apps/workflow-spa-modules/src/app/shared/helpers/load-script.function';

enum FCWidgetSiteIdEnum {
  Apomeds = 'APO',
  Easymeds = 'EM',
}

export function initFCWidgetChat(hideChatButton: boolean = false) {
  const isDeStore = (window as any)['apomedsConfigs']?.region === 'de';
  const isChStore = (window as any)['apomedsConfigs']?.region === 'ch';
  const isNlStore = (window as any)['apomedsConfigs']?.region === 'nl';

  if ('fcWidget' in window) {
    (window as any)['fcWidget'].open();
    return Promise.resolve();
  } else {
    return loadScript('/scripts/freshchat.js').then(() => {
      return new Promise(() => {
        (window as any)['fcWidget']?.init({
          token: (window as any)['apomedsConfigs']?.freshChatWidgetToken,
          host: (window as any)['apomedsConfigs']?.freshChatWidgetHost,
          ...(isDeStore && { widgetUuid: (window as any)['apomedsConfigs']?.freshChatWidgetUuidDE}),
          ...(isChStore && { widgetUuid: (window as any)['apomedsConfigs']?.freshChatWidgetUuidCH}),
          ...(isNlStore && { widgetUuid: (window as any)['apomedsConfigs']?.freshChatWidgetUuidNL}),
          locale: (isDeStore || isChStore) ? 'de' : (window as any)['apomedsConfigs']?.region,
          open: true,
          config: {
            headerProperty: { hideChatButton },
          },
        });

        (window as any)['fcWidget']?.user.setProperties({
          email: (window as any)['currentUser']?.email,
          click_token: localStorage.getItem('current_token') || null,
          siteId: (window as any)['apomedsConfigs']?.isEasymeds ? FCWidgetSiteIdEnum.Easymeds : FCWidgetSiteIdEnum.Apomeds,
        });
        generateCustomEvents();
      });
    });
  }
}

export function generateCustomEvents() {
  (window as any)['fcWidget'].on('widget:loaded', () => {
    window.dispatchEvent(new CustomEvent('fcWidgetLoaded'));
  });

  (window as any)['fcWidget'].on('widget:opened', () => {
    window.dispatchEvent(new CustomEvent('fcWidgetOpened'));
  });

  (window as any)['fcWidget'].on('widget:closed', () => {
    window.dispatchEvent(new CustomEvent('fcWidgetClosed'));
  });
}
