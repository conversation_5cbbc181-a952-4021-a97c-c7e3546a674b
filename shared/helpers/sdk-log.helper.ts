import { getCookie } from './cookie.helper';
import { GlobalTokens } from '../../apps/workflow-spa-modules/src/app/shared/models';
import jwt_decode from 'jwt-decode';
import { UserProfile } from '../models/user-profile.interface';

export function sdkLogHelper(event: string, context: any) {
  if (!(window as any).Tracker || !(window as any).Tracker.pushEvent) {
    console.warn('No tracker initialised');
    return;
  }

  (window as any).Tracker.pushEvent(event, {
    ...context,
    customer_id: getUserIdFromJwtToken()
  });
}

function getUserIdFromJwtToken(): number | null {
  const token = getCookie(GlobalTokens.authToken);

  if (token) {
    const decoded = jwt_decode<UserProfile>(token);

    return decoded?.user_id ? decoded.user_id : null;
  }

  return null;
}
