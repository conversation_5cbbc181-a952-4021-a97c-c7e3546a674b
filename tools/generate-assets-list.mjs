import { readFileSync, writeFile } from 'fs';
import { <PERSON><PERSON><PERSON> } from 'jsdom';

const projectRoot = `./dist/${process.env.PROJECT}`;

const indexData = readFileSync(`${projectRoot}/index.html`).toString();
const dom = new JSDOM(indexData);

const scripts = dom.window.document.querySelectorAll('script:not([data-skip])');
const styles = dom.window.document.querySelectorAll('link[rel=stylesheet]:not([data-skip])');

const assetsList = {
  scripts: Array.from(scripts).map((item) => item.outerHTML),
  styles: Array.from(styles).map((item) => item.outerHTML)
}

writeFile(`${projectRoot}/assets-list.json`, JSON.stringify(assetsList), "utf8", (error) => {
  if (error) {
    console.log(error);
  }
  console.log(`Assets List generated at ${projectRoot}/assets-list.json`);
});
