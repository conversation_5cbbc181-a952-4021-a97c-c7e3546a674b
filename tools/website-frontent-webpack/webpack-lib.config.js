const path = require('path');
const CssMinimizerPlugin = require("css-minimizer-webpack-plugin");
const TerserPlugin = require("terser-webpack-plugin");

const resolve = require('./webpack.resolve.config');
const postcssConfig = './tools/website-frontent-webpack/postcss.config.js';

const projectName = 'website-frontend';
const prod = process.env.CONFIG_ENV === 'prod';

const projectRoot = path.resolve(`${__dirname}/../..`);
const sourceDir = path.resolve(`${projectRoot}/apps/website-frontend/src`);

const distDir = `${projectRoot}/dist/website-frontend`;
const tsConfig = require.resolve('./tsconfig.json');

let bundlesArray = [];

const prodPlugins = [
  new TerserPlugin({
    test: /\.js($|\?)/i,
    parallel: true,
    terserOptions: {
      sourceMap: false,
      mangle: true
    }
  }),
  new CssMinimizerPlugin()
];

function createConfig() {
  console.log(__dirname);

  let plugins = [];

  if (prod) {
    plugins = [...prodPlugins];
  }

  const outputFileName = `${projectName}.min.js`;

  let entry = [`${sourceDir}/index.ts`];


  return {
    entry,
    target: 'web',
    watch: !prod,
    devtool: !prod && 'source-map',
    mode: prod ? 'production' : 'development',
    output: {
      path: distDir,
      filename: outputFileName,
      library: 'websiteFrontend',
      libraryTarget: 'var',
      clean: false
    },
    module: {
      rules: [
        {
          test: /\.ts?$/,
          use: [
            {
              loader: 'ts-loader',
              options: {
                context: __dirname,
                configFile: tsConfig
              }
            }
          ],
          exclude: /node_modules(?!\/webpack-dev-server)/
        },
        {
          test: /\.js?$/,
          use: [
            {
              loader: 'ts-loader',
              options: {
                context: __dirname,
                configFile: tsConfig
              }
            }
          ],
          exclude: /node_modules(?!\/webpack-dev-server)/
        },
        {
          test: /\.css$/,
          use: [
            {
              loader: 'file-loader',
              options: {
                name: 'css/[name].min.css'
              }
            },
            {
              loader: 'extract-loader'
            },
            {
              loader: 'css-loader',
              options: {
                importLoaders: 1,
                sourceMap: !prod
              }
            },
            {
              loader: 'postcss-loader',
              options: {
                postcssOptions: {
                  config: postcssConfig,
                  sourceMap: !prod
                }
              }
            }
          ]
        },
        {
          test: /\.scss$/,
          use: [
            {
              loader: 'file-loader',
              options: {
                name: 'css/[name].min.css'
              }
            },
            {
              loader: 'extract-loader'
            },
            {
              loader: 'css-loader',
              options: {
                importLoaders: 1,
                sourceMap: !prod,
                url: false
              }
            },
            {
              loader: 'postcss-loader',
              options: {
                postcssOptions: {
                  config: postcssConfig,
                  sourceMap: !prod
                }
              }
            },
            {
              loader: 'sass-loader',
              options: {
                sourceMap: !prod
              }
            }
          ]
        }
      ]
    },
    resolve,
    plugins
  };
}

bundlesArray = [
  createConfig(false),
];

module.exports = bundlesArray;
