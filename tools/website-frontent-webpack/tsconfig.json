{"compilerOptions": {"baseUrl": ".", "declaration": false, "module": "es2015", "moduleResolution": "node", "emitDecoratorMetadata": true, "removeComments": true, "noImplicitAny": false, "target": "es6", "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "noEmitHelpers": true, "importHelpers": true, "lib": ["dom", "es2018"], "checkJs": false, "sourceMap": true, "allowJs": true, "typeRoots": ["node_modules/@types"]}, "exclude": ["node_modules", ".history", "tmp"]}