# robots.txt Management

### 🔧 Local Development

The `robots.txt` file included here is a fallback solution that will work **locally** during development. You can modify it freely for testing purposes or to simulate crawler behavior in your local environment.

### 🚫 Staging, QA, Production

On **staging** **qa** and **production**, this file is **not used**.  
Instead, `robots.txt` (along with the entire `nginx.conf`) is fully **managed by the DevOps team**. During deployment, all local configuration files, including this one, are **overwritten** by the CI/CD pipeline or server provisioning scripts.

---

### ✅ Need to Update `robots.txt` on Prod/Stage/QA?

If you need to make changes to the `robots.txt` on production, staging or qa environments:

1. Contact the **DevOps team**.
2. Provide them with the updated rules or requirements.
3. Coordinate deployment if necessary.

---
