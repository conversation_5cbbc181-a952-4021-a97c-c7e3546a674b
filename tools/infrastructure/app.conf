location ~ /api/v1/status {
  set $upstream 127.0.0.1:8080;
  auth_basic off;
  allow all;
  proxy_pass http://$upstream;
}

location ~ /ui-components/ {
  add_header Expires "Wed, 21 Oct 2015 07:28:00 GMT";
  add_header Cache-Control "no-cache, no-store, must-revalidate, max-age=0";
  add_header Pragma "no-cache";
  root /app/dist;
}

location ~* ^/profile/.*\.(css|js)$ {
  add_header Expires "Wed, 21 Oct 2015 07:28:00 GMT";
  add_header Cache-Control "no-cache, no-store, must-revalidate, max-age=0";
  add_header Pragma "no-cache";
  root /app/dist/spa/;
  rewrite "^/(profile)/(.*)$" /$2 break;
}

location ~* \.(?:css(\.map)?|js(\.map)?|jpe?g|png|svg|gif|ico|cur|po|json|heic|webp|tiff?|mp3|m4a|aac|ogg|midi?|wav|mp4|mov|webm|mpe?g|avi|ogv|flv|wmv|woff|eot|ttf|webmanifest)$ {
  try_files $uri $uri/ /assets/$uri /assets/$uri/ @cdn;
  root /app/dist;
  add_header Expires "Wed, 21 Oct 2015 07:28:00 GMT";
  add_header Cache-Control "no-cache, no-store, must-revalidate, max-age=0";
  add_header Pragma "no-cache";
  add_header Access-Control-Allow-Origin "*";
  access_log off;
}

location @cdn {
  resolver ******* valid=60s ipv6=off;
  set $cdn cdn.apomeds.com;
  proxy_buffering off;
  proxy_ssl_server_name on;
  proxy_intercept_errors on; # DO-889
  error_page 403 =404 /404; # DO-889
  proxy_pass https://$cdn;
}

location ~* ^/spa/.*\.(css|js)$ {
  add_header Expires "Wed, 21 Oct 2015 07:28:00 GMT";
  add_header Cache-Control "no-cache, no-store, must-revalidate, max-age=0";
  add_header Pragma "no-cache";
  root /app/dist/consultation/;
  rewrite "^/(spa)/(.*)$" /$2 break;
}

location ~* ^/(assets|spa|css|fonts|img|profile|scripts|sprites|website-frontend)/(.*)$ {
  root /app/dist;
  add_header Expires "Wed, 21 Oct 2015 07:28:00 GMT";
  add_header Cache-Control "no-cache, no-store, must-revalidate, max-age=0";
  add_header Pragma "no-cache";
}

location /sitemap.xml {
  try_files $uri @cdn;
}

location /robots.txt {
  root /app/dist;
  try_files $uri =404;
}

location ~* ^/de/nuetzliche-artikel$ { # DO-904
  if ($arg_page) {
    return 301 $uri/page/$arg_page;
  }

  set $upstream 127.0.0.1:8080;
  proxy_pass http://$upstream;
  proxy_set_header Host $host;
  proxy_set_header X-Real-IP $remote_addr;
  proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
}

location / {
  set $upstream 127.0.0.1:8080;
  proxy_pass http://$upstream;
  proxy_set_header Host $host;
  proxy_set_header X-Real-IP $remote_addr;
  proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
}
