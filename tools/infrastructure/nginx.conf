worker_processes 2;
pid /run/nginx.pid;
events { worker_connections 1024; }
http {

    include       /etc/nginx/mime.types;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /dev/stdout main;
    error_log /dev/stdout warn;

    sendfile        on;
    keepalive_timeout  65;

    server {
        listen 80 default_server;
        include       /etc/nginx/conf.d/app.conf;
        set_real_ip_from 0.0.0.0/0;
        real_ip_header X-Forwarded-For;
        real_ip_recursive on;
        root /app/dist/;
        absolute_redirect off; #DO-921

    }

}
