[unix_http_server]
file=/var/run/supervisor.sock
chmod=0700

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[supervisorctl]
serverurl=unix:///var/run/supervisor.sock

[supervisord]
user=root
logfile=/dev/null
logfile_maxbytes=0
nodaemon=true

[program:nginx]
process_name=%(program_name)s
command=nginx -g "daemon off;"
autostart=true
autorestart=true
user=root
numprocs=1
redirect_stderr=true
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0

[program:frontendapp]
directory=/app
process_name=%(program_name)s
command=npm run start:prod
environment=PORT="8080"
autostart=true
autorestart=true
user=root
numprocs=1
redirect_stderr=true
stdout_logfile=/dev/fd/1
stdout_logfile_maxbytes=0
