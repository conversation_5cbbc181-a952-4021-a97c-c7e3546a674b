@import "colors.scss";
@import "variables.scss";

$form-width: 33.125em; // 530px/16px
$form-fsz: 20px; // 16px+25%
$form-title-fsz: 25px;
$form-subtitle-fsz: 19px;
$dynamic-form-fsz: 0.875em; // 20px/16px
$form-checkbox-fsz: 0.8125em; // 16px-15%/16px
$form-link-fsz: 0.8125em; // 16px-15%/16px
$form-sm-space: 1em; // 25px/16px
$form-md-space: 1.5625em; // 25px/16px
$form-lg-space: 2em; // 32px/16px

@mixin mobile-only() {
  @media (max-width: $mobile-screen-width) {
    @content;
  }
}

@mixin desktop-only() {
  @media (min-width: $mobile-screen-width) {
    @content;
  }
}

@mixin responsive-form-container {
  @include desktop-only {
    font-size: $form-fsz;
    max-width: $form-width;
  }
}

@mixin responsive-dynamic-form() {
  @include desktop-only {
    font-size: $dynamic-form-fsz;
  }
}

@mixin responsive-form-title() {
  margin: 0 0 20px;
  width: 100%;
  font-size: $form-title-fsz;
  color: var(--text-default-color);

  @include mobile-only {
    font-size: 16px;
    margin-bottom: 10px;
    margin-top: 0;
  }
}

@mixin responsive-form-subtitle() {
  @include responsive-form-title;
  font-size: $form-subtitle-fsz;
  font-weight: normal;
  margin: -10px 0 30px;

  @include mobile-only {
    margin: 0 0 30px;
  }
}

@mixin responsive-form-checkbox() {
  @include responsive-top-md-space;

  @include desktop-only {
    font-size: $form-checkbox-fsz;
  }
}

@mixin responsive-form-link() {
  @include desktop-only {
    font-size: $form-link-fsz;
    margin-bottom: $form-lg-space;
  }
}

@mixin responsive-top-md-space() {
  @include desktop-only {
    margin-top: $form-md-space;
  }
}

@mixin responsive-form-submit() {
  @include desktop-only {
    font-size: 1em; // 16px/16px
    margin-bottom: 0.875em; // 20px/16px

    &:after {
      font-size: 1.4375em; // 23px/16px
    }
  }
}
