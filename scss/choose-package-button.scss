@import "colors";
@import "variables";
@import "mixins";

.radio-rect {
  .modification-row {
    display: flex;
    text-align: left;
    flex-direction: column;

    > :first-child {
      margin-right: 0.25em;
    }

    @include mobile {
      font-size: 14px;
      flex-direction: column;

      > :first-child {
        font-size: 16px;
      }
    }

    @include medium-mobile {
      > :first-child {
        font-size: 14px;
      }
    }

    &__one-unit-price {
      font-weight: 400;
      font-size: 13px;
      line-height: 15px;
      color: $dim-gray;
      text-transform: none;;
    }
  }

  &--selected {
    .modification-row__one-unit-price {
      color: white;
    }
  }

  .price-row {
    display: flex;
    align-items: center;

    @media (max-width: 400px) {
      align-items: flex-end;
      flex-direction: column;

      .right-divider {
        margin-right: 0;
      }
    }
  }
}
