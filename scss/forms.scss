@import 'colors';
@import 'variables';
//@import 'mat-field';

.mat-input-element {
  font: inherit;
  background: rgba(0, 0, 0, 0);
  color: currentColor;
  border: none;
  outline: none;
  padding: 0;
  margin: 0;
  width: 100%;
  max-width: 100%;
  vertical-align: bottom;
  text-align: inherit;
  box-sizing: content-box;
}

input:not(.mat-mdc-input-element, .mat-input-element, .shipment-input, .no-style, [type="radio"], .quick-product-search__input):not(:where(kit-input, kit-input-phone) *), textarea {
  width: 100%;
  height: 42px;
  margin-top: 8px;
  padding: 11px 15px 11px;
  border: 1px solid $blue-grey;
  border-radius: 3px;
  font-size: 16px;
  color: $dark-blue;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;

  &::placeholder {
    color: $sky-blue;
  }

  &:disabled {
    background: $light-grey;
    border: 1px solid $sky-blue;
    color: $blue-grey;
  }
}

input.no-style {
  font-family: Roboto, sans-serif;
  font-size: 1em;
  width: 100%;
  height: auto;
  padding: 0;
  margin: -0.0625em 0 0 0;
  color: #112950;
  border: none;
}

.header-search-input {
  height: auto !important;
  margin-top: 0 !important;
  padding: 10px !important;
  border: 1px solid $sky-blue !important;
  font-size: 13px !important;
}

input.form-error,
textarea.form-error {
  border-color: $red;
}

label {
  display: block;
  margin-top: 15px;
  font-size: 14px;
  font-weight: 500;
  color: $dark-blue;

  &:first-child {
    margin-top: 0px;
  }
}

.form-control-danger {
  border: 1px solid $red;
}

.mat-menu-content {
  font-size: 16px;
  color: $dark-blue;
}

.mat-menu-content:not(:empty) {
  padding: 0px !important;
}

.mat-menu-panel {
  min-height: auto;
}

div.mat-radio-outer-circle {
  border-color: $blue-grey;
}

.mat-radio-button.mat-accent .mat-radio-inner-circle {
  background-color: var(--modal-radio-checked-color) !important;
}

.mat-radio-button.mat-accent {
  .mat-radio-outer-circle {
    border-color: var(--form-radio-outer-circle-color);
  }

  &.mat-radio-checked .mat-radio-outer-circle {
    border-color: var(--modal-radio-checked-color) !important;
  }
}

.mat-radio-label-content {
  display: flex !important;
  align-items: center;
  color: $dark-blue;
  white-space: normal;
  flex-wrap: wrap;
  min-width: 160px;
}

.mat-radio-button {
  display: block !important;
}

.mat-radio-button:last-child .mat-radio-container,
.mat-radio-button:last-child .mat-radio-label-content {
  margin-bottom: 0px;
}

input {
  outline: none;

  [type='date'] {
  }
}

.form-errors {
  font-weight: 400;
  color: $red;
  margin-top: 0;
  margin-bottom: 5px;
  font-size: 12px;
  text-align: left;
}

.mat-checkbox-label {
  font-size: $base-font-size;
  font-weight: 400;
}

.mdc-checkbox__background {
  border-color: var(--form-checkbox-border-color);
}

//.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-disabled .mat-mdc-select-arrow {
//  color: transparent;
//}
