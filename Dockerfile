# docker build -t registry.t4u-ho.team/apomeds-front-store .
# docker run --rm -ti -p 8080:80 registry.t4u-ho.team/apomeds-front-store
# docker push registry.t4u-ho.team/apomeds-front-store
# Stage 1 - this stage installs our modules
FROM node:18-alpine as build-deps

# Install AWS SDK
RUN apk add --no-cache py3-pip python3 python3-dev gcc libc-dev \
    && pip3 install awscli --break-system-packages\
    && rm -rf /var/cache/apk/*

# Add path
ENV PATH /app/node_modules/.bin:$PATH

WORKDIR /app

COPY package.json ./
COPY .npmrc ./
# Install app dependencies
RUN npm install

# Add app
COPY . .
ARG BUILD_VERSION
ARG CDN_URL="https://cdn.apomeds.com/"

RUN npm run version
RUN npm run lint
RUN npm run build

# Remove netire node modules and reinstall only for prod
#RUN rm -rf node_modules
RUN npm install --omit=dev --ignore-scripts

#Extra STAGE UPload to CDN
ARG BUILD_VERSION
ARG release
ARG AWS_KEY
ARG AWS_SEC
ARG AWS_BUCKET
RUN printf "%s\n%s\neu-west-1\njson" "$AWS_KEY" "$AWS_SEC" | aws configure
#
RUN [ -z $AWS_KEY ] || aws s3 cp /app/dist s3://$AWS_BUCKET --recursive


# Stage 2 - copy the output from the first stage of the build
FROM node:18-alpine as build-deps-stage
RUN apk update && apk add --no-cache supervisor nginx
WORKDIR /app/
COPY --from=build-deps /app/dist/ ./dist/
COPY --from=build-deps /app/node_modules/ ./node_modules/
COPY --from=build-deps /app/package.json ./
COPY --from=build-deps /app/tools/infrastructure/supervisord.conf /app/
COPY --from=build-deps /app/tools/infrastructure/nginx.conf /etc/nginx/
COPY --from=build-deps /app/tools/infrastructure/app.conf /etc/nginx/conf.d/
COPY --from=build-deps /app/tools/seo/robots.txt /app/dist/robots.txt
COPY --from=build-deps /app/.env.messengers /app/.env.messengers
COPY --from=build-deps /app/.env.google-maps /app/.env.google-maps

# Expose port 80
EXPOSE 80
# Start app
CMD ["supervisord", "-c", "/app/supervisord.conf"]
