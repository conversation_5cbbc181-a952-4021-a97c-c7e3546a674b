{"name": "apomeds-frontstore", "version": "0.0.0", "license": "MIT", "scripts": {"start": "npm run build:components && concurrently \"npm run start:components\" \"npm run start:checkout\" \"npm run start:frontend\" \"npm run start:website\"", "build": "npm run build:website && npm run build:components && npm run build:frontend && npm run build:checkout", "start:prod": "node dist/main", "start:website": "nx serve --project=website", "build:website": "nx build --project=website --configuration production", "lint:website": "nx lint website --fix", "start:frontend": "cross-env CONFIG_ENV=dev webpack --config ./tools/website-frontent-webpack/webpack-lib.config.js", "build:frontend": "cross-env CONFIG_ENV=prod webpack --config ./tools/website-frontent-webpack/webpack-lib.config.js", "start:components": "nx serve ui-components", "build:components": "nx build ui-components --configuration production", "start:checkout": "nx run workflow-spa-modules:serve --port 4201", "build:checkout": "nx build --project=workflow-spa-modules --configuration production && cross-env PROJECT=spa ts-node ./tools/generate-assets-list.mjs", "lint:checkout": "nx lint --project=workflow-spa-modules --fix", "lint": "npm run lint:website && npm run lint:checkout", "format": "nx format:write", "format:write": "nx format:write", "format:check": "nx format:check", "version": "ts-node tools/set-version.mjs", "prepare": "husky install", "copy:ui-kit-assets": "node ./apps/workflow-spa-modules/copy-ui-kit-assets.mjs"}, "husky": {"hooks": {"pre-commit": "npm run lint"}}, "private": true, "dependencies": {"@apo/ui-kit": "0.1.47", "@nestjs/axios": "3.1.3", "@nestjs/common": "10.4.9", "@nestjs/config": "3.3.0", "@nestjs/core": "10.4.9", "@nestjs/platform-express": "10.4.9", "@sentry/node": "9.5.0", "@sentry/replay": "7.120.3", "@sentry/tracing": "7.120.3", "@storybook/addon-interactions": "8.6.11", "axios-cache-interceptor": "1.3.3", "cookie-parser": "1.4.7", "hbs": "4.2.0", "http-proxy-middleware": "2.0.7", "ioredis": "5.3.2", "jsdom": "16.5.1", "lodash": "4.17.21", "reflect-metadata": "0.1.14", "rxjs": "7.8.1", "storybook": "8.6.11", "tslib": "2.8.1", "ua-parser-js": "1.0.39"}, "devDependencies": {"@angular-devkit/build-angular": "19.2.5", "@angular-devkit/core": "19.2.5", "@angular-devkit/schematics": "19.2.5", "@angular-eslint/eslint-plugin": "19.3.0", "@angular-eslint/eslint-plugin-template": "19.3.0", "@angular-eslint/template-parser": "19.3.0", "@angular/animations": "19.2.4", "@angular/cdk": "19.2.7", "@angular/cli": "19.2.5", "@angular/common": "19.2.4", "@angular/compiler": "19.2.4", "@angular/compiler-cli": "19.2.4", "@angular/core": "19.2.4", "@angular/forms": "19.2.4", "@angular/language-service": "19.2.4", "@angular/material": "19.2.7", "@angular/platform-browser": "19.2.4", "@angular/platform-browser-dynamic": "19.2.4", "@angular/router": "19.2.4", "@auth0/angular-jwt": "5.0.2", "@nestjs/schematics": "10.2.3", "@nestjs/testing": "10.4.9", "@ngx-translate/core": "17.0.0", "@ngxs/devtools-plugin": "19.0.0", "@ngxs/logger-plugin": "19.0.0", "@ngxs/store": "19.0.0", "@nx/angular": "20.7.0", "@nx/eslint": "20.7.0", "@nx/eslint-plugin": "20.7.0", "@nx/nest": "20.7.0", "@nx/node": "20.7.0", "@nx/storybook": "20.7.0", "@nx/workspace": "20.7.0", "@nxext/stencil": "20.0.6", "@schematics/angular": "19.2.5", "@sentry/angular": "9.5.0", "@stencil/core": "4.7.2", "@stencil/sass": "3.0.12", "@stencil/store": "2.0.16", "@types/express": "4.17.21", "@types/googlemaps": "3.43.3", "@types/hammerjs": "2.0.39", "@types/intl-tel-input": "18.1.4", "@types/jsdom": "21.1.7", "@types/node": "18.19.9", "@types/redis": "2.8.32", "@types/ua-parser-js": "0.7.36", "@typescript-eslint/eslint-plugin": "7.18.0", "@typescript-eslint/parser": "7.18.0", "@typescript-eslint/utils": "^7.16.0", "braintree-web": "3.88.3", "concurrently": "6.3.0", "copy-to-clipboard": "3.3.1", "credit-card-type": "9.1.0", "cross-env": "7.0.3", "dotenv": "10.0.0", "eslint": "8.57.1", "eslint-config-prettier": "10.1.1", "extract-loader": "5.1.0", "husky": "7.0.2", "intl-tel-input": "17.0.12", "jwt-decode": "3.1.2", "moment": "2.29.1", "moment-mini": "2.24.0", "ngx-google-analytics": "14.0.1", "ngx-mask": "19.0.6", "ngx-perfect-scrollbar-portable": "10.1.13", "nx": "20.7.0", "postcss": "8.4.27", "postcss-loader": "7.3.3", "prettier": "2.8.8", "sass-loader": "10.4.1", "terser-webpack-plugin": "5.3.9", "ts-loader": "8.4.0", "ts-node": "10.9.2", "typescript": "5.7.3", "web-animations-js": "2.3.2", "webpack": "5.98.0", "webpack-cli": "5.1.4", "zone.js": "0.15.0"}, "overrides": {"@apo/ui-kit": {"@angular/common": "$@angular/common", "@angular/core": "$@angular/core"}}}