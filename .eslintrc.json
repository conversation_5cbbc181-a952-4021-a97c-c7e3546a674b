{"root": true, "ignorePatterns": ["**/*"], "plugins": ["@nx"], "rules": {"comma-dangle": ["error", "always-multiline"], "quotes": ["error", "single", "avoid-escape"]}, "overrides": [{"files": ["*.ts", "*.tsx", "*.js", "*.jsx"], "rules": {"@nx/enforce-module-boundaries": ["error", {"enforceBuildableLibDependency": true, "allow": [], "depConstraints": [{"sourceTag": "*", "onlyDependOnLibsWithTags": ["*"]}]}]}}, {"files": ["*.ts", "*.tsx"], "extends": ["plugin:@nx/typescript"], "rules": {"@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-unused-vars": "off", "no-unsafe-optional-chaining": 0, "@typescript-eslint/no-extra-semi": "error", "no-extra-semi": "off"}}, {"files": ["*.js", "*.jsx"], "extends": ["plugin:@nx/javascript"], "rules": {"@typescript-eslint/no-extra-semi": "error", "no-extra-semi": "off"}}]}